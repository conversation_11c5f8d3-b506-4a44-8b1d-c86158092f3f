import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '../common';
const Footer = () => {
  return (
    <footer className="bg-dexin-light-20 py-6 sm:py-10">
      <div className="container mx-auto px-4 sm:px-6">
        {/* Phần trên của footer */}
        <div className="flex flex-col md:flex-row md:justify-between md:items-center py-4 space-y-6 md:space-y-0">
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-8 md:space-x-16">
            {/* Logo */}
            <div>
              <img src="/images/logo-dexin-final.png" alt="DEXIN" className="h-7 sm:h-8" />
            </div>

            {/* Main Navigation */}
            <nav className="flex flex-wrap gap-x-6 gap-y-2 sm:space-x-4 md:space-x-8">
              <Link to="/phac-y" className="text-gray-800 hover:text-dexin-primary font-medium">Phác ý</Link>
              <Link to="/mo-loi" className="text-gray-800 hover:text-dexin-primary font-medium">Mở lối</Link>
              <Link to="/ngo-loi" className="text-gray-800 hover:text-dexin-primary font-medium">Ngõ lời</Link>
              <Link to="/chuyen-nha" className="text-gray-800 hover:text-dexin-primary font-medium">Chuyện nhà</Link>
              <Link to="/chung-nhip" className="text-gray-800 hover:text-dexin-primary font-medium">Chung nhịp</Link>
              <Link to="/store" className="text-gray-800 hover:text-dexin-primary font-medium">Cửa hàng</Link>
            </nav>
          </div>

          {/* Newsletter */}
          <div className="w-full md:w-auto">
            <p className="text-gray-800 font-medium mb-3 md:text-right">Cập nhật xu hướng decor mới nhất</p>
            <div className="flex items-center gap-2">
              <input
                type="email"
                placeholder="Nhập địa chỉ email"
                className="px-4 py-2 border border-gray-200 rounded-md w-full sm:w-64 focus:outline-none focus:border-dexin-primary bg-white"
              />
              <Button variant="primary" size="sm" className="whitespace-nowrap px-3 sm:px-4">
                Đăng ký
              </Button>
            </div>
          </div>
        </div>

        <div className="border-t border-dexin-primary my-2 sm:my-0"></div>

        {/* Bottom Links */}
        <div className="flex flex-col md:flex-row justify-between items-center pt-2">
          <p className="text-gray-600 text-sm mb-4 md:mb-0">© 2025 DEXIN. Bảo lưu mọi quyền</p>
          <div className="flex space-x-6">
            <a href="/" className="text-gray-600 hover:text-dexin-primary text-sm">Điều khoản</a>
            <a href="/" className="text-gray-600 hover:text-dexin-primary text-sm">Quyền riêng tư</a>
            <a href="/" className="text-gray-600 hover:text-dexin-primary text-sm">Cookies</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;