import React, { useEffect } from 'react';

// Google Analytics component
const Analytics = () => {
  useEffect(() => {
    // Google Analytics 4 (GA4) tracking code
    const GA_TRACKING_ID = 'G-XXXXXXXXXX'; // Thay thế bằng tracking ID thực tế
    
    // Tạo script tag cho Google Analytics
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;
    document.head.appendChild(script1);

    // Tạo script tag cho cấu hình GA
    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${GA_TRACKING_ID}', {
        page_title: document.title,
        page_location: window.location.href
      });
    `;
    document.head.appendChild(script2);

    // Google Search Console verification (nếu cần)
    const searchConsoleVerification = document.createElement('meta');
    searchConsoleVerification.name = 'google-site-verification';
    searchConsoleVerification.content = 'your-verification-code-here'; // Thay thế bằng mã xác thực thực tế
    document.head.appendChild(searchConsoleVerification);

    // Cleanup function
    return () => {
      // Xóa các script khi component unmount (nếu cần)
      if (script1.parentNode) {
        script1.parentNode.removeChild(script1);
      }
      if (script2.parentNode) {
        script2.parentNode.removeChild(script2);
      }
    };
  }, []);

  return null; // Component này không render gì
};

// Hàm để track events
export const trackEvent = (eventName, parameters = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

// Hàm để track page views
export const trackPageView = (url, title) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-XXXXXXXXXX', {
      page_path: url,
      page_title: title
    });
  }
};

// Hàm để track conversions
export const trackConversion = (conversionId, value = 0, currency = 'VND') => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'conversion', {
      send_to: conversionId,
      value: value,
      currency: currency
    });
  }
};

// Hàm để track ecommerce events
export const trackPurchase = (transactionId, items, value, currency = 'VND') => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'purchase', {
      transaction_id: transactionId,
      value: value,
      currency: currency,
      items: items
    });
  }
};

export const trackAddToCart = (item) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'add_to_cart', {
      currency: 'VND',
      value: item.price,
      items: [item]
    });
  }
};

export const trackViewItem = (item) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'view_item', {
      currency: 'VND',
      value: item.price,
      items: [item]
    });
  }
};

export default Analytics;
