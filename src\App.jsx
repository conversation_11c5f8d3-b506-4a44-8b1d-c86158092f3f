import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Analytics } from '@vercel/analytics/react';
import HomePage from './pages/Home';
import Login from './pages/Login';
import Signup from './pages/Signup';
import <PERSON><PERSON><PERSON> from './pages/MoLoi';
import Chung<PERSON><PERSON> from './pages/ChungNhip';
import ChuyenNha from './pages/ChuyenNha';
import <PERSON><PERSON><PERSON><PERSON> from './pages/NgoLoi';
import PhacY from './pages/PhacY';
import Store from './pages/Store';
import Wishlist from './pages/Wishlist';
import Cart from './pages/Cart';
import ProductDetail from './pages/Store/ProductDetail';
import Account from './pages/Account';

import { LoadingProvider } from './context/LoadingContext';
import { ChatProvider } from './context/ChatContext';
import { WishlistProvider } from './context/WishlistContext';
import { CartProvider } from './context/CartContext';
import { AuthProvider } from './context/AuthContext';
import LoadingRoute from './components/layout/LoadingRoute';
import { initializePerformanceOptimizations } from './utils/performanceUtils';

// Layout chung cho các trang thông thường
import Layout from './components/layout/Layout';

const DefaultLayout = ({ children }) => (
  <Layout>{children}</Layout>
);

function App() {
  // Initialize performance optimizations
  useEffect(() => {
    initializePerformanceOptimizations();
  }, []);

  return (
    <AuthProvider>
      <LoadingProvider>
        <ChatProvider>
          <WishlistProvider>
            <CartProvider>
              <Router>
                <LoadingRoute>
                  <Routes>
                    {/* Trang MoLoi độc lập không có Navbar và Footer */}
                    <Route path="/mo-loi" element={<MoLoi />} />

                    {/* Trang NgoLoi độc lập không có Navbar và Footer */}
                    <Route path="/ngo-loi" element={<NgoLoi />} />

                    {/* Trang PhacY độc lập không có Navbar và Footer */}
                    <Route path="/phac-y" element={<PhacY />} />

                    {/* Các trang khác sử dụng layout mặc định */}
                    <Route path="/" element={<DefaultLayout><HomePage /></DefaultLayout>} />
                    <Route path="/login" element={<DefaultLayout><Login /></DefaultLayout>} />
                    <Route path="/signup" element={<DefaultLayout><Signup /></DefaultLayout>} />
                    <Route path="/chung-nhip" element={<DefaultLayout><ChungNhip /></DefaultLayout>} />
                    <Route path="/chuyen-nha" element={<DefaultLayout><ChuyenNha /></DefaultLayout>} />
                    <Route path="/store" element={<DefaultLayout><Store /></DefaultLayout>} />
                    <Route path="/wishlist" element={<DefaultLayout><Wishlist /></DefaultLayout>} />
                    <Route path="/cart" element={<DefaultLayout><Cart /></DefaultLayout>} />
                    <Route path="/product/:productId" element={<DefaultLayout><ProductDetail /></DefaultLayout>} />
                    <Route path="/account" element={<DefaultLayout><Account /></DefaultLayout>} />

                  </Routes>
                </LoadingRoute>
                {/* Vercel Analytics để theo dõi lượt truy cập */}
                <Analytics />
              </Router>
            </CartProvider>
          </WishlistProvider>
        </ChatProvider>
      </LoadingProvider>
    </AuthProvider>
  );
}

export default App;
