/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  darkMode: 'media',
  theme: {
    extend: {
      colors: {
        'dexin-pink': '#FFC0CB',
        'dexin-primary': '#B90E56',
        'dexin-dark': '#333333',
        'dexin-light': '#FE7CAB',
        'dexin-light-50': '#FEBDD5',
        'dexin-light-20': '#fe7cac41',
        'dexin-light-10': '#fe7cac20',
        'dexin-light-90': '#f35b90',
        'dexin-button': '#FE7CAB',
        'dexin-bg': '#FDF2F8',
        'dexin-sidebar': '#FFD8E6',
        'dexin-chat': '#FFD8E6',
        'dexin-chat-second': '#ffe5eed0',
        'dexin-gold': '#FFB800',
        'dexin-silver': '#73C2FF',
        'dexin-bronze': '#CD7F32',
      },
      fontFamily: {
        sans: ['BDLifelessGrotesk', 'sans-serif'],
        dexin: ['BDLifelessGrotesk', 'sans-serif'],
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: 0, transform: 'translateY(-10px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' }
        },
        scaleIn: {
          '0%': { opacity: 0, transform: 'scale(0.95)' },
          '100%': { opacity: 1, transform: 'scale(1)' }
        },
        pulse: {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.7 }
        },
        heartBeat: {
          '0%': { transform: 'scale(1)' },
          '14%': { transform: 'scale(1.3)' },
          '28%': { transform: 'scale(1)' },
          '42%': { transform: 'scale(1.3)' },
          '70%': { transform: 'scale(1)' }
        }
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-out forwards',
        scaleIn: 'scaleIn 0.4s ease-out forwards',
        pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        heartBeat: 'heartBeat 1s ease-in-out'
      },
      boxShadow: {
        'dexin': '0px 8px 15px rgba(254, 124, 171, 0.3)'
      }
    },
  },
  variants: {
    extend: {
      borderWidth: ['hover', 'focus'],
      borderColor: ['hover', 'focus']
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('tailwind-scrollbar'),
  ],
}