import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { faUser, faKey } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { PrivacyPolicyModal } from '../components/common/PolicyModals';
import { motion, AnimatePresence } from 'motion/react';
import { useAuth } from '../context/AuthContext';
import { toast } from 'react-toastify';

const ForgotPasswordModal = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    // G<PERSON>i yêu cầu đặt lại mật khẩu
    console.log('Gửi yêu cầu đặt lại mật khẩu cho email:', email);
    setIsSubmitted(true);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div 
          className="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div 
            className="relative bg-white rounded-xl shadow-2xl max-w-md w-full"
            initial={{ scale: 0.9, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            <div className="p-5 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">Quên mật khẩu</h2>
              <motion.button 
                onClick={onClose}
                className="bg-gray-100 hover:bg-gray-200 rounded-full p-2 transition-colors"
                whileHover={{ scale: 1.1, backgroundColor: "#f3f4f6" }}
                whileTap={{ scale: 0.95 }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </motion.button>
            </div>

            <div className="p-5">
              {isSubmitted ? (
                <motion.div 
                  className="text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.div 
                    className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4"
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ type: "spring", damping: 15, stiffness: 200 }}
                  >
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </motion.div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Email đã được gửi!</h3>
                  <p className="text-gray-600 mb-4">
                    Chúng tôi đã gửi email hướng dẫn đặt lại mật khẩu đến <span className="font-medium">{email}</span>. 
                    Vui lòng kiểm tra hộp thư của bạn.
                  </p>
                  <motion.button
                    onClick={onClose}
                    className="w-full bg-dexin-primary text-white py-2 rounded-lg hover:bg-dexin-primary/90 transition"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Đóng
                  </motion.button>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <p className="text-gray-600 mb-4">
                    Vui lòng nhập địa chỉ email của bạn. Chúng tôi sẽ gửi cho bạn một liên kết để đặt lại mật khẩu.
                  </p>
                  <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Địa chỉ email
                      </label>
                      <motion.input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary focus:border-transparent"
                        placeholder="Nhập địa chỉ email của bạn"
                        whileFocus={{ borderColor: "#B90E56" }}
                      />
                    </div>
                    <div className="flex justify-end space-x-3">
                      <motion.button
                        type="button"
                        onClick={onClose}
                        className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition"
                        whileHover={{ scale: 1.03, backgroundColor: "#f9fafb" }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Hủy
                      </motion.button>
                      <motion.button
                        type="submit"
                        className="px-4 py-2 bg-dexin-primary text-white rounded-lg hover:bg-dexin-primary/90 transition"
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Gửi
                      </motion.button>
                    </div>
                  </form>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!username.trim() || !password.trim()) {
      toast.error('Vui lòng nhập đầy đủ thông tin');
      return;
    }

    setIsLoading(true);

    try {
      const result = await login({
        username: username.trim(),
        password: password
      });

      if (result.success) {
        toast.success(result.message || 'Đăng nhập thành công!');
        // Redirect về trang chủ sau 1 giây
        setTimeout(() => {
          navigate('/');
        }, 1000);
      } else {
        toast.error(result.message || 'Đăng nhập thất bại');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Có lỗi xảy ra. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div 
      className="min-h-screen flex bg-gradient-to-r from-pink-50 to-white"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Phần bên trái - Hình ảnh màu hồng */}
      <motion.div 
        className="hidden md:flex md:w-1/2 bg-pink-100 items-center justify-center"
        initial={{ opacity: 0, x: -30 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.7, delay: 0.1 }}
      >
        <div className="max-w-md px-8">
          <motion.h1 
            className="text-4xl font-bold text-dexin-primary mb-6"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            DEXIN
          </motion.h1>
          <motion.p 
            className="text-xl text-gray-700 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Nơi bạn tìm thấy không gian lý tưởng cho cuộc sống của mình. Đăng nhập để khám phá những ý tưởng trang trí nhà cửa tuyệt vời.
          </motion.p>
          <motion.div 
            className="bg-dexin-bg p-6 rounded-xl backdrop-blur-sm shadow-lg"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            whileHover={{ scale: 1.03, boxShadow: "0 10px 25px rgba(254, 124, 171, 0.2)" }}
          >
            <p className="text-gray-700 italic">
              "Ngôi nhà không chỉ là nơi ở, mà còn là nơi phản ánh cá tính và phong cách của bạn."
            </p>
          </motion.div>
        </div>
      </motion.div>

      {/* Phần bên phải - Form đăng nhập */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-8">
        <motion.div 
          className="bg-white p-8 rounded-2xl shadow-xl w-full max-w-md border border-pink-100"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          whileHover={{ boxShadow: "0 15px 30px rgba(254, 124, 171, 0.15)" }}
        >
          <div className="text-center mb-8">
            <motion.h2 
              className="text-4xl font-bold text-dexin-primary mb-2"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              Đăng nhập
            </motion.h2>
            <motion.div 
              className="w-16 h-1 bg-dexin-primary mx-auto rounded-full mb-4"
              initial={{ width: 0 }}
              animate={{ width: 64 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            ></motion.div>
            <motion.p 
              className="text-gray-600"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              Bạn chưa có tài khoản? <Link to="/signup" className="text-dexin-primary font-bold hover:underline transition">Đăng ký</Link>
            </motion.p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="mb-2 text-sm font-medium text-gray-700">Tên người dùng hoặc Email</div>
              <motion.div 
                className="relative"
                whileHover={{ boxShadow: "0 2px 4px rgba(254, 124, 171, 0.1)" }}
              >
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400 z-10">
                  <FontAwesomeIcon icon={faUser} />
                </div>
                <input 
                  type="text" 
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary focus:border-transparent transition"
                  placeholder="Nhập email hoặc tên người dùng"
                  disabled={isLoading}
                />
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Mật khẩu</span>
                <motion.button 
                  type="button" 
                  onClick={() => setShowForgotPassword(true)}
                  className="text-sm text-dexin-primary hover:underline focus:outline-none"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  disabled={isLoading}
                >
                  Quên mật khẩu?
                </motion.button>
              </div>
              <motion.div 
                className="relative"
                whileHover={{ boxShadow: "0 2px 4px rgba(254, 124, 171, 0.1)" }}
              >
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400 z-10">
                  <FontAwesomeIcon icon={faKey} />
                </div>
                <input 
                  type={showPassword ? "text" : "password"} 
                  placeholder="Nhập mật khẩu của bạn"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-dexin-primary focus:border-transparent transition"
                  disabled={isLoading}
                />
                <motion.button 
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 z-10"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                      <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                    </svg>
                  )}
                </motion.button>
              </motion.div>
            </motion.div>

            <motion.div 
              className="flex items-center"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <input 
                type="checkbox" 
                id="remember" 
                className="h-4 w-4 text-dexin-primary border-gray-300 rounded focus:ring-dexin-primary"
                disabled={isLoading}
              />
              <label htmlFor="remember" className="ml-2 block text-sm text-gray-700">
                Ghi nhớ đăng nhập
              </label>
            </motion.div>

            <motion.button 
              type="submit" 
              disabled={isLoading}
              className={`w-full text-white py-3 rounded-full font-medium shadow-lg hover:shadow-xl transition duration-300 transform hover:-translate-y-0.5 ${
                isLoading 
                  ? 'bg-gray-400 cursor-not-allowed' 
                  : 'bg-dexin-light hover:bg-dexin-light/90'
              }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              whileHover={!isLoading ? { scale: 1.03, boxShadow: "0 10px 20px rgba(254, 124, 171, 0.3)" } : {}}
              whileTap={!isLoading ? { scale: 0.97 } : {}}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang đăng nhập...
                </div>
              ) : (
                'Đăng nhập'
              )}
            </motion.button>
          </form>

          <motion.div 
            className="mt-6 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <p className="text-xs text-gray-500">
              Bằng cách đăng nhập, bạn đồng ý với{' '}
              <motion.button 
                type="button" 
                onClick={() => setShowPrivacyPolicy(true)}
                className="text-dexin-primary hover:underline focus:outline-none"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={isLoading}
              >
                Chính sách bảo mật
              </motion.button>{' '}
              của chúng tôi
            </p>
          </motion.div>
        </motion.div>
      </div>

      {/* Modal quên mật khẩu */}
      <ForgotPasswordModal 
        isOpen={showForgotPassword} 
        onClose={() => setShowForgotPassword(false)} 
      />

      {/* Modal chính sách bảo mật */}
      <PrivacyPolicyModal 
        isOpen={showPrivacyPolicy} 
        onClose={() => setShowPrivacyPolicy(false)} 
      />
    </motion.div>
  );
};

export default Login; 