import React from 'react';
import { Search, Box } from 'lucide-react';
import { motion } from 'motion/react';
import { <PERSON> } from 'react-router-dom';

const LogoHeader = ({ onStartChat }) => {
  return (
    <div className="pt-2 sm:pt-3 px-3 sm:px-4 flex items-center justify-between">
      <Link to="/">
        <motion.img
          src="/images/logo-mini.png"
          alt="DEXIN"
          className="h-10 sm:h-14 object-contain"
          whileHover={{ scale: 1.05 }}
        />
      </Link>
      <div className="flex items-center">
        <motion.div
          className="bg-white shadow-md rounded-xl w-10 h-10 sm:w-14 sm:h-14 flex items-center justify-center cursor-pointer"
          whileHover={{
            scale: 1.05,
            boxShadow: '0px 8px 15px rgba(229, 138, 138, 0.53)',
          }}
          whileTap={{ scale: 0.95 }}
          initial={{
            boxShadow: '0 1px 3px rgba(197, 34, 34, 0.12), 0 1px 2px rgba(197, 34, 34, 0.24)',
          }}
          animate={{
            boxShadow: '0 1px 3px rgba(197, 34, 34, 0.12), 0 1px 2px rgba(197, 34, 34, 0.24)',
          }}
        >
          <button
            onClick={onStartChat}
            className="w-full h-full flex items-center justify-center"
          >
            <motion.div
              whileHover={{ rotate: 15, scale: 1.1 }}
            >
              <Box className="h-5 w-5 sm:h-6 sm:w-6 text-dexin-primary" strokeWidth={2} />
            </motion.div>
          </button>
        </motion.div>
      </div>
    </div>
  );
};

const SearchBar = () => (
  <div className="px-3 sm:px-4 py-2 mb-2 sm:mb-3">
    <div className="relative flex items-center">
      <Search className="absolute left-3 sm:left-4 h-4 w-4 sm:h-5 sm:w-5 text-dark pointer-events-none" />
      <input
        type="text"
        placeholder="Tìm tên"
        className="w-full bg-dexin-light-50 rounded-full py-2 sm:py-3 pl-10 sm:pl-12 pr-3 sm:pr-4 text-sm text-gray-900 focus:outline-none"
      />
    </div>
  </div>
);

const EmptyState = () => (
  <div className="flex-grow flex flex-col items-center justify-center p-4 sm:p-6 text-center">
    <h3 className="text-lg sm:text-xl font-bold mb-1 sm:mb-2 text-black">DEXIN phát hiện</h3>
    <p className="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8 flex items-center">
      bạn chưa có tin nhắn nào <span className="text-xl sm:text-2xl ml-1">🤔</span>
    </p>
    <p className="text-sm sm:text-base text-dexin-primary font-medium">
      Bấm "Trò chuyện ngay"
      <br />
      để tám với chúng mình thuii!
    </p>
  </div>
);

const ConversationItem = ({ conversation, onSelectConversation }) => {
  return (
    <motion.div
      className="conversation-item flex items-start py-2 sm:py-2.5 px-3 sm:px-4 cursor-pointer bg-dexin-chat-second rounded-xl mb-2"
      onClick={() => onSelectConversation(conversation)}
      whileHover={{
        scale: 1.01,
        backgroundColor: 'rgba(255, 234, 242, 0.5)',
      }}
      transition={{ duration: 0.2 }}
    >
      <div className="relative mr-2 sm:mr-3 w-12 h-12 sm:w-14 sm:h-14 flex-shrink-0">
        <img
          src={conversation.avatar || "/images/Avatar.png"}
          alt={conversation.name}
          className="w-full h-full rounded-full object-cover"
        />
      </div>
      <div className="flex-1 overflow-hidden pt-1 sm:pt-1.5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 max-w-[85%]">
            <h3 className="font-medium text-black text-sm sm:text-base truncate">
              {conversation.name}
            </h3>
            <span className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-rose-600 rounded-full block flex-shrink-0" />
          </div>
        </div>
        <div className="flex items-center mt-0.5 sm:mt-1">
          <motion.span
            className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full block mr-1.5 flex-shrink-0 shadow-lg"
            animate={{
              scale: [1, 1.2, 1],
              boxShadow: [
                '0 0 0 0 rgba(34, 197, 94, 0.7)',
                '0 0 0 4px rgba(34, 197, 94, 0)',
                '0 0 0 0 rgba(34, 197, 94, 0)'
              ]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <p className="text-xs sm:text-sm text-gray-600 truncate">{conversation.status}</p>
        </div>
      </div>
    </motion.div>
  );
};

const ConversationList = ({ conversations, onSelectConversation }) => (
  <div className="overflow-y-auto flex-grow">
    <div className="px-3 sm:px-4">
      <div className="flex items-center justify-between mb-2 sm:mb-3">
        <span className="text-xs sm:text-sm font-medium text-gray-600">Gần đây</span>
        <button className="text-xs text-dexin-primary font-medium">Xem tất cả</button>
      </div>
      {conversations.map((conversation) => (
        <ConversationItem
          key={conversation.id}
          conversation={conversation}
          onSelectConversation={onSelectConversation}
        />
      ))}
    </div>
  </div>
);

const ChatSidebar = ({
  isEmpty = false,
  conversations = [],
  selectedConversation = null,
  onSelectConversation,
  onStartChat,
  className = '',
}) => {
  return (
    <div className={`flex flex-col bg-white border-r border-dexin-light h-full ${className}`}>
      <LogoHeader onStartChat={onStartChat} />
      <div className="px-3 sm:px-4 pt-3 sm:pt-6 pb-2 sm:pb-3">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Tin nhắn</h1>
      </div>
      <SearchBar />
      {isEmpty ? <EmptyState /> : <ConversationList conversations={conversations} onSelectConversation={onSelectConversation} />}
    </div>
  );
};

export default ChatSidebar;
