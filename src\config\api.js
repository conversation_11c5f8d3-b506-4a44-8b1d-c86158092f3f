import axios from 'axios';

// Lấy API base URL từ environment variables, fallback về localhost nếu không có
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Tạo axios instance với cấu hình mặc định
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Add security headers
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Helper để kiểm tra xem có nên log debug không
const shouldLogDebug = () => {
  return import.meta.env.VITE_DEBUG === 'true' && import.meta.env.MODE === 'development';
};

// Request interceptor - C<PERSON> thể thêm token authentication sau này
apiClient.interceptors.request.use(
  (config) => {
    // Log request trong development mode only
    if (shouldLogDebug()) {
      console.log('API Request:', {
        method: config.method,
        url: config.url,
        params: config.params,
        // Don't log sensitive data
        dataLength: config.data ? JSON.stringify(config.data).length : 0,
      });
    }
    
    return config;
  },
  (error) => {
    if (shouldLogDebug()) {
      console.error('Request Error:', error);
    }
    return Promise.reject(error);
  }
);

// Response interceptor - Xử lý response và errors
apiClient.interceptors.response.use(
  (response) => {
    // Log response trong development mode only
    if (shouldLogDebug()) {
      console.log('API Response:', {
        status: response.status,
        url: response.config.url,
        dataLength: response.data ? JSON.stringify(response.data).length : 0,
        // Don't log actual data for security
      });
    }
    
    return response;
  },
  (error) => {
    // Xử lý các loại lỗi khác nhau
    let errorMessage = 'Có lỗi xảy ra khi kết nối với server';
    
    if (error.response) {
      // Server đã phản hồi với status code không thành công
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          errorMessage = data.message || 'Yêu cầu không hợp lệ';
          break;
        case 401:
          errorMessage = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại';
          // Có thể redirect về trang login ở đây
          break;
        case 403:
          errorMessage = 'Bạn không có quyền thực hiện hành động này';
          break;
        case 404:
          errorMessage = 'Không tìm thấy dữ liệu yêu cầu';
          break;
        case 422:
          errorMessage = data.message || 'Dữ liệu đầu vào không hợp lệ';
          break;
        case 429:
          errorMessage = 'Quá nhiều yêu cầu. Vui lòng thử lại sau';
          break;
        case 500:
          errorMessage = 'Lỗi server. Vui lòng thử lại sau';
          break;
        default:
          errorMessage = data.message || `Lỗi ${status}: ${error.response.statusText}`;
      }
      
      error.message = errorMessage;
    } else if (error.request) {
      // Không nhận được phản hồi từ server
      errorMessage = 'Không thể kết nối với server. Vui lòng kiểm tra kết nối mạng';
      error.message = errorMessage;
    } else {
      // Lỗi khác trong quá trình setup request
      errorMessage = error.message || 'Có lỗi xảy ra khi gửi yêu cầu';
    }
    
    // Only log in development
    if (shouldLogDebug()) {
      console.error('API Error:', {
        message: errorMessage,
        status: error.response?.status,
        url: error.config?.url,
        method: error.config?.method,
      });
    }
    
    return Promise.reject(error);
  }
);

// Export axios instance và utilities
export { apiClient, API_BASE_URL };

// Export default
export default apiClient; 