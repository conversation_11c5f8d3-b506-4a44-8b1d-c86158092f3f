// DEXIN Service Worker - Optimized
const CACHE_NAME = 'dexin-cache-v2';
const STATIC_CACHE_NAME = 'dexin-static-v2';
const DYNAMIC_CACHE_NAME = 'dexin-dynamic-v2';

// Essential files to cache
const urlsToCache = [
  '/',
  '/manifest.json',
  '/images/logo-mini.png',
  '/images/logo-dexin-final.png'
];

// Install event - Improved with better error handling
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        return cache.addAll(urlsToCache).catch((error) => {
          // Silent fail - don't break installation
          return Promise.resolve();
        });
      }),
      self.skipWaiting() // Force activation
    ])
  );
});

// Activate event - Clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    Promise.all([
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME &&
                cacheName !== DYNAMIC_CACHE_NAME) {
              return caches.delete(cacheName);
            }
          })
        );
      }),
      self.clients.claim() // Take control immediately
    ])
  );
});

// Fetch event - Improved strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip external requests
  if (url.origin !== location.origin) {
    return;
  }

  event.respondWith(
    caches.match(request).then((cachedResponse) => {
      if (cachedResponse) {
        return cachedResponse;
      }

      return fetch(request).then((response) => {
        // Don't cache non-successful responses
        if (!response || response.status !== 200 || response.type !== 'basic') {
          return response;
        }

        // Cache images and static assets
        if (request.destination === 'image' ||
            request.url.includes('/images/') ||
            request.url.includes('.png') ||
            request.url.includes('.jpg') ||
            request.url.includes('.jpeg') ||
            request.url.includes('.svg')) {

          const responseClone = response.clone();
          caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
            cache.put(request, responseClone);
          });
        }

        return response;
      }).catch(() => {
        // Return fallback for navigation requests
        if (request.mode === 'navigate') {
          return caches.match('/').then((fallback) => {
            return fallback || new Response('Offline', {
              status: 200,
              headers: { 'Content-Type': 'text/html' }
            });
          });
        }

        // Return generic offline response for other requests
        return new Response('Offline', { status: 503 });
      });
    })
  );
});

// Message event - Handle communication with main thread
self.addEventListener('message', (event) => {
  // Prevent the async response error
  const { data } = event;

  if (data && data.type === 'SKIP_WAITING') {
    self.skipWaiting();
    event.ports[0]?.postMessage({ success: true });
    return;
  }

  if (data && data.type === 'GET_VERSION') {
    event.ports[0]?.postMessage({ version: CACHE_NAME });
    return;
  }

  // Always respond to prevent hanging promises
  event.ports[0]?.postMessage({ received: true });
});

// Background sync for better offline experience
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync tasks
      Promise.resolve()
    );
  }
});