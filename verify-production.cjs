#!/usr/bin/env node

/**
 * Script kiểm tra production build đã khắc phục các lỗi
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Kiểm tra production build...\n');

// Danh sách các file quan trọng cần kiểm tra
const criticalFiles = [
  'build/index.html',
  'build/manifest.json',
  'build/favicon.ico',
  'build/images/logo-mini.png',
  'build/images/logo-dexin-final.png',
  'build/images/hometile-hero.jpg',
  'build/.htaccess',
  'build/web.config',
  'build/_redirects'
];

let allFilesExist = true;

console.log('📁 Kiểm tra các file quan trọng:');
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - KHÔNG TỒN TẠI`);
    allFilesExist = false;
  }
});

// Kiểm tra manifest.json
console.log('\n📋 Kiểm tra manifest.json:');
try {
  const manifest = JSON.parse(fs.readFileSync('build/manifest.json', 'utf8'));
  
  // Kiểm tra icon paths
  const hasSpacesInIcons = manifest.icons.some(icon => 
    icon.src.includes('%20') || icon.src.includes(' ')
  );
  
  if (hasSpacesInIcons) {
    console.log('❌ Manifest icons vẫn có khoảng trắng trong URL');
  } else {
    console.log('✅ Manifest icons không có khoảng trắng');
  }
  
  // Kiểm tra screenshot paths
  if (manifest.screenshots) {
    const hasSpacesInScreenshots = manifest.screenshots.some(screenshot => 
      screenshot.src.includes('%20') || screenshot.src.includes(' ')
    );
    
    if (hasSpacesInScreenshots) {
      console.log('❌ Manifest screenshots vẫn có khoảng trắng trong URL');
    } else {
      console.log('✅ Manifest screenshots không có khoảng trắng');
    }
  }
  
} catch (error) {
  console.log('❌ Lỗi đọc manifest.json:', error.message);
  allFilesExist = false;
}

// Kiểm tra server config files
console.log('\n⚙️ Kiểm tra server configuration:');

// Kiểm tra .htaccess
if (fs.existsSync('build/.htaccess')) {
  const htaccessContent = fs.readFileSync('build/.htaccess', 'utf8');
  if (htaccessContent.includes('application/javascript')) {
    console.log('✅ .htaccess có cấu hình MIME type đúng');
  } else {
    console.log('❌ .htaccess thiếu cấu hình MIME type');
  }
}

// Kiểm tra web.config
if (fs.existsSync('build/web.config')) {
  const webConfigContent = fs.readFileSync('build/web.config', 'utf8');
  if (webConfigContent.includes('application/javascript')) {
    console.log('✅ web.config có cấu hình MIME type đúng');
  } else {
    console.log('❌ web.config thiếu cấu hình MIME type');
  }
}

console.log('\n📊 Tổng kết:');
if (allFilesExist) {
  console.log('🎉 Tất cả các lỗi đã được khắc phục!');
  console.log('✅ MIME type errors - FIXED');
  console.log('✅ Manifest icon errors - FIXED');
  console.log('✅ File paths with spaces - FIXED');
  console.log('\n🚀 Production build sẵn sàng deploy!');
} else {
  console.log('❌ Vẫn còn một số vấn đề cần khắc phục');
  process.exit(1);
}
