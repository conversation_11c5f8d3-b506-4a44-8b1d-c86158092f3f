import apiClient from '../config/api';

// Helper function để chuẩn hóa user data response
const formatUserResponse = (userData) => ({
  id: userData.id,
  firstName: userData.firstName,
  lastName: userData.lastName,
  userName: userData.userName,
  email: userData.email,
  phone: userData.phone,
  gender: userData.gender,
  avatar: userData.avatar,
  role: userData.role
});

// Helper function để validate email
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Helper function để validate phone
const isValidPhone = (phone) => {
  if (!phone) return true; // Phone is optional
  const phoneRegex = /^[0-9+\-\s()]{10,}$/;
  return phoneRegex.test(phone);
};

// Utility function để tạo cache key
const createCacheKey = (type, value) => `${type}_${value.toLowerCase()}`;

// Simple in-memory cache để tránh repeated API calls
const userCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

// Cache utility functions
const setCacheItem = (key, data) => {
  userCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl: CACHE_TTL
  });
};

const getCacheItem = (key) => {
  const cached = userCache.get(key);
  if (!cached) return null;
  
  if (Date.now() - cached.timestamp > cached.ttl) {
    userCache.delete(key);
    return null;
  }
  
  return cached.data;
};

// Service để xử lý authentication với MockAPI
export const authService = {
  // Đăng ký user mới
  async register(userData) {
    // Input validation
    if (!userData.firstName?.trim()) {
      return { success: false, message: 'Họ không được để trống' };
    }
    if (!userData.lastName?.trim()) {
      return { success: false, message: 'Tên không được để trống' };
    }
    if (!userData.username?.trim()) {
      return { success: false, message: 'Tên người dùng không được để trống' };
    }
    if (!userData.email?.trim() || !isValidEmail(userData.email)) {
      return { success: false, message: 'Email không hợp lệ' };
    }
    if (!userData.password || userData.password.length < 6) {
      return { success: false, message: 'Mật khẩu phải có ít nhất 6 ký tự' };
    }
    if (userData.phone && !isValidPhone(userData.phone)) {
      return { success: false, message: 'Số điện thoại không hợp lệ' };
    }

    try {
      // Check if email exists - sử dụng query parameter
      const emailExists = await this.checkEmailExists(userData.email);
      if (emailExists) {
        return { success: false, message: 'Email này đã được sử dụng' };
      }

      // Check if username exists - sử dụng query parameter  
      const usernameExists = await this.checkUsernameExists(userData.username);
      if (usernameExists) {
        return { success: false, message: 'Tên người dùng này đã được sử dụng' };
      }

      // Create new user via API
      const requestData = {
        firstName: userData.firstName.trim(),
        lastName: userData.lastName.trim(),
        userName: userData.username.trim(),
        email: userData.email.toLowerCase().trim(),
        phone: userData.phone?.trim() || '',
        gender: userData.gender || 'other',
        password: userData.password,
        avatar: userData.avatar || 'default_avatar.png',
        role: 'user'
      };

      const response = await apiClient.post('/auth/user', requestData);
      
      // Cache the new user
      const user = response.data;
      setCacheItem(createCacheKey('email', user.email), user);
      setCacheItem(createCacheKey('username', user.userName), user);
      setCacheItem(createCacheKey('id', user.id), user);
      
      return {
        success: true,
        user: formatUserResponse(response.data),
        message: 'Đăng ký thành công!'
      };
    } catch (error) {
      console.error('Registration error:', error);
      
      if (error.response?.status === 422) {
        return { success: false, message: 'Dữ liệu đăng ký không hợp lệ' };
      }
      
      return {
        success: false,
        message: error.message || 'Có lỗi xảy ra khi đăng ký'
      };
    }
  },

  // SMART LOGIN - Sử dụng MockAPI query parameters
  async login(loginData) {
    // Input validation
    if (!loginData.identifier?.trim()) {
      return { success: false, message: 'Email hoặc tên người dùng không được để trống' };
    }
    if (!loginData.password) {
      return { success: false, message: 'Mật khẩu không được để trống' };
    }

    const identifier = loginData.identifier.toLowerCase().trim();

    try {
      let foundUser = null;

      // Determine if identifier is email or username
      if (isValidEmail(identifier)) {
        // Search by email using query parameter
        console.log(`🔍 Searching user by email: ${identifier}`);
        
        // Check cache first
        const cacheKey = createCacheKey('email', identifier);
        const cachedUser = getCacheItem(cacheKey);
        
        if (cachedUser) {
          console.log('✅ Found user in cache');
          foundUser = cachedUser;
        } else {
          // Single API call with email filter
          const response = await apiClient.get(`/auth/user?email=${encodeURIComponent(identifier)}`);
          
          if (response.data && response.data.length > 0) {
            foundUser = response.data[0]; // MockAPI returns array when filtering
            setCacheItem(cacheKey, foundUser); // Cache the user
            console.log('✅ Found user via API email search');
          }
        }
      } else {
        // Search by username using query parameter
        console.log(`🔍 Searching user by username: ${identifier}`);
        
        // Check cache first
        const cacheKey = createCacheKey('username', identifier);
        const cachedUser = getCacheItem(cacheKey);
        
        if (cachedUser) {
          console.log('✅ Found user in cache');
          foundUser = cachedUser;
        } else {
          // Single API call with username filter
          const response = await apiClient.get(`/auth/user?userName=${encodeURIComponent(identifier)}`);
          
          if (response.data && response.data.length > 0) {
            foundUser = response.data[0]; // MockAPI returns array when filtering
            setCacheItem(cacheKey, foundUser); // Cache the user
            console.log('✅ Found user via API username search');
          }
        }
      }

      if (!foundUser) {
        return {
          success: false,
          message: 'Email/Tên người dùng không tồn tại'
        };
      }

      // Verify password
      if (foundUser.password !== loginData.password) {
        return {
          success: false,
          message: 'Mật khẩu không đúng'
        };
      }

      // Cache user by all identifiers for future quick access
      setCacheItem(createCacheKey('email', foundUser.email), foundUser);
      setCacheItem(createCacheKey('username', foundUser.userName), foundUser);
      setCacheItem(createCacheKey('id', foundUser.id), foundUser);

      console.log('✅ Login successful');
      return {
        success: true,
        user: formatUserResponse(foundUser),
        message: 'Đăng nhập thành công!'
      };
    } catch (error) {
      console.error('Login error:', error);
      
      // Handle specific MockAPI errors
      if (error.response?.status === 404) {
        return {
          success: false,
          message: 'Email/Tên người dùng không tồn tại'
        };
      }
      
      return {
        success: false,
        message: 'Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại sau.'
      };
    }
  },

  // Check username exists - sử dụng query parameter
  async checkUsernameExists(username) {
    if (!username?.trim()) return false;
    
    const identifier = username.toLowerCase().trim();
    const cacheKey = createCacheKey('username', identifier);
    
    try {
      // Check cache first
      const cachedUser = getCacheItem(cacheKey);
      if (cachedUser) {
        return true;
      }

      // Single API call with username filter
      const response = await apiClient.get(`/auth/user?userName=${encodeURIComponent(identifier)}`);
      
      if (response.data && response.data.length > 0) {
        // Cache the found user
        setCacheItem(cacheKey, response.data[0]);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Check username error:', error);
      return false;
    }
  },

  // Check email exists - sử dụng query parameter
  async checkEmailExists(email) {
    if (!email?.trim()) return false;
    
    const identifier = email.toLowerCase().trim();
    const cacheKey = createCacheKey('email', identifier);
    
    try {
      // Check cache first
      const cachedUser = getCacheItem(cacheKey);
      if (cachedUser) {
        return true;
      }

      // Single API call with email filter
      const response = await apiClient.get(`/auth/user?email=${encodeURIComponent(identifier)}`);
      
      if (response.data && response.data.length > 0) {
        // Cache the found user
        setCacheItem(cacheKey, response.data[0]);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Check email error:', error);
      return false;
    }
  },

  // Update user - sử dụng API
  async updateUser(userId, userData) {
    if (!userId) {
      return { success: false, message: 'ID người dùng không hợp lệ' };
    }
    
    // Validation
    if (userData.firstName !== undefined && !userData.firstName?.trim()) {
      return { success: false, message: 'Họ không được để trống' };
    }
    if (userData.lastName !== undefined && !userData.lastName?.trim()) {
      return { success: false, message: 'Tên không được để trống' };
    }
    if (userData.email !== undefined && (!userData.email?.trim() || !isValidEmail(userData.email))) {
      return { success: false, message: 'Email không hợp lệ' };
    }
    if (userData.phone !== undefined && userData.phone && !isValidPhone(userData.phone)) {
      return { success: false, message: 'Số điện thoại không hợp lệ' };
    }

    try {
      // Prepare update data
      const updateData = { ...userData };
      if (updateData.firstName) updateData.firstName = updateData.firstName.trim();
      if (updateData.lastName) updateData.lastName = updateData.lastName.trim();
      if (updateData.email) updateData.email = updateData.email.toLowerCase().trim();
      if (updateData.phone) updateData.phone = updateData.phone.trim();

      const response = await apiClient.put(`/auth/user/${userId}`, updateData);
      
      // Update cache with new data
      const updatedUser = response.data;
      setCacheItem(createCacheKey('email', updatedUser.email), updatedUser);
      setCacheItem(createCacheKey('username', updatedUser.userName), updatedUser);
      setCacheItem(createCacheKey('id', updatedUser.id), updatedUser);

      return {
        success: true,
        user: formatUserResponse(response.data),
        message: 'Cập nhật thông tin thành công!'
      };
    } catch (error) {
      console.error('Update user error:', error);
      
      if (error.response?.status === 404) {
        return { success: false, message: 'Không tìm thấy người dùng' };
      }
      if (error.response?.status === 422) {
        return { success: false, message: 'Dữ liệu cập nhật không hợp lệ' };
      }
      
      return {
        success: false,
        message: error.message || 'Có lỗi xảy ra khi cập nhật thông tin'
      };
    }
  },

  // Get user by ID
  async getUserById(userId) {
    if (!userId) {
      return { success: false, message: 'ID người dùng không hợp lệ' };
    }

    try {
      // Check cache first
      const cacheKey = createCacheKey('id', userId);
      const cachedUser = getCacheItem(cacheKey);
      
      if (cachedUser) {
        return {
          success: true,
          user: formatUserResponse(cachedUser)
        };
      }

      // Single API call by ID
      const response = await apiClient.get(`/auth/user/${userId}`);
      
      // Cache the user
      const user = response.data;
      setCacheItem(createCacheKey('email', user.email), user);
      setCacheItem(createCacheKey('username', user.userName), user);
      setCacheItem(createCacheKey('id', user.id), user);

      return {
        success: true,
        user: formatUserResponse(response.data)
      };
    } catch (error) {
      console.error('Get user by ID error:', error);
      
      if (error.response?.status === 404) {
        return { success: false, message: 'Không tìm thấy người dùng' };
      }
      
      return {
        success: false,
        message: error.message || 'Không thể lấy thông tin người dùng'
      };
    }
  },

  // Change password
  async changePassword(userId, currentPassword, newPassword) {
    if (!userId) {
      return { success: false, message: 'ID người dùng không hợp lệ' };
    }
    if (!currentPassword) {
      return { success: false, message: 'Mật khẩu hiện tại không được để trống' };
    }
    if (!newPassword || newPassword.length < 6) {
      return { success: false, message: 'Mật khẩu mới phải có ít nhất 6 ký tự' };
    }
    if (currentPassword === newPassword) {
      return { success: false, message: 'Mật khẩu mới phải khác mật khẩu hiện tại' };
    }

    try {
      // Get current user to verify password
      const userResponse = await this.getUserById(userId);
      if (!userResponse.success) {
        return userResponse;
      }

      // Get full user data to verify current password
      const userDataResponse = await apiClient.get(`/auth/user/${userId}`);
      const user = userDataResponse.data;

      if (user.password !== currentPassword) {
        return {
          success: false,
          message: 'Mật khẩu hiện tại không đúng'
        };
      }

      // Update password
      await apiClient.put(`/auth/user/${userId}`, {
        ...user,
        password: newPassword
      });

      // Update cache with new password
      const updatedUser = { ...user, password: newPassword };
      setCacheItem(createCacheKey('email', updatedUser.email), updatedUser);
      setCacheItem(createCacheKey('username', updatedUser.userName), updatedUser);
      setCacheItem(createCacheKey('id', updatedUser.id), updatedUser);

      return {
        success: true,
        message: 'Đổi mật khẩu thành công!'
      };
    } catch (error) {
      console.error('Change password error:', error);
      
      if (error.response?.status === 404) {
        return { success: false, message: 'Không tìm thấy người dùng' };
      }
      
      return {
        success: false,
        message: error.message || 'Có lỗi xảy ra khi đổi mật khẩu'
      };
    }
  },

  // Clear cache
  clearCache() {
    userCache.clear();
    console.log('🗑️ User cache cleared');
  }
}; 