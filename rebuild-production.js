#!/usr/bin/env node

/**
 * <PERSON><PERSON>t để rebuild production với cấu hình mới
 * <PERSON>hắc phục lỗi MIME type và manifest icons
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Bắt đầu rebuild production...');

try {
  // 1. Clean build directory
  console.log('🧹 Dọn dẹp thư mục build...');
  if (fs.existsSync('build')) {
    execSync('rm -rf build', { stdio: 'inherit' });
  }

  // 2. Build project
  console.log('📦 Building project...');
  execSync('npm run build', { stdio: 'inherit' });

  // 3. Copy server configuration files
  console.log('⚙️ Copying server configuration files...');
  
  // Copy .htaccess
  const htaccessContent = `# Enable rewrite engine
RewriteEngine On

# Set proper MIME types for JavaScript modules
<FilesMatch "\\.(js|mjs)$">
    Header set Content-Type "application/javascript; charset=utf-8"
</FilesMatch>

# Set proper MIME types for CSS files
<FilesMatch "\\.css$">
    Header set Content-Type "text/css; charset=utf-8"
</FilesMatch>

# Set proper MIME types for JSON files
<FilesMatch "\\.json$">
    Header set Content-Type "application/json; charset=utf-8"
</FilesMatch>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Handle React Router (SPA) - redirect all requests to index.html
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule . /index.html [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>`;

  fs.writeFileSync('build/.htaccess', htaccessContent);

  // Copy _redirects for Netlify
  const redirectsContent = `# Netlify redirects for SPA
/*    /index.html   200

# Set proper headers for JavaScript modules
/*.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable`;

  fs.writeFileSync('build/_redirects', redirectsContent);

  // 4. Verify manifest.json
  console.log('✅ Verifying manifest.json...');
  const manifestPath = 'build/manifest.json';
  if (fs.existsSync(manifestPath)) {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    // Fix icon paths
    manifest.icons = manifest.icons.map(icon => ({
      ...icon,
      src: icon.src.replace(/%20/g, ' ')
    }));
    
    // Fix screenshot paths
    if (manifest.screenshots) {
      manifest.screenshots = manifest.screenshots.map(screenshot => ({
        ...screenshot,
        src: screenshot.src.replace(/%20/g, ' ')
      }));
    }
    
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
    console.log('✅ Manifest.json đã được cập nhật');
  }

  // 5. Verify critical files exist
  console.log('🔍 Kiểm tra các file quan trọng...');
  const criticalFiles = [
    'build/index.html',
    'build/manifest.json',
    'build/favicon.ico',
    'build/images/Logo Mini.png',
    'build/images/Logo DEXIN finall 2.png'
  ];

  for (const file of criticalFiles) {
    if (!fs.existsSync(file)) {
      console.warn(`⚠️ File không tồn tại: ${file}`);
    } else {
      console.log(`✅ ${file}`);
    }
  }

  console.log('🎉 Build production hoàn thành!');
  console.log('📋 Các file cấu hình server đã được tạo:');
  console.log('   - build/.htaccess (Apache)');
  console.log('   - build/web.config (IIS)');
  console.log('   - build/_redirects (Netlify)');
  console.log('');
  console.log('🚀 Bạn có thể deploy thư mục build/ lên production server');

} catch (error) {
  console.error('❌ Lỗi khi build:', error.message);
  process.exit(1);
}
