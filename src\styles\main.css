/* Fonts and custom styles */
@import './base/fonts.css';
@import './base/globals.css';
@import './components/utilities.css';

/* Tailwind */
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'BDLifelessGrotesk', sans-serif;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom classes */

.bg-gradient-pink {
  background: linear-gradient(180deg, #FFF0F5 0%, #FFE6EE 100%);
}

.text-gradient {
  background: linear-gradient(90deg, #FF6B93 0%, #FF5C85 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Toast customization */
.toast-message {
  font-family: 'BDLifelessGrotesk', sans-serif;
  font-size: 14px;
}

.Toastify__toast {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.Toastify__toast-theme--light.Toastify__toast--success {
  background-color: #fff;
  color: #333;
  border-left: 4px solid #FE7CAB;
}

.Toastify__toast-theme--light.Toastify__toast--info {
  background-color: #fff;
  color: #333;
  border-left: 4px solid #73C2FF;
}

.Toastify__progress-bar--success {
  background-color: #FE7CAB;
}

/* Font weight test classes */
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }
