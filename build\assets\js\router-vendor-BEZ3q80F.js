import{r as e,g as t,a as n}from"./react-vendor-CFHJfABC.js";function r(e,t){for(var n=0;n<t.length;n++){const r=t[n];if("string"!=typeof r&&!Array.isArray(r))for(const t in r)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(r,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>r[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var a=e();const o=t(a),i=r({__proto__:null,default:o},[a]);var l,u={};!function(){if(l)return u;l=1,Object.defineProperty(u,"__esModule",{value:!0}),u.parse=function(e,t){const n=new o,r=e.length;if(r<2)return n;const a=(null==t?void 0:t.decode)||c;let l=0;do{const t=e.indexOf("=",l);if(-1===t)break;const o=e.indexOf(";",l),u=-1===o?r:o;if(t>u){l=e.lastIndexOf(";",t-1)+1;continue}const c=i(e,l,t),h=s(e,t,c),p=e.slice(c,h);if(void 0===n[p]){let r=i(e,t+1,u),o=s(e,u,r);const l=a(e.slice(r,o));n[p]=l}l=u+1}while(l<r);return n},u.serialize=function(o,i,l){const u=(null==l?void 0:l.encode)||encodeURIComponent;if(!e.test(o))throw new TypeError(`argument name is invalid: ${o}`);const s=u(i);if(!t.test(s))throw new TypeError(`argument val is invalid: ${i}`);let c=o+"="+s;if(!l)return c;if(void 0!==l.maxAge){if(!Number.isInteger(l.maxAge))throw new TypeError(`option maxAge is invalid: ${l.maxAge}`);c+="; Max-Age="+l.maxAge}if(l.domain){if(!n.test(l.domain))throw new TypeError(`option domain is invalid: ${l.domain}`);c+="; Domain="+l.domain}if(l.path){if(!r.test(l.path))throw new TypeError(`option path is invalid: ${l.path}`);c+="; Path="+l.path}if(l.expires){if(!function(e){return"[object Date]"===a.call(e)}(l.expires)||!Number.isFinite(l.expires.valueOf()))throw new TypeError(`option expires is invalid: ${l.expires}`);c+="; Expires="+l.expires.toUTCString()}l.httpOnly&&(c+="; HttpOnly");l.secure&&(c+="; Secure");l.partitioned&&(c+="; Partitioned");if(l.priority){switch("string"==typeof l.priority?l.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${l.priority}`)}}if(l.sameSite){switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${l.sameSite}`)}}return c};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function i(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function s(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var s="popstate";function c(e={}){return function(e,t,n,r={}){let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,l="POP",u=null,c=p();null==c&&(c=0,i.replaceState({...i.state,idx:c},""));function p(){return(i.state||{idx:null}).idx}function v(){l="POP";let e=p(),t=null==e?null:e-c;c=e,u&&u({action:l,location:b.location,delta:t})}function y(e,t){l="PUSH";let n=m(b.location,e,t);c=p()+1;let r=d(n,c),s=b.createHref(n);try{i.pushState(r,"",s)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(s)}o&&u&&u({action:l,location:b.location,delta:1})}function g(e,t){l="REPLACE";let n=m(b.location,e,t);c=p();let r=d(n,c),a=b.createHref(n);i.replaceState(r,"",a),o&&u&&u({action:l,location:b.location,delta:0})}function w(e){return function(e,t=!1){let n="http://localhost";"undefined"!=typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href);h(n,"No window.location.(origin|href) available to create URL");let r="string"==typeof e?e:f(e);r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r);return new URL(r,n)}(e)}let b={get action(){return l},get location(){return e(a,i)},listen(e){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener(s,v),u=e,()=>{a.removeEventListener(s,v),u=null}},createHref:e=>t(a,e),createURL:w,encodeLocation(e){let t=w(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:y,replace:g,go:e=>i.go(e)};return b}((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return m("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:f(t)}),0,e)}function h(e,t){if(!1===e||null==e)throw new Error(t)}function p(e,t){if(!e)try{throw new Error(t)}catch(n){}}function d(e,t){return{usr:e.state,key:e.key,idx:t}}function m(e,t,n=null,r){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?v(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function f({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function v(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function y(e,t,n="/"){return function(e,t,n,r){let a="string"==typeof t?v(t):t,o=A(a.pathname||"/",n);if(null==o)return null;let i=g(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let l=null;for(let u=0;null==l&&u<i.length;++u){let e=T(o);l=L(i[u],e,r)}return l}(e,t,n,!1)}function g(e,t=[],n=[],r=""){let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(h(i.relativePath.startsWith(r),`Absolute route path "${i.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(r.length));let l=D([r,i.relativePath]),u=n.concat(i);e.children&&e.children.length>0&&(h(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),g(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:k(l,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&(null==(n=e.path)?void 0:n.includes("?")))for(let r of w(e.path))a(e,t,r);else a(e,t)})),t}function w(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=w(r.join("/")),l=[];return l.push(...i.map((e=>""===e?o:[o,e].join("/")))),a&&l.push(...i),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var b=/^:[\w-]+$/,x=3,E=2,C=1,R=10,S=-2,$=e=>"*"===e;function k(e,t){let n=e.split("/"),r=n.length;return n.some($)&&(r+=S),t&&(r+=E),n.filter((e=>!$(e))).reduce(((e,t)=>e+(b.test(t)?x:""===t?C:R)),r)}function L(e,t,n=!1){let{routesMeta:r}=e,a={},o="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],u=l===r.length-1,s="/"===o?t:t.slice(o.length)||"/",c=P({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),h=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=P({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:D([o,c.pathname]),pathnameBase:F(D([o,c.pathnameBase])),route:h}),"/"!==c.pathnameBase&&(o=D([o,c.pathnameBase]))}return i}function P(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t=!1,n=!0){p("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:r.reduce(((e,{paramName:t,isOptional:n},r)=>{if("*"===t){let e=l[r]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const a=l[r];return e[t]=n&&!a?void 0:(a||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function T(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return p(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function A(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function O(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function N(e){let t=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function M(e,t,n,r=!1){let a;"string"==typeof e?a=v(e):(a={...e},h(!a.pathname||!a.pathname.includes("?"),O("?","pathname","search",a)),h(!a.pathname||!a.pathname.includes("#"),O("#","pathname","hash",a)),h(!a.search||!a.search.includes("#"),O("#","search","hash",a)));let o,i=""===e||""===a.pathname,l=i?"/":a.pathname;if(null==l)o=n;else{let e=t.length-1;if(!r&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let u=function(e,t="/"){let{pathname:n,search:r="",hash:a=""}="string"==typeof e?v(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:j(r),hash:U(a)}}(a,o),s=l&&"/"!==l&&l.endsWith("/"),c=(i||"."===l)&&n.endsWith("/");return u.pathname.endsWith("/")||!s&&!c||(u.pathname+="/"),u}var D=e=>e.join("/").replace(/\/\/+/g,"/"),F=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),j=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",U=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var W=["POST","PUT","PATCH","DELETE"];new Set(W);var B=["GET",...W];new Set(B);var _=a.createContext(null);_.displayName="DataRouter";var z=a.createContext(null);z.displayName="DataRouterState";var I=a.createContext({isTransitioning:!1});I.displayName="ViewTransition",a.createContext(new Map).displayName="Fetchers",a.createContext(null).displayName="Await";var H=a.createContext(null);H.displayName="Navigation";var Y=a.createContext(null);Y.displayName="Location";var J=a.createContext({outlet:null,matches:[],isDataRoute:!1});J.displayName="Route";var K=a.createContext(null);function V(){return null!=a.useContext(Y)}function q(){return h(V(),"useLocation() may be used only in the context of a <Router> component."),a.useContext(Y).location}K.displayName="RouteError";var G="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function X(e){a.useContext(H).static||a.useLayoutEffect(e)}function Q(){let{isDataRoute:e}=a.useContext(J);return e?function(){let{router:e}=function(e){let t=a.useContext(_);return h(t,ie(e)),t}("useNavigate"),t=le("useNavigate"),n=a.useRef(!1);return X((()=>{n.current=!0})),a.useCallback((async(r,a={})=>{p(n.current,G),n.current&&("number"==typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...a}))}),[e,t])}():function(){h(V(),"useNavigate() may be used only in the context of a <Router> component.");let e=a.useContext(_),{basename:t,navigator:n}=a.useContext(H),{matches:r}=a.useContext(J),{pathname:o}=q(),i=JSON.stringify(N(r)),l=a.useRef(!1);return X((()=>{l.current=!0})),a.useCallback(((r,a={})=>{if(p(l.current,G),!l.current)return;if("number"==typeof r)return void n.go(r);let u=M(r,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(u.pathname="/"===u.pathname?t:D([t,u.pathname])),(a.replace?n.replace:n.push)(u,a.state,a)}),[t,n,i,o,e])}()}function Z(){let{matches:e}=a.useContext(J),t=e[e.length-1];return t?t.params:{}}function ee(e,{relative:t}={}){let{matches:n}=a.useContext(J),{pathname:r}=q(),o=JSON.stringify(N(n));return a.useMemo((()=>M(e,JSON.parse(o),r,"path"===t)),[e,o,r,t])}function te(e,t,n,r){var o;h(V(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:i}=a.useContext(H),{matches:l}=a.useContext(J),u=l[l.length-1],s=u?u.params:{},c=u?u.pathname:"/",d=u?u.pathnameBase:"/",m=u&&u.route;{let e=m&&m.path||"";se(c,!m||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${c}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let f,g=q();if(t){let e="string"==typeof t?v(t):t;h("/"===d||(null==(o=e.pathname)?void 0:o.startsWith(d)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${d}" but pathname "${e.pathname}" was given in the \`location\` prop.`),f=e}else f=g;let w=f.pathname||"/",b=w;if("/"!==d){let e=d.replace(/^\//,"").split("/");b="/"+w.replace(/^\//,"").split("/").slice(e.length).join("/")}let x=y(e,{pathname:b});p(m||null!=x,`No routes matched location "${f.pathname}${f.search}${f.hash}" `),p(null==x||void 0!==x[x.length-1].route.element||void 0!==x[x.length-1].route.Component||void 0!==x[x.length-1].route.lazy,`Matched leaf route at location "${f.pathname}${f.search}${f.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let E=function(e,t=[],n=null){if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,o=null==n?void 0:n.errors;if(null!=o){let e=r.findIndex((e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id])));h(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let i=!1,l=-1;if(n)for(let a=0;a<r.length;a++){let e=r[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=a),e.route.id){let{loaderData:t,errors:a}=n,o=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!a||void 0===a[e.route.id]);if(e.route.lazy||o){i=!0,r=l>=0?r.slice(0,l+1):[r[0]];break}}}return r.reduceRight(((e,u,s)=>{let c,h=!1,p=null,d=null;n&&(c=o&&u.route.id?o[u.route.id]:void 0,p=u.route.errorElement||re,i&&(l<0&&0===s?(se("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,d=null):l===s&&(h=!0,d=u.route.hydrateFallbackElement||null)));let m=t.concat(r.slice(0,s+1)),f=()=>{let t;return t=c?p:h?d:u.route.Component?a.createElement(u.route.Component,null):u.route.element?u.route.element:e,a.createElement(oe,{match:u,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(u.route.ErrorBoundary||u.route.errorElement||0===s)?a.createElement(ae,{location:n.location,revalidation:n.revalidation,component:p,error:c,children:f(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):f()}),null)}(x&&x.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:D([d,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:D([d,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);return t&&E?a.createElement(Y.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...f},navigationType:"POP"}},E):E}function ne(){let e=function(){var e;let t=a.useContext(K),n=function(e){let t=a.useContext(z);return h(t,ie(e)),t}("useRouteError"),r=le("useRouteError");if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},l=null;return l=a.createElement(a.Fragment,null,a.createElement("p",null,"💿 Hey developer 👋"),a.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",a.createElement("code",{style:i},"ErrorBoundary")," or"," ",a.createElement("code",{style:i},"errorElement")," prop on your route.")),a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,l)}a.createContext(null);var re=a.createElement(ne,null),ae=class extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?a.createElement(J.Provider,{value:this.props.routeContext},a.createElement(K.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function oe({routeContext:e,match:t,children:n}){let r=a.useContext(_);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),a.createElement(J.Provider,{value:e},n)}function ie(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function le(e){let t=function(e){let t=a.useContext(J);return h(t,ie(e)),t}(e),n=t.matches[t.matches.length-1];return h(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}var ue={};function se(e,t,n){t||ue[e]||(ue[e]=!0,p(!1,n))}function ce(e){h(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function he({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:o,static:i=!1}){h(!V(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),u=a.useMemo((()=>({basename:l,navigator:o,static:i,future:{}})),[l,o,i]);"string"==typeof n&&(n=v(n));let{pathname:s="/",search:c="",hash:d="",state:m=null,key:f="default"}=n,y=a.useMemo((()=>{let e=A(s,l);return null==e?null:{location:{pathname:e,search:c,hash:d,state:m,key:f},navigationType:r}}),[l,s,c,d,m,f,r]);return p(null!=y,`<Router basename="${l}"> is not able to match the URL "${s}${c}${d}" because it does not start with the basename, so the <Router> won't render anything.`),null==y?null:a.createElement(H.Provider,{value:u},a.createElement(Y.Provider,{children:t,value:y}))}function pe({children:e,location:t}){return te(de(e),t)}function de(e,t=[]){let n=[];return a.Children.forEach(e,((e,r)=>{if(!a.isValidElement(e))return;let o=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,de(e.props.children,o));h(e.type===ce,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),h(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=de(e.props.children,o)),n.push(i)})),n}a.memo((function({routes:e,future:t,state:n}){return te(e,void 0,n,t)}));var me="get",fe="application/x-www-form-urlencoded";function ve(e){return null!=e&&"string"==typeof e.tagName}var ye=null;var ge=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function we(e){return null==e||ge.has(e)?e:(p(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${fe}"`),null)}function be(e,t){let n,r,a,o,i;if(ve(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?A(i,t):null,n=e.getAttribute("method")||me,a=we(e.getAttribute("enctype"))||fe,o=new FormData(e)}else if(function(e){return ve(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return ve(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?A(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||me,a=we(e.getAttribute("formenctype"))||we(i.getAttribute("enctype"))||fe,o=new FormData(i,e),!function(){if(null===ye)try{new FormData(document.createElement("form"),0),ye=!1}catch(e){ye=!0}return ye}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(ve(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=me,r=null,a=fe,i=e}var l;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}function xe(e,t){if(!1===e||null==e)throw new Error(t)}function Ee(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function Ce(e,t,n){return function(e,t){let n=new Set;return new Set(t),e.reduce(((e,t)=>{let r=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(t));return n.has(r)||(n.add(r),e.push({key:r,link:t})),e}),[])}((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(Ee).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}function Re(e,t,n,r,a,o){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null==(r=n[t].route.path)?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===o?t.filter(((e,t)=>i(e,t)||l(e,t))):"data"===o?t.filter(((t,o)=>{var u;let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,o)||l(t,o))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:(null==(u=n[0])?void 0:u.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0})):[]}function Se(e,t,{includeHydrateFallback:n}={}){return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a})).flat(1),[...new Set(r)];var r}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function $e(){let e=a.useContext(_);return xe(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}var ke=a.createContext(void 0);function Le(){let e=a.useContext(ke);return xe(e,"You must render this element inside a <HydratedRouter> element"),e}function Pe(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Te({page:e,...t}){let{router:n}=$e(),r=a.useMemo((()=>y(n.routes,e,n.basename)),[n.routes,e,n.basename]);return r?a.createElement(Ae,{page:e,matches:r,...t}):null}function Ae({page:e,matches:t,...n}){let r=q(),{manifest:o,routeModules:i}=Le(),{basename:l}=$e(),{loaderData:u,matches:s}=function(){let e=a.useContext(z);return xe(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),c=a.useMemo((()=>Re(e,t,s,o,r,"data")),[e,t,s,o,r]),h=a.useMemo((()=>Re(e,t,s,o,r,"assets")),[e,t,s,o,r]),p=a.useMemo((()=>{if(e===r.pathname+r.search+r.hash)return[];let n=new Set,a=!1;if(t.forEach((e=>{var t;let r=o.routes[e.route.id];r&&r.hasLoader&&(!c.some((t=>t.route.id===e.route.id))&&e.route.id in u&&(null==(t=i[e.route.id])?void 0:t.shouldRevalidate)||r.hasClientLoader?a=!0:n.add(e.route.id))})),0===n.size)return[];let s=function(e,t){let n="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===A(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}(e,l);return a&&n.size>0&&s.searchParams.set("_routes",t.filter((e=>n.has(e.route.id))).map((e=>e.route.id)).join(",")),[s.pathname+s.search]}),[l,u,r,o,c,t,e,i]),d=a.useMemo((()=>Se(h,o)),[h,o]),m=function(e){let{manifest:t,routeModules:n}=Le(),[r,o]=a.useState([]);return a.useEffect((()=>{let r=!1;return Ce(e,t,n).then((e=>{r||o(e)})),()=>{r=!0}}),[e,t,n]),r}(h);return a.createElement(a.Fragment,null,p.map((e=>a.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...n}))),d.map((e=>a.createElement("link",{key:e,rel:"modulepreload",href:e,...n}))),m.map((({key:e,link:t})=>a.createElement("link",{key:e,...t}))))}function Oe(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}ke.displayName="FrameworkContext";var Ne="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{Ne&&(window.__reactRouterVersion="7.6.1")}catch(_e){}function Me({basename:e,children:t,window:n}){let r=a.useRef();null==r.current&&(r.current=c({window:n,v5Compat:!0}));let o=r.current,[i,l]=a.useState({action:o.action,location:o.location}),u=a.useCallback((e=>{a.startTransition((()=>l(e)))}),[l]);return a.useLayoutEffect((()=>o.listen(u)),[o,u]),a.createElement(he,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:o})}var De=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Fe=a.forwardRef((function({onClick:e,discover:t="render",prefetch:n="none",relative:r,reloadDocument:o,replace:i,state:l,target:u,to:s,preventScrollReset:c,viewTransition:d,...m},v){let y,{basename:g}=a.useContext(H),w="string"==typeof s&&De.test(s),b=!1;if("string"==typeof s&&w&&(y=s,Ne))try{let e=new URL(window.location.href),t=s.startsWith("//")?new URL(e.protocol+s):new URL(s),n=A(t.pathname,g);t.origin===e.origin&&null!=n?s=n+t.search+t.hash:b=!0}catch(_e){p(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let x=function(e,{relative:t}={}){h(V(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=a.useContext(H),{hash:o,pathname:i,search:l}=ee(e,{relative:t}),u=i;return"/"!==n&&(u="/"===i?n:D([n,i])),r.createHref({pathname:u,search:l,hash:o})}(s,{relative:r}),[E,C,R]=function(e,t){let n=a.useContext(ke),[r,o]=a.useState(!1),[i,l]=a.useState(!1),{onFocus:u,onBlur:s,onMouseEnter:c,onMouseLeave:h,onTouchStart:p}=t,d=a.useRef(null);a.useEffect((()=>{if("render"===e&&l(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{l(e.isIntersecting)}))}),{threshold:.5});return d.current&&e.observe(d.current),()=>{e.disconnect()}}}),[e]),a.useEffect((()=>{if(r){let e=setTimeout((()=>{l(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let m=()=>{o(!0)},f=()=>{o(!1),l(!1)};return n?"intent"!==e?[i,d,{}]:[i,d,{onFocus:Pe(u,m),onBlur:Pe(s,f),onMouseEnter:Pe(c,m),onMouseLeave:Pe(h,f),onTouchStart:Pe(p,m)}]:[!1,d,{}]}(n,m),S=function(e,{target:t,replace:n,state:r,preventScrollReset:o,relative:i,viewTransition:l}={}){let u=Q(),s=q(),c=ee(e,{relative:i});return a.useCallback((a=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(a,t)){a.preventDefault();let t=void 0!==n?n:f(s)===f(c);u(e,{replace:t,state:r,preventScrollReset:o,relative:i,viewTransition:l})}}),[s,u,c,n,r,t,e,o,i,l])}(s,{replace:i,state:l,target:u,preventScrollReset:c,relative:r,viewTransition:d});let $=a.createElement("a",{...m,...R,href:y||x,onClick:b||o?e:function(t){e&&e(t),t.defaultPrevented||S(t)},ref:Oe(v,C),target:u,"data-discover":w||"render"!==t?void 0:"true"});return E&&!w?a.createElement(a.Fragment,null,$,a.createElement(Te,{page:x})):$}));function je(e){let t=a.useContext(_);return h(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}Fe.displayName="Link",a.forwardRef((function({"aria-current":e="page",caseSensitive:t=!1,className:n="",end:r=!1,style:o,to:i,viewTransition:l,children:u,...s},c){let p=ee(i,{relative:s.relative}),d=q(),m=a.useContext(z),{navigator:f,basename:v}=a.useContext(H),y=null!=m&&function(e,t={}){let n=a.useContext(I);h(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=je("useViewTransitionState"),o=ee(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=A(n.currentLocation.pathname,r)||n.currentLocation.pathname,l=A(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=P(o.pathname,l)||null!=P(o.pathname,i)}(p)&&!0===l,g=f.encodeLocation?f.encodeLocation(p).pathname:p.pathname,w=d.pathname,b=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;t||(w=w.toLowerCase(),b=b?b.toLowerCase():null,g=g.toLowerCase()),b&&v&&(b=A(b,v)||b);const x="/"!==g&&g.endsWith("/")?g.length-1:g.length;let E,C=w===g||!r&&w.startsWith(g)&&"/"===w.charAt(x),R=null!=b&&(b===g||!r&&b.startsWith(g)&&"/"===b.charAt(g.length)),S={isActive:C,isPending:R,isTransitioning:y},$=C?e:void 0;E="function"==typeof n?n(S):[n,C?"active":null,R?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let k="function"==typeof o?o(S):o;return a.createElement(Fe,{...s,"aria-current":$,className:E,ref:c,style:k,to:i,viewTransition:l},"function"==typeof u?u(S):u)})).displayName="NavLink",a.forwardRef((({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:o,state:i,method:l=me,action:u,onSubmit:s,relative:c,preventScrollReset:p,viewTransition:d,...m},v)=>{let y=function(){let{router:e}=je("useSubmit"),{basename:t}=a.useContext(H),n=le("useRouteId");return a.useCallback((async(r,a={})=>{let{action:o,method:i,encType:l,formData:u,body:s}=be(r,t);if(!1===a.navigate){let t=a.fetcherKey||We();await e.fetch(t,n,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})}),[e,t,n])}(),g=function(e,{relative:t}={}){let{basename:n}=a.useContext(H),r=a.useContext(J);h(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),i={...ee(e||".",{relative:t})},l=q();if(null==e){i.search=l.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();i.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:D([n,i.pathname]));return f(i)}(u,{relative:c}),w="get"===l.toLowerCase()?"get":"post",b="string"==typeof u&&De.test(u);return a.createElement("form",{ref:v,method:w,action:g,onSubmit:r?s:e=>{if(s&&s(e),e.defaultPrevented)return;e.preventDefault();let r=e.nativeEvent.submitter,a=(null==r?void 0:r.getAttribute("formmethod"))||l;y(r||e.currentTarget,{fetcherKey:t,method:a,navigate:n,replace:o,state:i,relative:c,preventScrollReset:p,viewTransition:d})},...m,"data-discover":b||"render"!==e?void 0:"true"})})).displayName="Form";var Ue=0,We=()=>`__${String(++Ue)}__`;var Be=n();export{Me as B,Fe as L,o as R,Be as a,Z as b,q as c,pe as d,ce as e,a as r,i as t,Q as u};
