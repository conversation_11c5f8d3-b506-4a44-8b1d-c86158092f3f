import{c as t,g as e}from"./react-vendor-CFHJfABC.js";import{_ as r}from"./utils-vendor-B_0U5y1k.js";import"./router-vendor-BEZ3q80F.js";var i,n,a={};function s(){if(n)return i;n=1;var e=function(t){return t&&t.Math===Math&&t};return i=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof t&&t)||e("object"==typeof i&&i)||function(){return this}()||Function("return this")()}var o,u,h,c,l,f,g,p,d={};function v(){return u?o:(u=1,o=function(t){try{return!!t()}catch(e){return!0}})}function y(){if(c)return h;c=1;var t=v();return h=!t((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function m(){if(f)return l;f=1;var t=v();return l=!t((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))}function x(){if(p)return g;p=1;var t=m(),e=Function.prototype.call;return g=t?e.bind(e):function(){return e.apply(e,arguments)},g}var b,w,S,T,O,A,C,P,E,N,M,R,_,V,I,k,L,D,j,B,z,U,F,H,X,Y,W,q,$,G,Q,Z,K,J,tt,et,rt,it,nt,at,st,ot={};function ut(){return S?w:(S=1,w=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}})}function ht(){if(O)return T;O=1;var t=m(),e=Function.prototype,r=e.call,i=t&&e.bind.bind(r,r);return T=t?i:function(t){return function(){return r.apply(t,arguments)}},T}function ct(){if(C)return A;C=1;var t=ht(),e=t({}.toString),r=t("".slice);return A=function(t){return r(e(t),8,-1)}}function lt(){if(E)return P;E=1;var t=ht(),e=v(),r=ct(),i=Object,n=t("".split);return P=e((function(){return!i("z").propertyIsEnumerable(0)}))?function(t){return"String"===r(t)?n(t,""):i(t)}:i}function ft(){return M?N:(M=1,N=function(t){return null==t})}function gt(){if(_)return R;_=1;var t=ft(),e=TypeError;return R=function(r){if(t(r))throw new e("Can't call method on "+r);return r}}function pt(){if(I)return V;I=1;var t=lt(),e=gt();return V=function(r){return t(e(r))}}function dt(){if(L)return k;L=1;var t="object"==typeof document&&document.all;return k=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(t){return"function"==typeof t}}function vt(){if(j)return D;j=1;var t=dt();return D=function(e){return"object"==typeof e?null!==e:t(e)}}function yt(){if(z)return B;z=1;var t=s(),e=dt();return B=function(r,i){return arguments.length<2?(n=t[r],e(n)?n:void 0):t[r]&&t[r][i];var n},B}function mt(){if(F)return U;F=1;var t=ht();return U=t({}.isPrototypeOf)}function xt(){if(X)return H;X=1;var t=s().navigator,e=t&&t.userAgent;return H=e?String(e):""}function bt(){if(W)return Y;W=1;var t,e,r=s(),i=xt(),n=r.process,a=r.Deno,o=n&&n.versions||a&&a.version,u=o&&o.v8;return u&&(e=(t=u.split("."))[0]>0&&t[0]<4?1:+(t[0]+t[1])),!e&&i&&(!(t=i.match(/Edge\/(\d+)/))||t[1]>=74)&&(t=i.match(/Chrome\/(\d+)/))&&(e=+t[1]),Y=e}function wt(){if($)return q;$=1;var t=bt(),e=v(),r=s().String;return q=!!Object.getOwnPropertySymbols&&!e((function(){var e=Symbol("symbol detection");return!r(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&t&&t<41}))}function St(){if(Q)return G;Q=1;var t=wt();return G=t&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}function Tt(){if(K)return Z;K=1;var t=yt(),e=dt(),r=mt(),i=St(),n=Object;return Z=i?function(t){return"symbol"==typeof t}:function(i){var a=t("Symbol");return e(a)&&r(a.prototype,n(i))}}function Ot(){if(tt)return J;tt=1;var t=String;return J=function(e){try{return t(e)}catch(r){return"Object"}}}function At(){if(rt)return et;rt=1;var t=dt(),e=Ot(),r=TypeError;return et=function(i){if(t(i))return i;throw new r(e(i)+" is not a function")}}function Ct(){if(nt)return it;nt=1;var t=At(),e=ft();return it=function(r,i){var n=r[i];return e(n)?void 0:t(n)}}var Pt,Et,Nt,Mt,Rt,_t,Vt,It,kt,Lt,Dt,jt,Bt,zt,Ut,Ft,Ht,Xt,Yt,Wt,qt,$t,Gt,Qt,Zt={exports:{}};function Kt(){return Et?Pt:(Et=1,Pt=!1)}function Jt(){if(Mt)return Nt;Mt=1;var t=s(),e=Object.defineProperty;return Nt=function(r,i){try{e(t,r,{value:i,configurable:!0,writable:!0})}catch(n){t[r]=i}return i}}function te(){if(Rt)return Zt.exports;Rt=1;var t=Kt(),e=s(),r=Jt(),i="__core-js_shared__",n=Zt.exports=e[i]||r(i,{});return(n.versions||(n.versions=[])).push({version:"3.42.0",mode:t?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.42.0/LICENSE",source:"https://github.com/zloirock/core-js"}),Zt.exports}function ee(){if(Vt)return _t;Vt=1;var t=te();return _t=function(e,r){return t[e]||(t[e]=r||{})}}function re(){if(kt)return It;kt=1;var t=gt(),e=Object;return It=function(r){return e(t(r))}}function ie(){if(Dt)return Lt;Dt=1;var t=ht(),e=re(),r=t({}.hasOwnProperty);return Lt=Object.hasOwn||function(t,i){return r(e(t),i)}}function ne(){if(Bt)return jt;Bt=1;var t=ht(),e=0,r=Math.random(),i=t(1..toString);return jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+i(++e+r,36)}}function ae(){if(Ut)return zt;Ut=1;var t=s(),e=ee(),r=ie(),i=ne(),n=wt(),a=St(),o=t.Symbol,u=e("wks"),h=a?o.for||o:o&&o.withoutSetter||i;return zt=function(t){return r(u,t)||(u[t]=n&&r(o,t)?o[t]:h("Symbol."+t)),u[t]}}function se(){if(Ht)return Ft;Ht=1;var t=x(),e=vt(),r=Tt(),i=Ct(),n=function(){if(st)return at;st=1;var t=x(),e=dt(),r=vt(),i=TypeError;return at=function(n,a){var s,o;if("string"===a&&e(s=n.toString)&&!r(o=t(s,n)))return o;if(e(s=n.valueOf)&&!r(o=t(s,n)))return o;if("string"!==a&&e(s=n.toString)&&!r(o=t(s,n)))return o;throw new i("Can't convert object to primitive value")}}(),a=ae(),s=TypeError,o=a("toPrimitive");return Ft=function(a,u){if(!e(a)||r(a))return a;var h,c=i(a,o);if(c){if(void 0===u&&(u="default"),h=t(c,a,u),!e(h)||r(h))return h;throw new s("Can't convert object to primitive value")}return void 0===u&&(u="number"),n(a,u)}}function oe(){if(Yt)return Xt;Yt=1;var t=se(),e=Tt();return Xt=function(r){var i=t(r,"string");return e(i)?i:i+""}}function ue(){if(qt)return Wt;qt=1;var t=s(),e=vt(),r=t.document,i=e(r)&&e(r.createElement);return Wt=function(t){return i?r.createElement(t):{}}}function he(){if(Gt)return $t;Gt=1;var t=y(),e=v(),r=ue();return $t=!t&&!e((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))}function ce(){if(Qt)return d;Qt=1;var t=y(),e=x(),r=function(){if(b)return ot;b=1;var t={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,r=e&&!t.call({1:2},1);return ot.f=r?function(t){var r=e(this,t);return!!r&&r.enumerable}:t,ot}(),i=ut(),n=pt(),a=oe(),s=ie(),o=he(),u=Object.getOwnPropertyDescriptor;return d.f=t?u:function(t,h){if(t=n(t),h=a(h),o)try{return u(t,h)}catch(c){}if(s(t,h))return i(!e(r.f,t,h),t[h])},d}var le,fe,ge,pe,de,ve,ye,me={};function xe(){if(fe)return le;fe=1;var t=y(),e=v();return le=t&&e((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))}function be(){if(pe)return ge;pe=1;var t=vt(),e=String,r=TypeError;return ge=function(i){if(t(i))return i;throw new r(e(i)+" is not an object")}}function we(){if(de)return me;de=1;var t=y(),e=he(),r=xe(),i=be(),n=oe(),a=TypeError,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,u="enumerable",h="configurable",c="writable";return me.f=t?r?function(t,e,r){if(i(t),e=n(e),i(r),"function"==typeof t&&"prototype"===e&&"value"in r&&c in r&&!r[c]){var a=o(t,e);a&&a[c]&&(t[e]=r.value,r={configurable:h in r?r[h]:a[h],enumerable:u in r?r[u]:a[u],writable:!1})}return s(t,e,r)}:s:function(t,r,o){if(i(t),r=n(r),i(o),e)try{return s(t,r,o)}catch(u){}if("get"in o||"set"in o)throw new a("Accessors not supported");return"value"in o&&(t[r]=o.value),t},me}function Se(){if(ye)return ve;ye=1;var t=y(),e=we(),r=ut();return ve=t?function(t,i,n){return e.f(t,i,r(1,n))}:function(t,e,r){return t[e]=r,t}}var Te,Oe,Ae,Ce,Pe,Ee,Ne,Me,Re,_e,Ve,Ie,ke,Le,De,je={exports:{}};function Be(){if(Oe)return Te;Oe=1;var t=y(),e=ie(),r=Function.prototype,i=t&&Object.getOwnPropertyDescriptor,n=e(r,"name"),a=n&&"something"===function(){}.name,s=n&&(!t||t&&i(r,"name").configurable);return Te={EXISTS:n,PROPER:a,CONFIGURABLE:s}}function ze(){if(Ce)return Ae;Ce=1;var t=ht(),e=dt(),r=te(),i=t(Function.toString);return e(r.inspectSource)||(r.inspectSource=function(t){return i(t)}),Ae=r.inspectSource}function Ue(){if(Me)return Ne;Me=1;var t=ee(),e=ne(),r=t("keys");return Ne=function(t){return r[t]||(r[t]=e(t))}}function Fe(){return _e?Re:(_e=1,Re={})}function He(){if(Ie)return Ve;Ie=1;var t,e,r,i=function(){if(Ee)return Pe;Ee=1;var t=s(),e=dt(),r=t.WeakMap;return Pe=e(r)&&/native code/.test(String(r))}(),n=s(),a=vt(),o=Se(),u=ie(),h=te(),c=Ue(),l=Fe(),f="Object already initialized",g=n.TypeError,p=n.WeakMap;if(i||h.state){var d=h.state||(h.state=new p);d.get=d.get,d.has=d.has,d.set=d.set,t=function(t,e){if(d.has(t))throw new g(f);return e.facade=t,d.set(t,e),e},e=function(t){return d.get(t)||{}},r=function(t){return d.has(t)}}else{var v=c("state");l[v]=!0,t=function(t,e){if(u(t,v))throw new g(f);return e.facade=t,o(t,v,e),e},e=function(t){return u(t,v)?t[v]:{}},r=function(t){return u(t,v)}}return Ve={set:t,get:e,has:r,enforce:function(i){return r(i)?e(i):t(i,{})},getterFor:function(t){return function(r){var i;if(!a(r)||(i=e(r)).type!==t)throw new g("Incompatible receiver, "+t+" required");return i}}}}function Xe(){if(ke)return je.exports;ke=1;var t=ht(),e=v(),r=dt(),i=ie(),n=y(),a=Be().CONFIGURABLE,s=ze(),o=He(),u=o.enforce,h=o.get,c=String,l=Object.defineProperty,f=t("".slice),g=t("".replace),p=t([].join),d=n&&!e((function(){return 8!==l((function(){}),"length",{value:8}).length})),m=String(String).split("String"),x=je.exports=function(t,e,r){"Symbol("===f(c(e),0,7)&&(e="["+g(c(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!i(t,"name")||a&&t.name!==e)&&(n?l(t,"name",{value:e,configurable:!0}):t.name=e),d&&r&&i(r,"arity")&&t.length!==r.arity&&l(t,"length",{value:r.arity});try{r&&i(r,"constructor")&&r.constructor?n&&l(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(o){}var s=u(t);return i(s,"source")||(s.source=p(m,"string"==typeof e?e:"")),t};return Function.prototype.toString=x((function(){return r(this)&&h(this).source||s(this)}),"toString"),je.exports}function Ye(){if(De)return Le;De=1;var t=dt(),e=we(),r=Xe(),i=Jt();return Le=function(n,a,s,o){o||(o={});var u=o.enumerable,h=void 0!==o.name?o.name:a;if(t(s)&&r(s,h,o),o.global)u?n[a]=s:i(a,s);else{try{o.unsafe?n[a]&&(u=!0):delete n[a]}catch(c){}u?n[a]=s:e.f(n,a,{value:s,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return n}}var We,qe,$e,Ge,Qe,Ze,Ke,Je,tr,er,rr,ir,nr,ar,sr,or,ur,hr={};function cr(){if(Ge)return $e;Ge=1;var t=function(){if(qe)return We;qe=1;var t=Math.ceil,e=Math.floor;return We=Math.trunc||function(r){var i=+r;return(i>0?e:t)(i)}}();return $e=function(e){var r=+e;return r!=r||0===r?0:t(r)}}function lr(){if(Je)return Ke;Je=1;var t=cr(),e=Math.min;return Ke=function(r){var i=t(r);return i>0?e(i,9007199254740991):0}}function fr(){if(er)return tr;er=1;var t=lr();return tr=function(e){return t(e.length)}}function gr(){if(ir)return rr;ir=1;var t=pt(),e=function(){if(Ze)return Qe;Ze=1;var t=cr(),e=Math.max,r=Math.min;return Qe=function(i,n){var a=t(i);return a<0?e(a+n,0):r(a,n)}}(),r=fr(),i=function(i){return function(n,a,s){var o=t(n),u=r(o);if(0===u)return!i&&-1;var h,c=e(s,u);if(i&&a!=a){for(;u>c;)if((h=o[c++])!=h)return!0}else for(;u>c;c++)if((i||c in o)&&o[c]===a)return i||c||0;return!i&&-1}};return rr={includes:i(!0),indexOf:i(!1)}}function pr(){if(ar)return nr;ar=1;var t=ht(),e=ie(),r=pt(),i=gr().indexOf,n=Fe(),a=t([].push);return nr=function(t,s){var o,u=r(t),h=0,c=[];for(o in u)!e(n,o)&&e(u,o)&&a(c,o);for(;s.length>h;)e(u,o=s[h++])&&(~i(c,o)||a(c,o));return c}}function dr(){return or?sr:(or=1,sr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}var vr,yr,mr,xr,br,wr,Sr,Tr,Or,Ar,Cr,Pr,Er,Nr,Mr,Rr,_r,Vr,Ir,kr,Lr,Dr,jr,Br,zr,Ur,Fr,Hr,Xr,Yr,Wr,qr,$r,Gr,Qr,Zr,Kr,Jr,ti,ei,ri,ii,ni,ai,si,oi,ui,hi,ci,li,fi,gi,pi,di,vi,yi,mi,xi,bi,wi,Si,Ti,Oi,Ai,Ci,Pi,Ei,Ni,Mi,Ri,_i,Vi,Ii,ki={};function Li(){if(mr)return yr;mr=1;var t=yt(),e=ht(),r=function(){if(ur)return hr;ur=1;var t=pr(),e=dr().concat("length","prototype");return hr.f=Object.getOwnPropertyNames||function(r){return t(r,e)},hr}(),i=(vr||(vr=1,ki.f=Object.getOwnPropertySymbols),ki),n=be(),a=e([].concat);return yr=t("Reflect","ownKeys")||function(t){var e=r.f(n(t)),s=i.f;return s?a(e,s(t)):e}}function Di(){if(Sr)return wr;Sr=1;var t=v(),e=dt(),r=/#|\.prototype\./,i=function(r,i){var u=a[n(r)];return u===o||u!==s&&(e(i)?t(i):!!i)},n=i.normalize=function(t){return String(t).replace(r,".").toLowerCase()},a=i.data={},s=i.NATIVE="N",o=i.POLYFILL="P";return wr=i}function ji(){if(Or)return Tr;Or=1;var t=s(),e=ce().f,r=Se(),i=Ye(),n=Jt(),a=function(){if(br)return xr;br=1;var t=ie(),e=Li(),r=ce(),i=we();return xr=function(n,a,s){for(var o=e(a),u=i.f,h=r.f,c=0;c<o.length;c++){var l=o[c];t(n,l)||s&&t(s,l)||u(n,l,h(a,l))}}}(),o=Di();return Tr=function(s,u){var h,c,l,f,g,p=s.target,d=s.global,v=s.stat;if(h=d?t:v?t[p]||n(p,{}):t[p]&&t[p].prototype)for(c in u){if(f=u[c],l=s.dontCallGetSet?(g=e(h,c))&&g.value:h[c],!o(d?c:p+(v?".":"#")+c,s.forced)&&void 0!==l){if(typeof f==typeof l)continue;a(f,l)}(s.sham||l&&l.sham)&&r(f,"sham",!0),i(h,c,f,s)}}}function Bi(){if(Cr)return Ar;Cr=1;var t=s(),e=xt(),r=ct(),i=function(t){return e.slice(0,t.length)===t};return Ar=i("Bun/")?"BUN":i("Cloudflare-Workers")?"CLOUDFLARE":i("Deno/")?"DENO":i("Node.js/")?"NODE":t.Bun&&"string"==typeof Bun.version?"BUN":t.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(t.process)?"NODE":t.window&&t.document?"BROWSER":"REST"}function zi(){if(Er)return Pr;Er=1;var t=Bi();return Pr="NODE"===t}function Ui(){if(Ir)return Vr;Ir=1;var t=function(){if(_r)return Rr;_r=1;var t=vt();return Rr=function(e){return t(e)||null===e}}(),e=String,r=TypeError;return Vr=function(i){if(t(i))return i;throw new r("Can't set "+e(i)+" as a prototype")}}function Fi(){if(Lr)return kr;Lr=1;var t=function(){if(Mr)return Nr;Mr=1;var t=ht(),e=At();return Nr=function(r,i,n){try{return t(e(Object.getOwnPropertyDescriptor(r,i)[n]))}catch(a){}}}(),e=vt(),r=gt(),i=Ui();return kr=Object.setPrototypeOf||("__proto__"in{}?function(){var n,a=!1,s={};try{(n=t(Object.prototype,"__proto__","set"))(s,[]),a=s instanceof Array}catch(o){}return function(t,s){return r(t),i(s),e(t)?(a?n(t,s):t.__proto__=s,t):t}}():void 0)}function Hi(){if(jr)return Dr;jr=1;var t=we().f,e=ie(),r=ae()("toStringTag");return Dr=function(i,n,a){i&&!a&&(i=i.prototype),i&&!e(i,r)&&t(i,r,{configurable:!0,value:n})}}function Xi(){if(Fr)return Ur;Fr=1;var t=yt(),e=function(){if(zr)return Br;zr=1;var t=Xe(),e=we();return Br=function(r,i,n){return n.get&&t(n.get,i,{getter:!0}),n.set&&t(n.set,i,{setter:!0}),e.f(r,i,n)}}(),r=ae(),i=y(),n=r("species");return Ur=function(r){var a=t(r);i&&a&&!a[n]&&e(a,n,{configurable:!0,get:function(){return this}})}}function Yi(){if($r)return qr;$r=1;var t=function(){if(Wr)return Yr;Wr=1;var t={};return t[ae()("toStringTag")]="z",Yr="[object z]"===String(t)}(),e=dt(),r=ct(),i=ae()("toStringTag"),n=Object,a="Arguments"===r(function(){return arguments}());return qr=t?r:function(t){var s,o,u;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(o=function(t,e){try{return t[e]}catch(r){}}(s=n(t),i))?o:a?r(s):"Object"===(u=r(s))&&e(s.callee)?"Arguments":u}}function Wi(){if(Kr)return Zr;Kr=1;var t=function(){if(Qr)return Gr;Qr=1;var t=ht(),e=v(),r=dt(),i=Yi(),n=yt(),a=ze(),s=function(){},o=n("Reflect","construct"),u=/^\s*(?:class|function)\b/,h=t(u.exec),c=!u.test(s),l=function(t){if(!r(t))return!1;try{return o(s,[],t),!0}catch(e){return!1}},f=function(t){if(!r(t))return!1;switch(i(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return c||!!h(u,a(t))}catch(e){return!0}};return f.sham=!0,Gr=!o||e((function(){var t;return l(l.call)||!l(Object)||!l((function(){t=!0}))||t}))?f:l}(),e=Ot(),r=TypeError;return Zr=function(i){if(t(i))return i;throw new r(e(i)+" is not a constructor")}}function qi(){if(ti)return Jr;ti=1;var t=be(),e=Wi(),r=ft(),i=ae()("species");return Jr=function(n,a){var s,o=t(n).constructor;return void 0===o||r(s=t(o)[i])?a:e(s)}}function $i(){if(ri)return ei;ri=1;var t=m(),e=Function.prototype,r=e.apply,i=e.call;return ei="object"==typeof Reflect&&Reflect.apply||(t?i.bind(r):function(){return i.apply(r,arguments)}),ei}function Gi(){if(ni)return ii;ni=1;var t=ct(),e=ht();return ii=function(r){if("Function"===t(r))return e(r)}}function Qi(){if(si)return ai;si=1;var t=Gi(),e=At(),r=m(),i=t(t.bind);return ai=function(t,n){return e(t),void 0===n?t:r?i(t,n):function(){return t.apply(n,arguments)}},ai}function Zi(){if(ui)return oi;ui=1;var t=yt();return oi=t("document","documentElement")}function Ki(){if(pi)return gi;pi=1;var t=xt();return gi=/(?:ipad|iphone|ipod).*applewebkit/i.test(t)}function Ji(){if(vi)return di;vi=1;var t,e,r,i,n=s(),a=$i(),o=Qi(),u=dt(),h=ie(),c=v(),l=Zi(),f=function(){if(ci)return hi;ci=1;var t=ht();return hi=t([].slice)}(),g=ue(),p=function(){if(fi)return li;fi=1;var t=TypeError;return li=function(e,r){if(e<r)throw new t("Not enough arguments");return e}}(),d=Ki(),y=zi(),m=n.setImmediate,x=n.clearImmediate,b=n.process,w=n.Dispatch,S=n.Function,T=n.MessageChannel,O=n.String,A=0,C={},P="onreadystatechange";c((function(){t=n.location}));var E=function(t){if(h(C,t)){var e=C[t];delete C[t],e()}},N=function(t){return function(){E(t)}},M=function(t){E(t.data)},R=function(e){n.postMessage(O(e),t.protocol+"//"+t.host)};return m&&x||(m=function(t){p(arguments.length,1);var r=u(t)?t:S(t),i=f(arguments,1);return C[++A]=function(){a(r,void 0,i)},e(A),A},x=function(t){delete C[t]},y?e=function(t){b.nextTick(N(t))}:w&&w.now?e=function(t){w.now(N(t))}:T&&!d?(i=(r=new T).port2,r.port1.onmessage=M,e=o(i.postMessage,i)):n.addEventListener&&u(n.postMessage)&&!n.importScripts&&t&&"file:"!==t.protocol&&!c(R)?(e=R,n.addEventListener("message",M,!1)):e=P in g("script")?function(t){l.appendChild(g("script"))[P]=function(){l.removeChild(this),E(t)}}:function(t){setTimeout(N(t),0)}),di={set:m,clear:x}}function tn(){if(bi)return xi;bi=1;var t=function(){this.head=null,this.tail=null};return t.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},xi=t}function en(){if(Ci)return Ai;Ci=1;var t,e,r,i,n,a=s(),o=function(){if(mi)return yi;mi=1;var t=s(),e=y(),r=Object.getOwnPropertyDescriptor;return yi=function(i){if(!e)return t[i];var n=r(t,i);return n&&n.value}}(),u=Qi(),h=Ji().set,c=tn(),l=Ki(),f=function(){if(Si)return wi;Si=1;var t=xt();return wi=/ipad|iphone|ipod/i.test(t)&&"undefined"!=typeof Pebble}(),g=function(){if(Oi)return Ti;Oi=1;var t=xt();return Ti=/web0s(?!.*chrome)/i.test(t)}(),p=zi(),d=a.MutationObserver||a.WebKitMutationObserver,v=a.document,m=a.process,x=a.Promise,b=o("queueMicrotask");if(!b){var w=new c,S=function(){var e,r;for(p&&(e=m.domain)&&e.exit();r=w.get();)try{r()}catch(i){throw w.head&&t(),i}e&&e.enter()};l||p||g||!d||!v?!f&&x&&x.resolve?((i=x.resolve(void 0)).constructor=x,n=u(i.then,i),t=function(){n(S)}):p?t=function(){m.nextTick(S)}:(h=u(h,a),t=function(){h(S)}):(e=!0,r=v.createTextNode(""),new d(S).observe(r,{characterData:!0}),t=function(){r.data=e=!e}),b=function(e){w.head||t(),w.add(e)}}return Ai=b}function rn(){return Mi?Ni:(Mi=1,Ni=function(t){try{return{error:!1,value:t()}}catch(e){return{error:!0,value:e}}})}function nn(){if(_i)return Ri;_i=1;var t=s();return Ri=t.Promise}function an(){if(Ii)return Vi;Ii=1;var t=s(),e=nn(),r=dt(),i=Di(),n=ze(),a=ae(),o=Bi(),u=Kt(),h=bt(),c=e&&e.prototype,l=a("species"),f=!1,g=r(t.PromiseRejectionEvent),p=i("Promise",(function(){var t=n(e),r=t!==String(e);if(!r&&66===h)return!0;if(u&&(!c.catch||!c.finally))return!0;if(!h||h<51||!/native code/.test(t)){var i=new e((function(t){t(1)})),a=function(t){t((function(){}),(function(){}))};if((i.constructor={})[l]=a,!(f=i.then((function(){}))instanceof a))return!0}return!(r||"BROWSER"!==o&&"DENO"!==o||g)}));return Vi={CONSTRUCTOR:p,REJECTION_EVENT:g,SUBCLASSING:f}}var sn,on,un={};function hn(){if(sn)return un;sn=1;var t=At(),e=TypeError,r=function(r){var i,n;this.promise=new r((function(t,r){if(void 0!==i||void 0!==n)throw new e("Bad Promise constructor");i=t,n=r})),this.resolve=t(i),this.reject=t(n)};return un.f=function(t){return new r(t)},un}function cn(){if(on)return a;on=1;var t,e,r,i=ji(),n=Kt(),o=zi(),u=s(),h=x(),c=Ye(),l=Fi(),f=Hi(),g=Xi(),p=At(),d=dt(),v=vt(),y=function(){if(Xr)return Hr;Xr=1;var t=mt(),e=TypeError;return Hr=function(r,i){if(t(i,r))return r;throw new e("Incorrect invocation")}}(),m=qi(),b=Ji().set,w=en(),S=Ei?Pi:(Ei=1,Pi=function(t,e){}),T=rn(),O=tn(),A=He(),C=nn(),P=an(),E=hn(),N="Promise",M=P.CONSTRUCTOR,R=P.REJECTION_EVENT,_=P.SUBCLASSING,V=A.getterFor(N),I=A.set,k=C&&C.prototype,L=C,D=k,j=u.TypeError,B=u.document,z=u.process,U=E.f,F=U,H=!!(B&&B.createEvent&&u.dispatchEvent),X="unhandledrejection",Y=function(t){var e;return!(!v(t)||!d(e=t.then))&&e},W=function(t,e){var r,i,n,a=e.value,s=1===e.state,o=s?t.ok:t.fail,u=t.resolve,c=t.reject,l=t.domain;try{o?(s||(2===e.rejection&&Z(e),e.rejection=1),!0===o?r=a:(l&&l.enter(),r=o(a),l&&(l.exit(),n=!0)),r===t.promise?c(new j("Promise-chain cycle")):(i=Y(r))?h(i,r,u,c):u(r)):c(a)}catch(f){l&&!n&&l.exit(),c(f)}},q=function(t,e){t.notified||(t.notified=!0,w((function(){for(var r,i=t.reactions;r=i.get();)W(r,t);t.notified=!1,e&&!t.rejection&&G(t)})))},$=function(t,e,r){var i,n;H?((i=B.createEvent("Event")).promise=e,i.reason=r,i.initEvent(t,!1,!0),u.dispatchEvent(i)):i={promise:e,reason:r},!R&&(n=u["on"+t])?n(i):t===X&&S("Unhandled promise rejection",r)},G=function(t){h(b,u,(function(){var e,r=t.facade,i=t.value;if(Q(t)&&(e=T((function(){o?z.emit("unhandledRejection",i,r):$(X,r,i)})),t.rejection=o||Q(t)?2:1,e.error))throw e.value}))},Q=function(t){return 1!==t.rejection&&!t.parent},Z=function(t){h(b,u,(function(){var e=t.facade;o?z.emit("rejectionHandled",e):$("rejectionhandled",e,t.value)}))},K=function(t,e,r){return function(i){t(e,i,r)}},J=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,q(t,!0))},tt=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new j("Promise can't be resolved itself");var i=Y(e);i?w((function(){var r={done:!1};try{h(i,e,K(tt,r,t),K(J,r,t))}catch(n){J(r,n,t)}})):(t.value=e,t.state=1,q(t,!1))}catch(n){J({done:!1},n,t)}}};if(M&&(D=(L=function(e){y(this,D),p(e),h(t,this);var r=V(this);try{e(K(tt,r),K(J,r))}catch(i){J(r,i)}}).prototype,(t=function(t){I(this,{type:N,done:!1,notified:!1,parent:!1,reactions:new O,rejection:!1,state:0,value:null})}).prototype=c(D,"then",(function(t,e){var r=V(this),i=U(m(this,L));return r.parent=!0,i.ok=!d(t)||t,i.fail=d(e)&&e,i.domain=o?z.domain:void 0,0===r.state?r.reactions.add(i):w((function(){W(i,r)})),i.promise})),e=function(){var e=new t,r=V(e);this.promise=e,this.resolve=K(tt,r),this.reject=K(J,r)},E.f=U=function(t){return t===L||undefined===t?new e(t):F(t)},!n&&d(C)&&k!==Object.prototype)){r=k.then,_||c(k,"then",(function(t,e){var i=this;return new L((function(t,e){h(r,i,t,e)})).then(t,e)}),{unsafe:!0});try{delete k.constructor}catch(et){}l&&l(k,D)}return i({global:!0,constructor:!0,wrap:!0,forced:M},{Promise:L}),f(L,N,!1,!0),g(N),a}var ln,fn,gn,pn,dn,vn,yn,mn,xn,bn,wn,Sn,Tn,On,An,Cn,Pn,En={};function Nn(){return fn?ln:(fn=1,ln={})}function Mn(){if(vn)return dn;vn=1;var t=Yi(),e=Ct(),r=ft(),i=Nn(),n=ae()("iterator");return dn=function(a){if(!r(a))return e(a,n)||e(a,"@@iterator")||i[t(a)]}}function Rn(){if(Sn)return wn;Sn=1;var t=Qi(),e=x(),r=be(),i=Ot(),n=function(){if(pn)return gn;pn=1;var t=ae(),e=Nn(),r=t("iterator"),i=Array.prototype;return gn=function(t){return void 0!==t&&(e.Array===t||i[r]===t)}}(),a=fr(),s=mt(),o=function(){if(mn)return yn;mn=1;var t=x(),e=At(),r=be(),i=Ot(),n=Mn(),a=TypeError;return yn=function(s,o){var u=arguments.length<2?n(s):o;if(e(u))return r(t(u,s));throw new a(i(s)+" is not iterable")},yn}(),u=Mn(),h=function(){if(bn)return xn;bn=1;var t=x(),e=be(),r=Ct();return xn=function(i,n,a){var s,o;e(i);try{if(!(s=r(i,"return"))){if("throw"===n)throw a;return a}s=t(s,i)}catch(u){o=!0,s=u}if("throw"===n)throw a;if(o)throw s;return e(s),a}}(),c=TypeError,l=function(t,e){this.stopped=t,this.result=e},f=l.prototype;return wn=function(g,p,d){var v,y,m,x,b,w,S,T=d&&d.that,O=!(!d||!d.AS_ENTRIES),A=!(!d||!d.IS_RECORD),C=!(!d||!d.IS_ITERATOR),P=!(!d||!d.INTERRUPTED),E=t(p,T),N=function(t){return v&&h(v,"normal",t),new l(!0,t)},M=function(t){return O?(r(t),P?E(t[0],t[1],N):E(t[0],t[1])):P?E(t,N):E(t)};if(A)v=g.iterator;else if(C)v=g;else{if(!(y=u(g)))throw new c(i(g)+" is not iterable");if(n(y)){for(m=0,x=a(g);x>m;m++)if((b=M(g[m]))&&s(f,b))return b;return new l(!1)}v=o(g,y)}for(w=A?g.next:v.next;!(S=e(w,v)).done;){try{b=M(S.value)}catch(R){h(v,"throw",R)}if("object"==typeof b&&b&&s(f,b))return b}return new l(!1)}}function _n(){if(Cn)return An;Cn=1;var t=nn(),e=function(){if(On)return Tn;On=1;var t=ae()("iterator"),e=!1;try{var r=0,i={next:function(){return{done:!!r++}},return:function(){e=!0}};i[t]=function(){return this},Array.from(i,(function(){throw 2}))}catch(n){}return Tn=function(r,i){try{if(!i&&!e)return!1}catch(n){return!1}var a=!1;try{var s={};s[t]=function(){return{next:function(){return{done:a=!0}}}},r(s)}catch(n){}return a}}(),r=an().CONSTRUCTOR;return An=r||!e((function(e){t.all(e).then(void 0,(function(){}))}))}var Vn,In={};var kn,Ln={};var Dn,jn={};var Bn,zn,Un,Fn,Hn={};function Xn(){if(Un)return Hn;Un=1;var t=ji(),e=yt(),r=Kt(),i=nn(),n=an().CONSTRUCTOR,a=function(){if(zn)return Bn;zn=1;var t=be(),e=vt(),r=hn();return Bn=function(i,n){if(t(i),e(n)&&n.constructor===i)return n;var a=r.f(i);return(0,a.resolve)(n),a.promise}}(),s=e("Promise"),o=r&&!n;return t({target:"Promise",stat:!0,forced:r||n},{resolve:function(t){return a(o&&this===s?i:this,t)}}),Hn}function Yn(t,e,r,i,n,a,s){try{var o=t[a](s),u=o.value}catch(h){return void r(h)}o.done?e(u):Promise.resolve(u).then(i,n)}function Wn(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var a=t.apply(e,r);function s(t){Yn(a,i,n,s,o,"next",t)}function o(t){Yn(a,i,n,s,o,"throw",t)}s(void 0)}))}}Fn||(Fn=1,cn(),function(){if(Pn)return En;Pn=1;var t=ji(),e=x(),r=At(),i=hn(),n=rn(),a=Rn();t({target:"Promise",stat:!0,forced:_n()},{all:function(t){var s=this,o=i.f(s),u=o.resolve,h=o.reject,c=n((function(){var i=r(s.resolve),n=[],o=0,c=1;a(t,(function(t){var r=o++,a=!1;c++,e(i,s,t).then((function(t){a||(a=!0,n[r]=t,--c||u(n))}),h)})),--c||u(n)}));return c.error&&h(c.value),o.promise}})}(),function(){if(Vn)return In;Vn=1;var t=ji(),e=Kt(),r=an().CONSTRUCTOR,i=nn(),n=yt(),a=dt(),s=Ye(),o=i&&i.prototype;if(t({target:"Promise",proto:!0,forced:r,real:!0},{catch:function(t){return this.then(void 0,t)}}),!e&&a(i)){var u=n("Promise").prototype.catch;o.catch!==u&&s(o,"catch",u,{unsafe:!0})}}(),function(){if(kn)return Ln;kn=1;var t=ji(),e=x(),r=At(),i=hn(),n=rn(),a=Rn();t({target:"Promise",stat:!0,forced:_n()},{race:function(t){var s=this,o=i.f(s),u=o.reject,h=n((function(){var i=r(s.resolve);a(t,(function(t){e(i,s,t).then(o.resolve,u)}))}));return h.error&&u(h.value),o.promise}})}(),function(){if(Dn)return jn;Dn=1;var t=ji(),e=hn();t({target:"Promise",stat:!0,forced:an().CONSTRUCTOR},{reject:function(t){var r=e.f(this);return(0,r.reject)(t),r.promise}})}(),Xn());var qn,$n,Gn,Qn,Zn,Kn,Jn={},ta={};function ea(){if($n)return qn;$n=1;var t=Yi(),e=String;return qn=function(r){if("Symbol"===t(r))throw new TypeError("Cannot convert a Symbol value to a string");return e(r)}}function ra(){if(Qn)return Gn;Qn=1;var t=be();return Gn=function(){var e=t(this),r="";return e.hasIndices&&(r+="d"),e.global&&(r+="g"),e.ignoreCase&&(r+="i"),e.multiline&&(r+="m"),e.dotAll&&(r+="s"),e.unicode&&(r+="u"),e.unicodeSets&&(r+="v"),e.sticky&&(r+="y"),r}}function ia(){if(Kn)return Zn;Kn=1;var t=v(),e=s().RegExp,r=t((function(){var t=e("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),i=r||t((function(){return!e("a","y").sticky})),n=r||t((function(){var t=e("^r","gy");return t.lastIndex=2,null!==t.exec("str")}));return Zn={BROKEN_CARET:n,MISSED_STICKY:i,UNSUPPORTED_Y:r}}var na,aa,sa,oa,ua,ha,ca,la,fa,ga,pa,da,va,ya,ma,xa,ba,wa,Sa,Ta,Oa,Aa={};function Ca(){if(sa)return Aa;sa=1;var t=y(),e=xe(),r=we(),i=be(),n=pt(),a=function(){if(aa)return na;aa=1;var t=pr(),e=dr();return na=Object.keys||function(r){return t(r,e)}}();return Aa.f=t&&!e?Object.defineProperties:function(t,e){i(t);for(var s,o=n(e),u=a(e),h=u.length,c=0;h>c;)r.f(t,s=u[c++],o[s]);return t},Aa}function Pa(){if(ua)return oa;ua=1;var t,e=be(),r=Ca(),i=dr(),n=Fe(),a=Zi(),s=ue(),o=Ue(),u="prototype",h="script",c=o("IE_PROTO"),l=function(){},f=function(t){return"<"+h+">"+t+"</"+h+">"},g=function(t){t.write(f("")),t.close();var e=t.parentWindow.Object;return t=null,e},p=function(){try{t=new ActiveXObject("htmlfile")}catch(c){}var e,r,n;p="undefined"!=typeof document?document.domain&&t?g(t):(r=s("iframe"),n="java"+h+":",r.style.display="none",a.appendChild(r),r.src=String(n),(e=r.contentWindow.document).open(),e.write(f("document.F=Object")),e.close(),e.F):g(t);for(var o=i.length;o--;)delete p[u][i[o]];return p()};return n[c]=!0,oa=Object.create||function(t,i){var n;return null!==t?(l[u]=e(t),n=new l,l[u]=null,n[c]=t):n=p(),void 0===i?n:r.f(n,i)}}function Ea(){if(pa)return ga;pa=1;var t,e,r=x(),i=ht(),n=ea(),a=ra(),o=ia(),u=ee(),h=Pa(),c=He().get,l=function(){if(ca)return ha;ca=1;var t=v(),e=s().RegExp;return ha=t((function(){var t=e(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)}))}(),f=function(){if(fa)return la;fa=1;var t=v(),e=s().RegExp;return la=t((function(){var t=e("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))}(),g=u("native-string-replace",String.prototype.replace),p=RegExp.prototype.exec,d=p,y=i("".charAt),m=i("".indexOf),b=i("".replace),w=i("".slice),S=(e=/b*/g,r(p,t=/a/,"a"),r(p,e,"a"),0!==t.lastIndex||0!==e.lastIndex),T=o.BROKEN_CARET,O=void 0!==/()??/.exec("")[1];return(S||O||T||l||f)&&(d=function(t){var e,i,s,o,u,l,f,v=this,x=c(v),A=n(t),C=x.raw;if(C)return C.lastIndex=v.lastIndex,e=r(d,C,A),v.lastIndex=C.lastIndex,e;var P=x.groups,E=T&&v.sticky,N=r(a,v),M=v.source,R=0,_=A;if(E&&(N=b(N,"y",""),-1===m(N,"g")&&(N+="g"),_=w(A,v.lastIndex),v.lastIndex>0&&(!v.multiline||v.multiline&&"\n"!==y(A,v.lastIndex-1))&&(M="(?: "+M+")",_=" "+_,R++),i=new RegExp("^(?:"+M+")",N)),O&&(i=new RegExp("^"+M+"$(?!\\s)",N)),S&&(s=v.lastIndex),o=r(p,E?i:v,_),E?o?(o.input=w(o.input,R),o[0]=w(o[0],R),o.index=v.lastIndex,v.lastIndex+=o[0].length):v.lastIndex=0:S&&o&&(v.lastIndex=v.global?o.index+o[0].length:s),O&&o&&o.length>1&&r(g,o[0],i,(function(){for(u=1;u<arguments.length-2;u++)void 0===arguments[u]&&(o[u]=void 0)})),o&&P)for(o.groups=l=h(null),u=0;u<P.length;u++)l[(f=P[u])[0]]=o[f[1]];return o}),ga=d}function Na(){if(ya)return va;ya=1,function(){if(da)return ta;da=1;var t=ji(),e=Ea();t({target:"RegExp",proto:!0,forced:/./.exec!==e},{exec:e})}();var t=x(),e=Ye(),r=Ea(),i=v(),n=ae(),a=Se(),s=n("species"),o=RegExp.prototype;return va=function(u,h,c,l){var f=n(u),g=!i((function(){var t={};return t[f]=function(){return 7},7!==""[u](t)})),p=g&&!i((function(){var t=!1,e=/a/;return"split"===u&&((e={}).constructor={},e.constructor[s]=function(){return e},e.flags="",e[f]=/./[f]),e.exec=function(){return t=!0,null},e[f](""),!t}));if(!g||!p||c){var d=/./[f],v=h(f,""[u],(function(e,i,n,a,s){var u=i.exec;return u===r||u===o.exec?g&&!s?{done:!0,value:t(d,i,n,a)}:{done:!0,value:t(e,n,i,a)}:{done:!1}}));e(String.prototype,u,v[0]),e(o,f,v[1])}l&&a(o[f],"sham",!0)}}function Ma(){if(wa)return ba;wa=1;var t=function(){if(xa)return ma;xa=1;var t=ht(),e=cr(),r=ea(),i=gt(),n=t("".charAt),a=t("".charCodeAt),s=t("".slice),o=function(t){return function(o,u){var h,c,l=r(i(o)),f=e(u),g=l.length;return f<0||f>=g?t?"":void 0:(h=a(l,f))<55296||h>56319||f+1===g||(c=a(l,f+1))<56320||c>57343?t?n(l,f):h:t?s(l,f,f+2):c-56320+(h-55296<<10)+65536}};return ma={codeAt:o(!1),charAt:o(!0)}}().charAt;return ba=function(e,r,i){return r+(i?t(e,r).length:1)}}function Ra(){if(Ta)return Sa;Ta=1;var t=x(),e=be(),r=dt(),i=ct(),n=Ea(),a=TypeError;return Sa=function(s,o){var u=s.exec;if(r(u)){var h=t(u,s,o);return null!==h&&e(h),h}if("RegExp"===i(s))return t(n,s,o);throw new a("RegExp#exec called on incompatible receiver")}}!function(){if(Oa)return Jn;Oa=1;var t=x(),e=Na(),r=be(),i=vt(),n=lr(),a=ea(),s=gt(),o=Ct(),u=Ma(),h=Ra();e("match",(function(e,c,l){return[function(r){var n=s(this),u=i(r)?o(r,e):void 0;return u?t(u,r,n):new RegExp(r)[e](a(n))},function(t){var e=r(this),i=a(t),s=l(c,e,i);if(s.done)return s.value;if(!e.global)return h(e,i);var o=e.unicode;e.lastIndex=0;for(var f,g=[],p=0;null!==(f=h(e,i));){var d=a(f[0]);g[p]=d,""===d&&(e.lastIndex=u(i,n(e.lastIndex),o)),p++}return 0===p?null:g}]}))}();var _a,Va,Ia,ka={};!function(){if(Ia)return ka;Ia=1;var t=$i(),e=x(),r=ht(),i=Na(),n=v(),a=be(),s=dt(),o=vt(),u=cr(),h=lr(),c=ea(),l=gt(),f=Ma(),g=Ct(),p=function(){if(Va)return _a;Va=1;var t=ht(),e=re(),r=Math.floor,i=t("".charAt),n=t("".replace),a=t("".slice),s=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,o=/\$([$&'`]|\d{1,2})/g;return _a=function(t,u,h,c,l,f){var g=h+t.length,p=c.length,d=o;return void 0!==l&&(l=e(l),d=s),n(f,d,(function(e,n){var s;switch(i(n,0)){case"$":return"$";case"&":return t;case"`":return a(u,0,h);case"'":return a(u,g);case"<":s=l[a(n,1,-1)];break;default:var o=+n;if(0===o)return e;if(o>p){var f=r(o/10);return 0===f?e:f<=p?void 0===c[f-1]?i(n,1):c[f-1]+i(n,1):e}s=c[o-1]}return void 0===s?"":s}))}}(),d=Ra(),y=ae()("replace"),m=Math.max,b=Math.min,w=r([].concat),S=r([].push),T=r("".indexOf),O=r("".slice),A="$0"==="a".replace(/./,"$0"),C=!!/./[y]&&""===/./[y]("a","$0");i("replace",(function(r,i,n){var v=C?"$":"$0";return[function(t,r){var n=l(this),a=o(t)?g(t,y):void 0;return a?e(a,t,n,r):e(i,c(n),t,r)},function(e,r){var o=a(this),l=c(e);if("string"==typeof r&&-1===T(r,v)&&-1===T(r,"$<")){var g=n(i,o,l,r);if(g.done)return g.value}var y=s(r);y||(r=c(r));var x,A=o.global;A&&(x=o.unicode,o.lastIndex=0);for(var C,P=[];null!==(C=d(o,l))&&(S(P,C),A);){""===c(C[0])&&(o.lastIndex=f(l,h(o.lastIndex),x))}for(var E,N="",M=0,R=0;R<P.length;R++){for(var _,V=c((C=P[R])[0]),I=m(b(u(C.index),l.length),0),k=[],L=1;L<C.length;L++)S(k,void 0===(E=C[L])?E:String(E));var D=C.groups;if(y){var j=w([V],k,I,l);void 0!==D&&S(j,D),_=c(t(r,void 0,j))}else _=p(V,l,I,k,D,r);I>=M&&(N+=O(l,M,I)+_,M=I+V.length)}return N+O(l,M)}]}),!!n((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!A||C)}();var La,Da,ja,Ba,za,Ua,Fa,Ha,Xa,Ya,Wa,qa,$a,Ga,Qa,Za,Ka,Ja,ts,es,rs,is,ns,as={};function ss(){if(Ba)return ja;Ba=1;var t=function(){if(Da)return La;Da=1;var t=vt(),e=ct(),r=ae()("match");return La=function(i){var n;return t(i)&&(void 0!==(n=i[r])?!!n:"RegExp"===e(i))}}(),e=TypeError;return ja=function(r){if(t(r))throw new e("The method doesn't accept regular expressions");return r}}function os(){if(Ua)return za;Ua=1;var t=ae()("match");return za=function(e){var r=/./;try{"/./"[e](r)}catch(i){try{return r[t]=!1,"/./"[e](r)}catch(n){}}return!1}}function us(){if($a)return qa;$a=1;var t=ie(),e=dt(),r=re(),i=Ue(),n=function(){if(Wa)return Ya;Wa=1;var t=v();return Ya=!t((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))}(),a=i("IE_PROTO"),s=Object,o=s.prototype;return qa=n?s.getPrototypeOf:function(i){var n=r(i);if(t(n,a))return n[a];var u=n.constructor;return e(u)&&n instanceof u?u.prototype:n instanceof s?o:null}}function hs(){if(Qa)return Ga;Qa=1;var t,e,r,i=v(),n=dt(),a=vt(),s=Pa(),o=us(),u=Ye(),h=ae(),c=Kt(),l=h("iterator"),f=!1;return[].keys&&("next"in(r=[].keys())?(e=o(o(r)))!==Object.prototype&&(t=e):f=!0),!a(t)||i((function(){var e={};return t[l].call(e)!==e}))?t={}:c&&(t=s(t)),n(t[l])||u(t,l,(function(){return this})),Ga={IteratorPrototype:t,BUGGY_SAFARI_ITERATORS:f}}function cs(){if(ts)return Ja;ts=1;var t=ji(),e=x(),r=Kt(),i=Be(),n=dt(),a=function(){if(Ka)return Za;Ka=1;var t=hs().IteratorPrototype,e=Pa(),r=ut(),i=Hi(),n=Nn(),a=function(){return this};return Za=function(s,o,u,h){var c=o+" Iterator";return s.prototype=e(t,{next:r(+!h,u)}),i(s,c,!1,!0),n[c]=a,s}}(),s=us(),o=Fi(),u=Hi(),h=Se(),c=Ye(),l=ae(),f=Nn(),g=hs(),p=i.PROPER,d=i.CONFIGURABLE,v=g.IteratorPrototype,y=g.BUGGY_SAFARI_ITERATORS,m=l("iterator"),b="keys",w="values",S="entries",T=function(){return this};return Ja=function(i,l,g,x,O,A,C){a(g,l,x);var P,E,N,M=function(t){if(t===O&&k)return k;if(!y&&t&&t in V)return V[t];switch(t){case b:case w:case S:return function(){return new g(this,t)}}return function(){return new g(this)}},R=l+" Iterator",_=!1,V=i.prototype,I=V[m]||V["@@iterator"]||O&&V[O],k=!y&&I||M(O),L="Array"===l&&V.entries||I;if(L&&(P=s(L.call(new i)))!==Object.prototype&&P.next&&(r||s(P)===v||(o?o(P,v):n(P[m])||c(P,m,T)),u(P,R,!0,!0),r&&(f[R]=T)),p&&O===w&&I&&I.name!==w&&(!r&&d?h(V,"name",w):(_=!0,k=function(){return e(I,this)})),O)if(E={values:M(w),keys:A?k:M(b),entries:M(S)},C)for(N in E)(y||_||!(N in V))&&c(V,N,E[N]);else t({target:l,proto:!0,forced:y||_},E);return r&&!C||V[m]===k||c(V,m,k,{name:O}),f[l]=k,E}}function ls(){if(ns)return is;ns=1;var t=pt(),e=function(){if(Xa)return Ha;Xa=1;var t=ae(),e=Pa(),r=we().f,i=t("unscopables"),n=Array.prototype;return void 0===n[i]&&r(n,i,{configurable:!0,value:e(null)}),Ha=function(t){n[i][t]=!0}}(),r=Nn(),i=He(),n=we().f,a=cs(),s=rs?es:(rs=1,es=function(t,e){return{value:t,done:e}}),o=Kt(),u=y(),h="Array Iterator",c=i.set,l=i.getterFor(h);is=a(Array,"Array",(function(e,r){c(this,{type:h,target:t(e),index:0,kind:r})}),(function(){var t=l(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,s(void 0,!0);switch(t.kind){case"keys":return s(r,!1);case"values":return s(e[r],!1)}return s([r,e[r]],!1)}),"values");var f=r.Arguments=r.Array;if(e("keys"),e("values"),e("entries"),!o&&u&&"values"!==f.name)try{n(f,"name",{value:"values"})}catch(g){}return is}!function(){if(Fa)return as;Fa=1;var t,e=ji(),r=Gi(),i=ce().f,n=lr(),a=ea(),s=ss(),o=gt(),u=os(),h=Kt(),c=r("".slice),l=Math.min,f=u("startsWith");e({target:"String",proto:!0,forced:!!(h||f||(t=i(String.prototype,"startsWith"),!t||t.writable))&&!f},{startsWith:function(t){var e=a(o(this));s(t);var r=n(l(arguments.length>1?arguments[1]:void 0,e.length)),i=a(t);return c(e,r,r+i.length)===i}})}(),ls();var fs,gs,ps,ds,vs,ys={};function ms(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e);if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r(e)?e:e+""}function xs(t,e,r){return(e=ms(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}!function(){if(vs)return ys;vs=1;var t=s(),e=gs?fs:(gs=1,fs={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}),r=function(){if(ds)return ps;ds=1;var t=ue()("span").classList,e=t&&t.constructor&&t.constructor.prototype;return ps=e===Object.prototype?void 0:e}(),i=ls(),n=Se(),a=Hi(),o=ae()("iterator"),u=i.values,h=function(t,r){if(t){if(t[o]!==u)try{n(t,o,u)}catch(h){t[o]=u}if(a(t,r,!0),e[r])for(var s in i)if(t[s]!==i[s])try{n(t,s,i[s])}catch(h){t[s]=i[s]}}};for(var c in e)h(t[c]&&t[c].prototype,c);h(r,"DOMTokenList")}();var bs,ws,Ss,Ts,Os,As={};function Cs(){if(Ts)return Ss;Ts=1;var t=v();return Ss=function(e,r){var i=[][e];return!!i&&t((function(){i.call(null,r||function(){return 1},1)}))}}!function(){if(Os)return As;Os=1;var t=ji(),e=function(){if(ws)return bs;ws=1;var t=At(),e=re(),r=lt(),i=fr(),n=TypeError,a="Reduce of empty array with no initial value",s=function(s){return function(o,u,h,c){var l=e(o),f=r(l),g=i(l);if(t(u),0===g&&h<2)throw new n(a);var p=s?g-1:0,d=s?-1:1;if(h<2)for(;;){if(p in f){c=f[p],p+=d;break}if(p+=d,s?p<0:g<=p)throw new n(a)}for(;s?p>=0:g>p;p+=d)p in f&&(c=u(c,f[p],p,l));return c}};return bs={left:s(!1),right:s(!0)}}().left,r=Cs(),i=bt();t({target:"Array",proto:!0,forced:!zi()&&i>79&&i<83||!r("reduce")},{reduce:function(t){var r=arguments.length;return e(this,t,r,r>1?arguments[1]:void 0)}})}();var Ps,Es={};!function(){if(Ps)return Es;Ps=1;var t,e=ji(),r=Gi(),i=ce().f,n=lr(),a=ea(),s=ss(),o=gt(),u=os(),h=Kt(),c=r("".slice),l=Math.min,f=u("endsWith");e({target:"String",proto:!0,forced:!!(h||f||(t=i(String.prototype,"endsWith"),!t||t.writable))&&!f},{endsWith:function(t){var e=a(o(this));s(t);var r=arguments.length>1?arguments[1]:void 0,i=e.length,u=void 0===r?i:l(n(r),i),h=a(t);return c(e,u-h.length,u)===h}})}();var Ns,Ms={};!function(){if(Ns)return Ms;Ns=1;var t=x(),e=ht(),r=Na(),i=be(),n=vt(),a=gt(),s=qi(),o=Ma(),u=lr(),h=ea(),c=Ct(),l=Ra(),f=ia(),g=v(),p=f.UNSUPPORTED_Y,d=Math.min,y=e([].push),m=e("".slice),b=!g((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),w="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;r("split",(function(e,r,f){var g="0".split(void 0,0).length?function(e,i){return void 0===e&&0===i?[]:t(r,this,e,i)}:r;return[function(r,i){var s=a(this),o=n(r)?c(r,e):void 0;return o?t(o,r,s,i):t(g,h(s),r,i)},function(t,e){var n=i(this),a=h(t);if(!w){var c=f(g,n,a,e,g!==r);if(c.done)return c.value}var v=s(n,RegExp),x=n.unicode,b=(n.ignoreCase?"i":"")+(n.multiline?"m":"")+(n.unicode?"u":"")+(p?"g":"y"),S=new v(p?"^(?:"+n.source+")":n,b),T=void 0===e?4294967295:e>>>0;if(0===T)return[];if(0===a.length)return null===l(S,a)?[a]:[];for(var O=0,A=0,C=[];A<a.length;){S.lastIndex=p?0:A;var P,E=l(S,p?m(a,A):a);if(null===E||(P=d(u(S.lastIndex+(p?A:0)),a.length))===O)A=o(a,A,x);else{if(y(C,m(a,O,A)),C.length===T)return C;for(var N=1;N<=E.length-1;N++)if(y(C,E[N]),C.length===T)return C;A=O=P}}return y(C,m(a,O)),C}]}),w||!b,p)}();var Rs,_s,Vs={exports:{}},Is={exports:{}},ks=Is.exports;const Ls=e(function(){if(_s)return Vs.exports;_s=1;for(var e=(Rs||(Rs=1,function(){var t,e,r,i,n,a;"undefined"!=typeof performance&&null!==performance&&performance.now?Is.exports=function(){return performance.now()}:"undefined"!=typeof process&&null!==process&&process.hrtime?(Is.exports=function(){return(t()-n)/1e6},e=process.hrtime,i=(t=function(){var t;return 1e9*(t=e())[0]+t[1]})(),a=1e9*process.uptime(),n=i-a):Date.now?(Is.exports=function(){return Date.now()-r},r=Date.now()):(Is.exports=function(){return(new Date).getTime()-r},r=(new Date).getTime())}.call(ks)),Is.exports),r="undefined"==typeof window?t:window,i=["moz","webkit"],n="AnimationFrame",a=r["request"+n],s=r["cancel"+n]||r["cancelRequest"+n],o=0;!a&&o<i.length;o++)a=r[i[o]+"Request"+n],s=r[i[o]+"Cancel"+n]||r[i[o]+"CancelRequest"+n];if(!a||!s){var u=0,h=0,c=[],l=1e3/60;a=function(t){if(0===c.length){var r=e(),i=Math.max(0,l-(r-u));u=i+r,setTimeout((function(){var t=c.slice(0);c.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(u)}catch(r){setTimeout((function(){throw r}),0)}}),Math.round(i))}return c.push({handle:++h,callback:t,cancelled:!1}),h},s=function(t){for(var e=0;e<c.length;e++)c[e].handle===t&&(c[e].cancelled=!0)}}return Vs.exports=function(t){return a.call(r,t)},Vs.exports.cancel=function(){s.apply(r,arguments)},Vs.exports.polyfill=function(t){t||(t=r),t.requestAnimationFrame=a,t.cancelAnimationFrame=s},Vs.exports}());var Ds,js,Bs,zs,Us,Fs,Hs,Xs,Ys,Ws={};function qs(){return js?Ds:(js=1,Ds="\t\n\v\f\r                　\u2028\u2029\ufeff")}!function(){if(Hs)return Ws;Hs=1;var t=ji(),e=function(){if(zs)return Bs;zs=1;var t=ht(),e=gt(),r=ea(),i=qs(),n=t("".replace),a=RegExp("^["+i+"]+"),s=RegExp("(^|[^"+i+"])["+i+"]+$"),o=function(t){return function(i){var o=r(e(i));return 1&t&&(o=n(o,a,"")),2&t&&(o=n(o,s,"$1")),o}};return Bs={start:o(1),end:o(2),trim:o(3)}}().trim;t({target:"String",proto:!0,forced:function(){if(Fs)return Us;Fs=1;var t=Be().PROPER,e=v(),r=qs();return Us=function(i){return e((function(){return!!r[i]()||"​᠎"!=="​᠎"[i]()||t&&r[i].name!==i}))}}()("trim")},{trim:function(){return e(this)}})}();const $s=e(Ys?Xs:(Ys=1,Xs=function(t){this.ok=!1,this.alpha=1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};t=e[t]||t;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseFloat(t[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<r.length;i++){var n=r[i].re,a=r[i].process,s=n.exec(t);if(s){var o=a(s);this.r=o[0],this.g=o[1],this.b=o[2],o.length>3&&(this.alpha=o[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r},this.getHelpXML=function(){for(var t=new Array,i=0;i<r.length;i++)for(var n=r[i].example,a=0;a<n.length;a++)t[t.length]=n[a];for(var s in e)t[t.length]=s;var o=document.createElement("ul");for(o.setAttribute("id","rgbcolor-examples"),i=0;i<t.length;i++)try{var u=document.createElement("li"),h=new RGBColor(t[i]),c=document.createElement("div");c.style.cssText="margin: 3px; border: 1px solid black; background:"+h.toHex()+"; color:"+h.toHex(),c.appendChild(document.createTextNode("test"));var l=document.createTextNode(" "+t[i]+" -> "+h.toRGB()+" -> "+h.toHex());u.appendChild(c),u.appendChild(l),o.appendChild(u)}catch(f){}return o}}));var Gs,Qs={};!function(){if(Gs)return Qs;Gs=1;var t=ji(),e=Gi(),r=gr().indexOf,i=Cs(),n=e([].indexOf),a=!!n&&1/n([1],1,-0)<0;t({target:"Array",proto:!0,forced:a||!i("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return a?n(this,t,e)||0:r(this,t,e)}})}();var Zs,Ks={};!function(){if(Zs)return Ks;Zs=1;var t=ji(),e=ht(),r=ss(),i=gt(),n=ea(),a=os(),s=e("".indexOf);t({target:"String",proto:!0,forced:!a("includes")},{includes:function(t){return!!~s(n(i(this)),n(r(t)),arguments.length>1?arguments[1]:void 0)}})}();var Js,to,eo,ro={};!function(){if(eo)return ro;eo=1;var t=ji(),e=ht(),r=function(){if(to)return Js;to=1;var t=ct();return Js=Array.isArray||function(e){return"Array"===t(e)}}(),i=e([].reverse),n=[1,2];t({target:"Array",proto:!0,forced:String(n)===String(n.reverse())},{reverse:function(){return r(this)&&(this.length=this.length),i(this)}})}();
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var io=function(t,e){return(io=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function no(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}io(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function ao(t,e){var r=t[0],i=t[1];return[r*Math.cos(e)-i*Math.sin(e),r*Math.sin(e)+i*Math.cos(e)]}function so(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=0;r<t.length;r++)if("number"!=typeof t[r])throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof t[r]+" == typeof "+t[r]);return!0}var oo=Math.PI;function uo(t,e,r){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var i=t.rX,n=t.rY,a=t.x,s=t.y;i=Math.abs(t.rX),n=Math.abs(t.rY);var o=ao([(e-a)/2,(r-s)/2],-t.xRot/180*oo),u=o[0],h=o[1],c=Math.pow(u,2)/Math.pow(i,2)+Math.pow(h,2)/Math.pow(n,2);1<c&&(i*=Math.sqrt(c),n*=Math.sqrt(c)),t.rX=i,t.rY=n;var l=Math.pow(i,2)*Math.pow(h,2)+Math.pow(n,2)*Math.pow(u,2),f=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(i,2)*Math.pow(n,2)-l)/l)),g=i*h/n*f,p=-n*u/i*f,d=ao([g,p],t.xRot/180*oo);t.cX=d[0]+(e+a)/2,t.cY=d[1]+(r+s)/2,t.phi1=Math.atan2((h-p)/n,(u-g)/i),t.phi2=Math.atan2((-h-p)/n,(-u-g)/i),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*oo),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*oo),t.phi1*=180/oo,t.phi2*=180/oo}function ho(t,e,r){so(t,e,r);var i=t*t+e*e-r*r;if(0>i)return[];if(0===i)return[[t*r/(t*t+e*e),e*r/(t*t+e*e)]];var n=Math.sqrt(i);return[[(t*r+e*n)/(t*t+e*e),(e*r-t*n)/(t*t+e*e)],[(t*r-e*n)/(t*t+e*e),(e*r+t*n)/(t*t+e*e)]]}var co,lo=Math.PI/180;function fo(t,e,r){return(1-r)*t+r*e}function go(t,e,r,i){return t+Math.cos(i/180*oo)*e+Math.sin(i/180*oo)*r}function po(t,e,r,i){var n=1e-6,a=e-t,s=r-e,o=3*a+3*(i-r)-6*s,u=6*(s-a),h=3*a;return Math.abs(o)<n?[-h/u]:function(t,e,r){var i=t*t/4-e;if(i<-1e-6)return[];if(i<=r)return[-t/2];var n=Math.sqrt(i);return[-t/2-n,-t/2+n]}(u/o,h/o,n)}function vo(t,e,r,i,n){var a=1-n;return t*(a*a*a)+e*(3*a*a*n)+r*(3*a*n*n)+i*(n*n*n)}!function(t){function e(){return n((function(t,e,r){return t.relative&&(void 0!==t.x1&&(t.x1+=e),void 0!==t.y1&&(t.y1+=r),void 0!==t.x2&&(t.x2+=e),void 0!==t.y2&&(t.y2+=r),void 0!==t.x&&(t.x+=e),void 0!==t.y&&(t.y+=r),t.relative=!1),t}))}function r(){var t=NaN,e=NaN,r=NaN,i=NaN;return n((function(n,a,s){return n.type&Ao.SMOOTH_CURVE_TO&&(n.type=Ao.CURVE_TO,t=isNaN(t)?a:t,e=isNaN(e)?s:e,n.x1=n.relative?a-t:2*a-t,n.y1=n.relative?s-e:2*s-e),n.type&Ao.CURVE_TO?(t=n.relative?a+n.x2:n.x2,e=n.relative?s+n.y2:n.y2):(t=NaN,e=NaN),n.type&Ao.SMOOTH_QUAD_TO&&(n.type=Ao.QUAD_TO,r=isNaN(r)?a:r,i=isNaN(i)?s:i,n.x1=n.relative?a-r:2*a-r,n.y1=n.relative?s-i:2*s-i),n.type&Ao.QUAD_TO?(r=n.relative?a+n.x1:n.x1,i=n.relative?s+n.y1:n.y1):(r=NaN,i=NaN),n}))}function i(){var t=NaN,e=NaN;return n((function(r,i,n){if(r.type&Ao.SMOOTH_QUAD_TO&&(r.type=Ao.QUAD_TO,t=isNaN(t)?i:t,e=isNaN(e)?n:e,r.x1=r.relative?i-t:2*i-t,r.y1=r.relative?n-e:2*n-e),r.type&Ao.QUAD_TO){t=r.relative?i+r.x1:r.x1,e=r.relative?n+r.y1:r.y1;var a=r.x1,s=r.y1;r.type=Ao.CURVE_TO,r.x1=((r.relative?0:i)+2*a)/3,r.y1=((r.relative?0:n)+2*s)/3,r.x2=(r.x+2*a)/3,r.y2=(r.y+2*s)/3}else t=NaN,e=NaN;return r}))}function n(t){var e=0,r=0,i=NaN,n=NaN;return function(a){if(isNaN(i)&&!(a.type&Ao.MOVE_TO))throw new Error("path must start with moveto");var s=t(a,e,r,i,n);return a.type&Ao.CLOSE_PATH&&(e=i,r=n),void 0!==a.x&&(e=a.relative?e+a.x:a.x),void 0!==a.y&&(r=a.relative?r+a.y:a.y),a.type&Ao.MOVE_TO&&(i=e,n=r),s}}function a(t,e,r,i,a,s){return so(t,e,r,i,a,s),n((function(n,o,u,h){var c=n.x1,l=n.x2,f=n.relative&&!isNaN(h),g=void 0!==n.x?n.x:f?0:o,p=void 0!==n.y?n.y:f?0:u;function d(t){return t*t}n.type&Ao.HORIZ_LINE_TO&&0!==e&&(n.type=Ao.LINE_TO,n.y=n.relative?0:u),n.type&Ao.VERT_LINE_TO&&0!==r&&(n.type=Ao.LINE_TO,n.x=n.relative?0:o),void 0!==n.x&&(n.x=n.x*t+p*r+(f?0:a)),void 0!==n.y&&(n.y=g*e+n.y*i+(f?0:s)),void 0!==n.x1&&(n.x1=n.x1*t+n.y1*r+(f?0:a)),void 0!==n.y1&&(n.y1=c*e+n.y1*i+(f?0:s)),void 0!==n.x2&&(n.x2=n.x2*t+n.y2*r+(f?0:a)),void 0!==n.y2&&(n.y2=l*e+n.y2*i+(f?0:s));var v=t*i-e*r;if(void 0!==n.xRot&&(1!==t||0!==e||0!==r||1!==i))if(0===v)delete n.rX,delete n.rY,delete n.xRot,delete n.lArcFlag,delete n.sweepFlag,n.type=Ao.LINE_TO;else{var y=n.xRot*Math.PI/180,m=Math.sin(y),x=Math.cos(y),b=1/d(n.rX),w=1/d(n.rY),S=d(x)*b+d(m)*w,T=2*m*x*(b-w),O=d(m)*b+d(x)*w,A=S*i*i-T*e*i+O*e*e,C=T*(t*i+e*r)-2*(S*r*i+O*t*e),P=S*r*r-T*t*r+O*t*t,E=(Math.atan2(C,A-P)+Math.PI)%Math.PI/2,N=Math.sin(E),M=Math.cos(E);n.rX=Math.abs(v)/Math.sqrt(A*d(M)+C*N*M+P*d(N)),n.rY=Math.abs(v)/Math.sqrt(A*d(N)-C*N*M+P*d(M)),n.xRot=180*E/Math.PI}return void 0!==n.sweepFlag&&0>v&&(n.sweepFlag=+!n.sweepFlag),n}))}t.ROUND=function(t){function e(e){return Math.round(e*t)/t}return void 0===t&&(t=1e13),so(t),function(t){return void 0!==t.x1&&(t.x1=e(t.x1)),void 0!==t.y1&&(t.y1=e(t.y1)),void 0!==t.x2&&(t.x2=e(t.x2)),void 0!==t.y2&&(t.y2=e(t.y2)),void 0!==t.x&&(t.x=e(t.x)),void 0!==t.y&&(t.y=e(t.y)),void 0!==t.rX&&(t.rX=e(t.rX)),void 0!==t.rY&&(t.rY=e(t.rY)),t}},t.TO_ABS=e,t.TO_REL=function(){return n((function(t,e,r){return t.relative||(void 0!==t.x1&&(t.x1-=e),void 0!==t.y1&&(t.y1-=r),void 0!==t.x2&&(t.x2-=e),void 0!==t.y2&&(t.y2-=r),void 0!==t.x&&(t.x-=e),void 0!==t.y&&(t.y-=r),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,e,r){return void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===r&&(r=!0),n((function(i,n,a,s,o){if(isNaN(s)&&!(i.type&Ao.MOVE_TO))throw new Error("path must start with moveto");return e&&i.type&Ao.HORIZ_LINE_TO&&(i.type=Ao.LINE_TO,i.y=i.relative?0:a),r&&i.type&Ao.VERT_LINE_TO&&(i.type=Ao.LINE_TO,i.x=i.relative?0:n),t&&i.type&Ao.CLOSE_PATH&&(i.type=Ao.LINE_TO,i.x=i.relative?s-n:s,i.y=i.relative?o-a:o),i.type&Ao.ARC&&(0===i.rX||0===i.rY)&&(i.type=Ao.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i}))},t.NORMALIZE_ST=r,t.QT_TO_C=i,t.INFO=n,t.SANITIZE=function(t){void 0===t&&(t=0),so(t);var e=NaN,r=NaN,i=NaN,a=NaN;return n((function(n,s,o,u,h){var c=Math.abs,l=!1,f=0,g=0;if(n.type&Ao.SMOOTH_CURVE_TO&&(f=isNaN(e)?0:s-e,g=isNaN(r)?0:o-r),n.type&(Ao.CURVE_TO|Ao.SMOOTH_CURVE_TO)?(e=n.relative?s+n.x2:n.x2,r=n.relative?o+n.y2:n.y2):(e=NaN,r=NaN),n.type&Ao.SMOOTH_QUAD_TO?(i=isNaN(i)?s:2*s-i,a=isNaN(a)?o:2*o-a):n.type&Ao.QUAD_TO?(i=n.relative?s+n.x1:n.x1,a=n.relative?o+n.y1:n.y2):(i=NaN,a=NaN),n.type&Ao.LINE_COMMANDS||n.type&Ao.ARC&&(0===n.rX||0===n.rY||!n.lArcFlag)||n.type&Ao.CURVE_TO||n.type&Ao.SMOOTH_CURVE_TO||n.type&Ao.QUAD_TO||n.type&Ao.SMOOTH_QUAD_TO){var p=void 0===n.x?0:n.relative?n.x:n.x-s,d=void 0===n.y?0:n.relative?n.y:n.y-o;f=isNaN(i)?void 0===n.x1?f:n.relative?n.x:n.x1-s:i-s,g=isNaN(a)?void 0===n.y1?g:n.relative?n.y:n.y1-o:a-o;var v=void 0===n.x2?0:n.relative?n.x:n.x2-s,y=void 0===n.y2?0:n.relative?n.y:n.y2-o;c(p)<=t&&c(d)<=t&&c(f)<=t&&c(g)<=t&&c(v)<=t&&c(y)<=t&&(l=!0)}return n.type&Ao.CLOSE_PATH&&c(s-u)<=t&&c(o-h)<=t&&(l=!0),l?[]:n}))},t.MATRIX=a,t.ROTATE=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),so(t,e,r);var i=Math.sin(t),n=Math.cos(t);return a(n,i,-i,n,e-e*n+r*i,r-e*i-r*n)},t.TRANSLATE=function(t,e){return void 0===e&&(e=0),so(t,e),a(1,0,0,1,t,e)},t.SCALE=function(t,e){return void 0===e&&(e=t),so(t,e),a(t,0,0,e,0,0)},t.SKEW_X=function(t){return so(t),a(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return so(t),a(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),so(t),a(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),so(t),a(1,0,0,-1,0,t)},t.A_TO_C=function(){return n((function(t,e,r){return Ao.ARC===t.type?function(t,e,r){var i,n,a,s;t.cX||uo(t,e,r);for(var o=Math.min(t.phi1,t.phi2),u=Math.max(t.phi1,t.phi2)-o,h=Math.ceil(u/90),c=new Array(h),l=e,f=r,g=0;g<h;g++){var p=fo(t.phi1,t.phi2,g/h),d=fo(t.phi1,t.phi2,(g+1)/h),v=d-p,y=4/3*Math.tan(v*lo/4),m=[Math.cos(p*lo)-y*Math.sin(p*lo),Math.sin(p*lo)+y*Math.cos(p*lo)],x=m[0],b=m[1],w=[Math.cos(d*lo),Math.sin(d*lo)],S=w[0],T=w[1],O=[S+y*Math.sin(d*lo),T-y*Math.cos(d*lo)],A=O[0],C=O[1];c[g]={relative:t.relative,type:Ao.CURVE_TO};var P=function(e,r){var i=ao([e*t.rX,r*t.rY],t.xRot),n=i[0],a=i[1];return[t.cX+n,t.cY+a]};i=P(x,b),c[g].x1=i[0],c[g].y1=i[1],n=P(A,C),c[g].x2=n[0],c[g].y2=n[1],a=P(S,T),c[g].x=a[0],c[g].y=a[1],t.relative&&(c[g].x1-=l,c[g].y1-=f,c[g].x2-=l,c[g].y2-=f,c[g].x-=l,c[g].y-=f),l=(s=[c[g].x,c[g].y])[0],f=s[1]}return c}(t,t.relative?0:e,t.relative?0:r):t}))},t.ANNOTATE_ARCS=function(){return n((function(t,e,r){return t.relative&&(e=0,r=0),Ao.ARC===t.type&&uo(t,e,r),t}))},t.CLONE=function(){return function(t){var e={};for(var r in t)e[r]=t[r];return e}},t.CALCULATE_BOUNDS=function(){var t=e(),a=i(),s=r(),o=n((function(e,r,i){var n=s(a(t(function(t){var e={};for(var r in t)e[r]=t[r];return e}(e))));function u(t){t>o.maxX&&(o.maxX=t),t<o.minX&&(o.minX=t)}function h(t){t>o.maxY&&(o.maxY=t),t<o.minY&&(o.minY=t)}if(n.type&Ao.DRAWING_COMMANDS&&(u(r),h(i)),n.type&Ao.HORIZ_LINE_TO&&u(n.x),n.type&Ao.VERT_LINE_TO&&h(n.y),n.type&Ao.LINE_TO&&(u(n.x),h(n.y)),n.type&Ao.CURVE_TO){u(n.x),h(n.y);for(var c=0,l=po(r,n.x1,n.x2,n.x);c<l.length;c++)0<(P=l[c])&&1>P&&u(vo(r,n.x1,n.x2,n.x,P));for(var f=0,g=po(i,n.y1,n.y2,n.y);f<g.length;f++)0<(P=g[f])&&1>P&&h(vo(i,n.y1,n.y2,n.y,P))}if(n.type&Ao.ARC){u(n.x),h(n.y),uo(n,r,i);for(var p=n.xRot/180*Math.PI,d=Math.cos(p)*n.rX,v=Math.sin(p)*n.rX,y=-Math.sin(p)*n.rY,m=Math.cos(p)*n.rY,x=n.phi1<n.phi2?[n.phi1,n.phi2]:-180>n.phi2?[n.phi2+360,n.phi1+360]:[n.phi2,n.phi1],b=x[0],w=x[1],S=function(t){var e=t[0],r=t[1],i=180*Math.atan2(r,e)/Math.PI;return i<b?i+360:i},T=0,O=ho(y,-d,0).map(S);T<O.length;T++)(P=O[T])>b&&P<w&&u(go(n.cX,d,y,P));for(var A=0,C=ho(m,-v,0).map(S);A<C.length;A++){var P;(P=C[A])>b&&P<w&&h(go(n.cY,v,m,P))}}return e}));return o.minX=1/0,o.maxX=-1/0,o.minY=1/0,o.maxY=-1/0,o}}(co||(co={}));var yo,mo,xo,bo,wo=function(){function t(){}return t.prototype.round=function(t){return this.transform(co.ROUND(t))},t.prototype.toAbs=function(){return this.transform(co.TO_ABS())},t.prototype.toRel=function(){return this.transform(co.TO_REL())},t.prototype.normalizeHVZ=function(t,e,r){return this.transform(co.NORMALIZE_HVZ(t,e,r))},t.prototype.normalizeST=function(){return this.transform(co.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(co.QT_TO_C())},t.prototype.aToC=function(){return this.transform(co.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(co.SANITIZE(t))},t.prototype.translate=function(t,e){return this.transform(co.TRANSLATE(t,e))},t.prototype.scale=function(t,e){return this.transform(co.SCALE(t,e))},t.prototype.rotate=function(t,e,r){return this.transform(co.ROTATE(t,e,r))},t.prototype.matrix=function(t,e,r,i,n,a){return this.transform(co.MATRIX(t,e,r,i,n,a))},t.prototype.skewX=function(t){return this.transform(co.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(co.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(co.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(co.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(co.ANNOTATE_ARCS())},t}(),So=function(t){return" "===t||"\t"===t||"\r"===t||"\n"===t},To=function(t){return"0".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<="9".charCodeAt(0)},Oo=function(t){function e(){var e=t.call(this)||this;return e.curNumber="",e.curCommandType=-1,e.curCommandRelative=!1,e.canParseCommandOrComma=!0,e.curNumberHasExp=!1,e.curNumberHasExpDigits=!1,e.curNumberHasDecimal=!1,e.curArgs=[],e}return no(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(" ",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return t},e.prototype.parse=function(t,e){var r=this;void 0===e&&(e=[]);for(var i=function(t){e.push(t),r.curArgs.length=0,r.canParseCommandOrComma=!0},n=0;n<t.length;n++){var a=t[n],s=!(this.curCommandType!==Ao.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||"0"!==this.curNumber&&"1"!==this.curNumber),o=To(a)&&("0"===this.curNumber&&"0"===a||s);if(!To(a)||o)if("e"!==a&&"E"!==a)if("-"!==a&&"+"!==a||!this.curNumberHasExp||this.curNumberHasExpDigits)if("."!==a||this.curNumberHasExp||this.curNumberHasDecimal||s){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw new SyntaxError("Invalid number ending at "+n);if(this.curCommandType===Ao.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw new SyntaxError('Expected positive number, got "'+u+'" at index "'+n+'"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&"0"!==this.curNumber&&"1"!==this.curNumber)throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+n+'"');this.curArgs.push(u),this.curArgs.length===Co[this.curCommandType]&&(Ao.HORIZ_LINE_TO===this.curCommandType?i({type:Ao.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):Ao.VERT_LINE_TO===this.curCommandType?i({type:Ao.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===Ao.MOVE_TO||this.curCommandType===Ao.LINE_TO||this.curCommandType===Ao.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),Ao.MOVE_TO===this.curCommandType&&(this.curCommandType=Ao.LINE_TO)):this.curCommandType===Ao.CURVE_TO?i({type:Ao.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===Ao.SMOOTH_CURVE_TO?i({type:Ao.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===Ao.QUAD_TO?i({type:Ao.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===Ao.ARC&&i({type:Ao.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!So(a))if(","===a&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if("+"!==a&&"-"!==a&&"."!==a)if(o)this.curNumber=a,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError("Unterminated command at index "+n+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,"z"!==a&&"Z"!==a)if("h"===a||"H"===a)this.curCommandType=Ao.HORIZ_LINE_TO,this.curCommandRelative="h"===a;else if("v"===a||"V"===a)this.curCommandType=Ao.VERT_LINE_TO,this.curCommandRelative="v"===a;else if("m"===a||"M"===a)this.curCommandType=Ao.MOVE_TO,this.curCommandRelative="m"===a;else if("l"===a||"L"===a)this.curCommandType=Ao.LINE_TO,this.curCommandRelative="l"===a;else if("c"===a||"C"===a)this.curCommandType=Ao.CURVE_TO,this.curCommandRelative="c"===a;else if("s"===a||"S"===a)this.curCommandType=Ao.SMOOTH_CURVE_TO,this.curCommandRelative="s"===a;else if("q"===a||"Q"===a)this.curCommandType=Ao.QUAD_TO,this.curCommandRelative="q"===a;else if("t"===a||"T"===a)this.curCommandType=Ao.SMOOTH_QUAD_TO,this.curCommandRelative="t"===a;else{if("a"!==a&&"A"!==a)throw new SyntaxError('Unexpected character "'+a+'" at index '+n+".");this.curCommandType=Ao.ARC,this.curCommandRelative="a"===a}else e.push({type:Ao.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=a,this.curNumberHasDecimal="."===a}else this.curNumber+=a,this.curNumberHasDecimal=!0;else this.curNumber+=a;else this.curNumber+=a,this.curNumberHasExp=!0;else this.curNumber+=a,this.curNumberHasExpDigits=this.curNumberHasExp}return e},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(e,r){void 0===r&&(r=[]);for(var i=0,n=Object.getPrototypeOf(this).parse.call(this,e);i<n.length;i++){var a=n[i],s=t(a);Array.isArray(s)?r.push.apply(r,s):r.push(s)}return r}}})},e}(wo),Ao=function(t){function e(r){var i=t.call(this)||this;return i.commands="string"==typeof r?e.parse(r):r,i}return no(e,t),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var t=co.CALCULATE_BOUNDS();return this.transform(t),t},e.prototype.transform=function(t){for(var e=[],r=0,i=this.commands;r<i.length;r++){var n=t(i[r]);Array.isArray(n)?e.push.apply(e,n):e.push(n)}return this.commands=e,this},e.encode=function(t){return function(t){var e="";Array.isArray(t)||(t=[t]);for(var r=0;r<t.length;r++){var i=t[r];if(i.type===Ao.CLOSE_PATH)e+="z";else if(i.type===Ao.HORIZ_LINE_TO)e+=(i.relative?"h":"H")+i.x;else if(i.type===Ao.VERT_LINE_TO)e+=(i.relative?"v":"V")+i.y;else if(i.type===Ao.MOVE_TO)e+=(i.relative?"m":"M")+i.x+" "+i.y;else if(i.type===Ao.LINE_TO)e+=(i.relative?"l":"L")+i.x+" "+i.y;else if(i.type===Ao.CURVE_TO)e+=(i.relative?"c":"C")+i.x1+" "+i.y1+" "+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===Ao.SMOOTH_CURVE_TO)e+=(i.relative?"s":"S")+i.x2+" "+i.y2+" "+i.x+" "+i.y;else if(i.type===Ao.QUAD_TO)e+=(i.relative?"q":"Q")+i.x1+" "+i.y1+" "+i.x+" "+i.y;else if(i.type===Ao.SMOOTH_QUAD_TO)e+=(i.relative?"t":"T")+i.x+" "+i.y;else{if(i.type!==Ao.ARC)throw new Error('Unexpected command type "'+i.type+'" at index '+r+".");e+=(i.relative?"a":"A")+i.rX+" "+i.rY+" "+i.xRot+" "+ +i.lArcFlag+" "+ +i.sweepFlag+" "+i.x+" "+i.y}}return e}(t)},e.parse=function(t){var e=new Oo,r=[];return e.parse(t,r),e.finish(r),r},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(wo),Co=((yo={})[Ao.MOVE_TO]=2,yo[Ao.LINE_TO]=2,yo[Ao.HORIZ_LINE_TO]=1,yo[Ao.VERT_LINE_TO]=1,yo[Ao.CLOSE_PATH]=0,yo[Ao.QUAD_TO]=4,yo[Ao.SMOOTH_QUAD_TO]=2,yo[Ao.CURVE_TO]=6,yo[Ao.SMOOTH_CURVE_TO]=4,yo[Ao.ARC]=7,yo),Po={};function Eo(t){return(Eo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(){if(bo)return Po;bo=1;var t=Be().PROPER,e=Ye(),r=be(),i=ea(),n=v(),a=function(){if(xo)return mo;xo=1;var t=x(),e=ie(),r=mt(),i=ra(),n=RegExp.prototype;return mo=function(a){var s=a.flags;return void 0!==s||"flags"in n||e(a,"flags")||!r(n,a)?s:t(i,a)}}(),s="toString",o=RegExp.prototype,u=o[s],h=n((function(){return"/a/b"!==u.call({source:"a",flags:"b"})})),c=t&&u.name!==s;(h||c)&&e(o,s,(function(){var t=r(this);return"/"+i(t.source)+"/"+i(a(t))}),{unsafe:!0})}();var No=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],Mo=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function Ro(t,e,r,i,n,a){if(!(isNaN(a)||a<1)){a|=0;var s=function(t,e,r,i,n){if("string"==typeof t&&(t=document.getElementById(t)),!t||"object"!==Eo(t)||!("getContext"in t))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var a=t.getContext("2d");try{return a.getImageData(e,r,i,n)}catch(s){throw new Error("unable to access image data: "+s)}}(t,e,r,i,n);s=function(t,e,r,i,n,a){for(var s,o=t.data,u=2*a+1,h=i-1,c=n-1,l=a+1,f=l*(l+1)/2,g=new _o,p=g,d=1;d<u;d++)p=p.next=new _o,d===l&&(s=p);p.next=g;for(var v=null,y=null,m=0,x=0,b=No[a],w=Mo[a],S=0;S<n;S++){p=g;for(var T=o[x],O=o[x+1],A=o[x+2],C=o[x+3],P=0;P<l;P++)p.r=T,p.g=O,p.b=A,p.a=C,p=p.next;for(var E=0,N=0,M=0,R=0,_=l*T,V=l*O,I=l*A,k=l*C,L=f*T,D=f*O,j=f*A,B=f*C,z=1;z<l;z++){var U=x+((h<z?h:z)<<2),F=o[U],H=o[U+1],X=o[U+2],Y=o[U+3],W=l-z;L+=(p.r=F)*W,D+=(p.g=H)*W,j+=(p.b=X)*W,B+=(p.a=Y)*W,E+=F,N+=H,M+=X,R+=Y,p=p.next}v=g,y=s;for(var q=0;q<i;q++){var $=B*b>>>w;if(o[x+3]=$,0!==$){var G=255/$;o[x]=(L*b>>>w)*G,o[x+1]=(D*b>>>w)*G,o[x+2]=(j*b>>>w)*G}else o[x]=o[x+1]=o[x+2]=0;L-=_,D-=V,j-=I,B-=k,_-=v.r,V-=v.g,I-=v.b,k-=v.a;var Q=q+a+1;Q=m+(Q<h?Q:h)<<2,L+=E+=v.r=o[Q],D+=N+=v.g=o[Q+1],j+=M+=v.b=o[Q+2],B+=R+=v.a=o[Q+3],v=v.next;var Z=y,K=Z.r,J=Z.g,tt=Z.b,et=Z.a;_+=K,V+=J,I+=tt,k+=et,E-=K,N-=J,M-=tt,R-=et,y=y.next,x+=4}m+=i}for(var rt=0;rt<i;rt++){var it=o[x=rt<<2],nt=o[x+1],at=o[x+2],st=o[x+3],ot=l*it,ut=l*nt,ht=l*at,ct=l*st,lt=f*it,ft=f*nt,gt=f*at,pt=f*st;p=g;for(var dt=0;dt<l;dt++)p.r=it,p.g=nt,p.b=at,p.a=st,p=p.next;for(var vt=i,yt=0,mt=0,xt=0,bt=0,wt=1;wt<=a;wt++){x=vt+rt<<2;var St=l-wt;lt+=(p.r=it=o[x])*St,ft+=(p.g=nt=o[x+1])*St,gt+=(p.b=at=o[x+2])*St,pt+=(p.a=st=o[x+3])*St,bt+=it,yt+=nt,mt+=at,xt+=st,p=p.next,wt<c&&(vt+=i)}x=rt,v=g,y=s;for(var Tt=0;Tt<n;Tt++){var Ot=x<<2;o[Ot+3]=st=pt*b>>>w,st>0?(st=255/st,o[Ot]=(lt*b>>>w)*st,o[Ot+1]=(ft*b>>>w)*st,o[Ot+2]=(gt*b>>>w)*st):o[Ot]=o[Ot+1]=o[Ot+2]=0,lt-=ot,ft-=ut,gt-=ht,pt-=ct,ot-=v.r,ut-=v.g,ht-=v.b,ct-=v.a,Ot=rt+((Ot=Tt+l)<c?Ot:c)*i<<2,lt+=bt+=v.r=o[Ot],ft+=yt+=v.g=o[Ot+1],gt+=mt+=v.b=o[Ot+2],pt+=xt+=v.a=o[Ot+3],v=v.next,ot+=it=y.r,ut+=nt=y.g,ht+=at=y.b,ct+=st=y.a,bt-=it,yt-=nt,mt-=at,xt-=st,y=y.next,x+=i}}return t}(s,0,0,i,n,a),t.getContext("2d").putImageData(s,e,r)}}var _o=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};var Vo=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>Wn((function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)}))()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function Io(t){return t.replace(/(?!\u3000)\s+/gm," ")}function ko(t){return t.replace(/^[\n \t]+/,"")}function Lo(t){return t.replace(/[\n \t]+$/,"")}function Do(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var jo=/^[A-Z-]+$/;function Bo(t){return jo.test(t)?t.toLowerCase():t}function zo(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function Uo(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,((t,r)=>e--&&r?String(Math.round(parseFloat(t))):t))}var Fo=/(\[[^\]]+\])/g,Ho=/(#[^\s+>~.[:]+)/g,Xo=/(\.[^\s+>~.[:]+)/g,Yo=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,Wo=/(:[\w-]+\([^)]*\))/gi,qo=/(:[^\s+>~.[:]+)/g,$o=/([^\s+>~.[:]+)/g;function Go(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function Qo(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=Go(r,Fo),e[1]+=i,[r,i]=Go(r,Ho),e[0]+=i,[r,i]=Go(r,Xo),e[1]+=i,[r,i]=Go(r,Yo),e[2]+=i,[r,i]=Go(r,Wo),e[1]+=i,[r,i]=Go(r,qo),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=Go(r,$o),e[2]+=i,e.join("")}var Zo=1e-8;function Ko(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function Jo(t,e){return(t[0]*e[0]+t[1]*e[1])/(Ko(t)*Ko(e))}function tu(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Jo(t,e))}function eu(t){return t*t*t}function ru(t){return 3*t*t*(1-t)}function iu(t){return 3*t*(1-t)*(1-t)}function nu(t){return(1-t)*(1-t)*(1-t)}function au(t){return t*t}function su(t){return 2*t*(1-t)}function ou(t){return(1-t)*(1-t)}class uu{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new uu(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return Io(this.getString()).trim().split(t).map((t=>new uu(e,r,t)))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=Uo(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"==typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var a=this.getNumber();return e&&a<1?a*n.computeSize(r):a}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?uu.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r&&(","===e[n]&&i++,3!==i);n++);if(t.hasValue()&&this.isString()&&3!==i){var a=new $s(e);a.ok&&(a.alpha=t.getNumber(),e=a.toRGBA())}return new uu(this.document,this.name,e)}}uu.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class hu{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class cu{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=Do(t);return new cu(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=Do(t);return new cu(r,i)}static parsePath(t){for(var e=Do(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new cu(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class lu{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach(((t,e)=>{for(var{run:i}=t,n=r[e];n;)i(n),n=n.parent})),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(a,s)&&(i[n]=t)}))}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach(((r,n)=>{var{x:a,y:s}=r;!i[n]&&e.isPointInBox(a,s)&&(i[n]=t)}))}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,n=new cu(t,e),a=i.canvas;a;)n.x-=a.offsetLeft,n.y-=a.offsetTop,a=a.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var fu="undefined"!=typeof window?window:null,gu="undefined"!=typeof fetch?fetch.bind(void 0):null;class pu{constructor(t){var{fetch:e=gu,window:r=fu}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new hu,this.mouse=new lu(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every((t=>t()));return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:a,height:s,desiredHeight:o,minX:u=0,minY:h=0,refX:c,refY:l,clip:f=!1,clipX:g=0,clipY:p=0}=t,d=Io(i).replace(/^defer\s/,""),[v,y]=d.split(" "),m=v||"xMidYMid",x=y||"meet",b=n/a,w=s/o,S=Math.min(b,w),T=Math.max(b,w),O=a,A=o;"meet"===x&&(O*=S,A*=S),"slice"===x&&(O*=T,A*=T);var C=new uu(e,"refX",c),P=new uu(e,"refY",l),E=C.hasValue()&&P.hasValue();if(E&&r.translate(-S*C.getPixels("x"),-S*P.getPixels("y")),f){var N=S*g,M=S*p;r.beginPath(),r.moveTo(N,M),r.lineTo(n,M),r.lineTo(n,s),r.lineTo(N,s),r.closePath(),r.clip()}if(!E){var R="meet"===x&&S===w,_="slice"===x&&T===w,V="meet"===x&&S===b,I="slice"===x&&T===b;m.startsWith("xMid")&&(R||_)&&r.translate(n/2-O/2,0),m.endsWith("YMid")&&(V||I)&&r.translate(0,s/2-A/2),m.startsWith("xMax")&&(R||_)&&r.translate(n-O,0),m.endsWith("YMax")&&(V||I)&&r.translate(0,s-A)}switch(!0){case"none"===m:r.scale(b,w);break;case"meet"===x:r.scale(S,S);break;case"slice"===x:r.scale(T,T)}r.translate(-u,-h)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:a=!1,forceRedraw:s,scaleWidth:o,scaleHeight:u,offsetX:h,offsetY:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:l,mouse:f}=this,g=1e3/l;if(this.frameDuration=g,this.readyPromise=new Promise((t=>{this.resolveReady=t})),this.isReady()&&this.render(t,n,a,o,u,h,c),e){var p=Date.now(),d=p,v=0,y=()=>{p=Date.now(),(v=p-d)>=g&&(d=p-v%g,this.shouldUpdate(i,s)&&(this.render(t,n,a,o,u,h,c),f.runEvents())),this.intervalId=Ls(y)};r||f.start(),this.intervalId=Ls(y)}}stop(){this.intervalId&&(Ls.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce(((t,e)=>e.update(r)||t),!1))return!0}return!("function"!=typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,r,i,n,a,s){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:h,ctx:c,isFirstRender:l}=this,f=c.canvas;h.clear(),f.width&&f.height?h.setCurrent(f.width,f.height):h.setCurrent(o,u);var g=t.getStyle("width"),p=t.getStyle("height");!e&&(l||"number"!=typeof i&&"number"!=typeof n)&&(g.hasValue()&&(f.width=g.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),p.hasValue()&&(f.height=p.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&g.hasValue()&&p.hasValue()&&(d=g.getPixels("x"),v=p.getPixels("y")),h.setCurrent(d,v),"number"==typeof a&&t.getAttribute("x",!0).setValue(a),"number"==typeof s&&t.getAttribute("y",!0).setValue(s),"number"==typeof i||"number"==typeof n){var y=Do(t.getAttribute("viewBox").getString()),m=0,x=0;if("number"==typeof i){var b=t.getStyle("width");b.hasValue()?m=b.getPixels("x")/i:isNaN(y[2])||(m=y[2]/i)}if("number"==typeof n){var w=t.getStyle("height");w.hasValue()?x=w.getPixels("y")/n:isNaN(y[3])||(x=y[3]/n)}m||(m=x),x||(x=m),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var S=t.getStyle("transform",!0,!0);S.setValue("".concat(S.getString()," scale(").concat(1/m,", ").concat(1/x,")"))}r||c.clearRect(0,0,d,v),t.render(c),l&&(this.isFirstRender=!1)}}pu.defaultWindow=fu,pu.defaultFetch=gu;var{defaultFetch:du}=pu,vu="undefined"!=typeof DOMParser?DOMParser:null;class yu{constructor(){var{fetch:t=du,DOMParser:e=vu}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return Wn((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return Wn((function*(){var r=yield e.fetch(t),i=yield r.text();return e.parseFromString(i)}))()}}class mu{constructor(t,e){this.type="translate",this.point=null,this.point=cu.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class xu{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=Do(e);this.angle=new uu(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(a.getRadians()),t.translate(-s,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:a}=this,s=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(s,o),t.rotate(-1*a.getRadians()),t.translate(-s,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class bu{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=cu.parseScale(e);0!==i.x&&0!==i.y||(i.x=Zo,i.y=Zo),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(e,r||e),t.translate(-a,-s)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,a=i.getPixels("x"),s=n.getPixels("y");t.translate(a,s),t.scale(1/e,1/r||e),t.translate(-a,-s)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class wu{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=Do(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),a=r.getPixels("y");t.translate(n,a),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-a)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],a=i[2],s=i[4],o=i[1],u=i[3],h=i[5],c=1/(n*(1*u-0*h)-a*(1*o-0*h)+s*(0*o-0*u)),l=e.getPixels("x"),f=r.getPixels("y");t.translate(l,f),t.transform(c*(1*u-0*h),c*(0*h-1*o),c*(0*s-1*a),c*(1*n-0*s),c*(a*h-s*u),c*(s*o-n*h)),t.translate(-l,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}class Su extends wu{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new uu(t,"angle",e)}}class Tu extends Su{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class Ou extends Su{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class Au{constructor(t,e,r){this.document=t,this.transforms=[];var i=function(t){return Io(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e);i.forEach((t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),n=Au.transformTypes[e];void 0!==n&&this.transforms.push(new n(this.document,i,r))}}))}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split(),a=[i,n];return r.hasValue()?new Au(t,r.getString(),a):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}Au.transformTypes={translate:mu,rotate:xu,scale:bu,matrix:wu,skewX:Tu,skewY:Ou};class Cu{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach((e=>{var r=Bo(e.nodeName);this.attributes[r]=new uu(t,r,e.value)})),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue())this.getAttribute("style").getString().split(";").map((t=>t.trim())).forEach((e=>{if(e){var[r,i]=e.split(":").map((t=>t.trim()));this.styles[r]=new uu(t,r,i)}}));var{definitions:i}=t,n=this.getAttribute("id");n.hasValue()&&(i[n.getString()]||(i[n.getString()]=this)),Array.from(e.childNodes).forEach((e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}}))}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new uu(this.document,t,"");return this.attributes[t]=i,i}return r||uu.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return uu.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!=n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:a}=this;if(a){var s=a.getStyle(t);if(null!=s&&s.hasValue())return s}}if(e){var o=new uu(this.document,t,"");return this.styles[t]=o,o}return i||uu.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=Au.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach((e=>{e.render(t)}))}addChild(t){var e=t instanceof Cu?t:this.document.createElement(t);e.parent=this,Cu.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some((e=>".".concat(e)===t))}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var a in i){var s=this.stylesSpecificity[a];void 0===s&&(s="000"),n>=s&&(this.styles[a]=i[a],this.stylesSpecificity[a]=n)}}}removeStyles(t,e){return e.reduce(((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]}),[])}restoreStyles(t,e){e.forEach((e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)}))}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}Cu.ignoreChildTypes=["title"];class Pu extends Cu{constructor(t,e,r){super(t,e,r)}}function Eu(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function Nu(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function Mu(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class Ru{constructor(t,e,r,i,n,a){var s=a?"string"==typeof a?Ru.parse(a):a:{};this.fontFamily=n||s.fontFamily,this.fontSize=i||s.fontSize,this.fontStyle=t||s.fontStyle,this.fontWeight=r||s.fontWeight,this.fontVariant=e||s.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",r="",i="",n="",a="",s=Io(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return s.forEach((t=>{switch(!0){case!o.fontStyle&&Ru.styles.includes(t):"inherit"!==t&&(e=t),o.fontStyle=!0;break;case!o.fontVariant&&Ru.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&Ru.weights.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([n]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(a+=t)}})),new Ru(e,r,i,n,a,t)}toString(){return[Nu(this.fontStyle),this.fontVariant,Mu(this.fontWeight),this.fontSize,(t=this.fontFamily,"undefined"==typeof process?t:t.trim().split(",").map(Eu).join(","))].join(" ").trim();var t}}Ru.styles="normal|italic|oblique|inherit",Ru.variants="normal|small-caps|inherit",Ru.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class _u{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var a=6*e-12*r+6*i,s=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0!==s){var u=Math.pow(a,2)-4*o*s;if(!(u<0)){var h=(-a+Math.sqrt(u))/(2*s);0<h&&h<1&&(t?this.addX(this.sumCubic(h,e,r,i,n)):this.addY(this.sumCubic(h,e,r,i,n)));var c=(-a-Math.sqrt(u))/(2*s);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)))}}else{if(0===a)return;var l=-o/a;0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,a,s,o){this.addPoint(t,e),this.addPoint(s,o),this.bezierCurveAdd(!0,t,r,n,s),this.bezierCurveAdd(!1,e,i,a,o)}addQuadraticCurve(t,e,r,i,n,a){var s=t+2/3*(r-t),o=e+2/3*(i-e),u=s+1/3*(n-t),h=o+1/3*(a-e);this.addBezierCurve(t,e,s,u,o,h,n,a)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:a}=this;return r<=t&&t<=n&&i<=e&&e<=a}}class Vu extends Ao{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new cu(0,0),this.control=new cu(0,0),this.current=new cu(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new cu(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==Ao.CURVE_TO&&t!==Ao.SMOOTH_CURVE_TO&&t!==Ao.QUAD_TO&&t!==Ao.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this;return new cu(2*e-i,2*r-n)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class Iu extends Cu{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),a=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var s=r.getFillStyleDefinition(this,i);s&&(t.fillStyle=s)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var u=new uu(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=u}if(n.isUrlDefinition()){var h=n.getFillStyleDefinition(this,a);h&&(t.strokeStyle=h)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var c=n.getString();"inherit"!==c&&(t.strokeStyle="none"===c?"rgba(0,0,0,0)":c)}if(a.hasValue()){var l=new uu(this.document,"stroke",t.strokeStyle).addOpacity(a).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");if(f.hasValue()){var g=f.getPixels();t.lineWidth=g||Zo}var p=this.getStyle("stroke-linecap"),d=this.getStyle("stroke-linejoin"),v=this.getStyle("stroke-miterlimit"),y=this.getStyle("stroke-dasharray"),m=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),d.hasValue()&&(t.lineJoin=d.getString()),v.hasValue()&&(t.miterLimit=v.getNumber()),y.hasValue()&&"none"!==y.getString()){var x=Do(y.getString());void 0!==t.setLineDash?t.setLineDash(x):void 0!==t.webkitLineDash?t.webkitLineDash=x:void 0===t.mozDash||1===x.length&&0===x[0]||(t.mozDash=x);var b=m.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=b:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=b:void 0!==t.mozDashOffset&&(t.mozDashOffset=b)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var w=this.getStyle("font"),S=this.getStyle("font-style"),T=this.getStyle("font-variant"),O=this.getStyle("font-weight"),A=this.getStyle("font-size"),C=this.getStyle("font-family"),P=new Ru(S.getString(),T.getString(),O.getString(),A.hasValue()?"".concat(A.getPixels(!0),"px"):"",C.getString(),Ru.parse(w.getString(),t.font));S.setValue(P.fontStyle),T.setValue(P.fontVariant),O.setValue(P.fontWeight),A.setValue(P.fontSize),C.setValue(P.fontFamily),t.font=P.toString(),A.isPixels()&&(this.document.emSize=A.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class ku extends Iu{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new Vu(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new _u;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case Vu.MOVE_TO:this.pathM(t,r);break;case Vu.LINE_TO:this.pathL(t,r);break;case Vu.HORIZ_LINE_TO:this.pathH(t,r);break;case Vu.VERT_LINE_TO:this.pathV(t,r);break;case Vu.CURVE_TO:this.pathC(t,r);break;case Vu.SMOOTH_CURVE_TO:this.pathS(t,r);break;case Vu.QUAD_TO:this.pathQ(t,r);break;case Vu.SMOOTH_QUAD_TO:this.pathT(t,r);break;case Vu.ARC:this.pathA(t,r);break;case Vu.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map(((t,e)=>[t,r[e]]))}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),a=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[u,h]=r[0];o.render(t,u,h)}if(a.isUrlDefinition())for(var c=a.getDefinition(),l=1;l<i;l++){var[f,g]=r[l];c.render(t,f,g)}if(s.isUrlDefinition()){var p=s.getDefinition(),[d,v]=r[i];p.render(t,d,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=ku.pathM(r),{x:n,y:a}=i;r.addMarker(i),e.addPoint(n,a),t&&t.moveTo(n,a)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=ku.pathL(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathH(t){var{current:e,command:r}=t,i=new cu((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=ku.pathH(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathV(t){var{current:e,command:r}=t,i=new cu(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=ku.pathV(r),{x:a,y:s}=n;r.addMarker(n,i),e.addPoint(a,s),t&&t.lineTo(a,s)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=ku.pathC(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:a,currentPoint:s}=ku.pathS(r);r.addMarker(s,a,n),e.addBezierCurve(i.x,i.y,n.x,n.y,a.x,a.y,s.x,s.y),t&&t.bezierCurveTo(n.x,n.y,a.x,a.y,s.x,s.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=ku.pathQ(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:a}=ku.pathT(r);r.addMarker(a,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,a.x,a.y),t&&t.quadraticCurveTo(n.x,n.y,a.x,a.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:a,lArcFlag:s,sweepFlag:o}=r,u=a*(Math.PI/180),h=t.getAsCurrentPoint(),c=new cu(Math.cos(u)*(e.x-h.x)/2+Math.sin(u)*(e.y-h.y)/2,-Math.sin(u)*(e.x-h.x)/2+Math.cos(u)*(e.y-h.y)/2),l=Math.pow(c.x,2)/Math.pow(i,2)+Math.pow(c.y,2)/Math.pow(n,2);l>1&&(i*=Math.sqrt(l),n*=Math.sqrt(l));var f=(s===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(c.y,2)-Math.pow(n,2)*Math.pow(c.x,2))/(Math.pow(i,2)*Math.pow(c.y,2)+Math.pow(n,2)*Math.pow(c.x,2)));isNaN(f)&&(f=0);var g=new cu(f*i*c.y/n,f*-n*c.x/i),p=new cu((e.x+h.x)/2+Math.cos(u)*g.x-Math.sin(u)*g.y,(e.y+h.y)/2+Math.sin(u)*g.x+Math.cos(u)*g.y),d=tu([1,0],[(c.x-g.x)/i,(c.y-g.y)/n]),v=[(c.x-g.x)/i,(c.y-g.y)/n],y=[(-c.x-g.x)/i,(-c.y-g.y)/n],m=tu(v,y);return Jo(v,y)<=-1&&(m=Math.PI),Jo(v,y)>=1&&(m=0),{currentPoint:h,rX:i,rY:n,sweepFlag:o,xAxisRotation:u,centp:p,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:a,sweepFlag:s,xAxisRotation:o,centp:u,a1:h,ad:c}=ku.pathA(r),l=1-s?1:-1,f=h+l*(c/2),g=new cu(u.x+n*Math.cos(f),u.y+a*Math.sin(f));if(r.addMarkerAngle(g,f-l*Math.PI/2),r.addMarkerAngle(i,f-l*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(h)&&!isNaN(c)){var p=n>a?n:a,d=n>a?1:n/a,v=n>a?a/n:1;t.translate(u.x,u.y),t.rotate(o),t.scale(d,v),t.arc(0,0,p,h,h+c,Boolean(1-s)),t.scale(1/d,1/v),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){ku.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class Lu extends ku{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class Du extends Iu{constructor(t,e,r){super(t,e,new.target===Du||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach(((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n})),e}getFontSize(){var{document:t,parent:e}=this,r=Ru.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new _u(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var a=e.length,s=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===s)&&r<a-1&&" "!==o&&(u="terminal"),r>0&&" "!==s&&r<a-1&&" "!==o&&(u="medial"),r>0&&" "!==s&&(r===a-1||" "===o)&&(u="initial"),void 0!==t.glyphs[i]){var h=t.glyphs[i];n=h instanceof Lu?h:h[u]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,a=Io(e.textContent||"");return 0===i&&(a=ko(a)),i===n&&(a=Lo(a)),a}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach(((e,r)=>{this.renderChild(t,this,this,r)}));var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n)for(var{unitsPerEm:a}=n.fontFace,s=Ru.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(s.fontSize),u=r.getStyle("font-style").getString(s.fontStyle),h=o/a,c=n.isRTL?i.split("").reverse().join(""):i,l=Do(r.getAttribute("dx").getString()),f=c.length,g=0;g<f;g++){var p=this.getGlyph(n,c,g);t.translate(this.x,this.y),t.scale(h,-h);var d=t.lineWidth;t.lineWidth=t.lineWidth*a/o,"italic"===u&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/h,-1/h),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||n.horizAdvX)/a,void 0===l[g]||isNaN(l[g])||(this.x+=l[g])}else{var{x:v,y:y}=this;t.fillStyle&&t.fillText(i,v,y),t.strokeStyle&&t.strokeText(i,v,y)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach(((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)})),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach(((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)})):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!=typeof n.measureText)return n;t.save(),n.setContext(t,!0);var a=n.getAttribute("x"),s=n.getAttribute("y"),o=n.getAttribute("dx"),u=n.getAttribute("dy"),h=n.getStyle("font-family").getDefinition(),c=Boolean(h)&&h.isRTL;0===i&&(a.hasValue()||a.setValue(n.getInheritedAttribute("x")),s.hasValue()||s.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),u.hasValue()||u.setValue(n.getInheritedAttribute("dy")));var l=n.measureText(t);return c&&(e.x-=l),a.hasValue()?(e.applyAnchoring(),n.x=a.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,c||(e.x+=l),s.hasValue()?(n.y=s.getPixels("y"),u.hasValue()&&(n.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+l),e.maxX=Math.max(e.maxX,n.x,n.x+l),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!=typeof n.getBoundingBox)return null;var a=n.getBoundingBox(t);return a?(n.children.forEach(((r,i)=>{var s=e.getChildBoundingBox(t,e,n,i);a.addBoundingBox(s)})),a):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach(((r,i)=>{e.renderChild(t,e,n,i)}))}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),a=i.isRTL?e.split("").reverse().join(""):e,s=Do(r.getAttribute("dx").getString()),o=a.length,u=0,h=0;h<o;h++){u+=(this.getGlyph(i,a,h).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,void 0===s[h]||isNaN(s[h])||(u+=s[h])}return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:c}=t.measureText(e);return this.clearContext(t),t.restore(),c}getInheritedAttribute(t){for(var e=this;e instanceof Du&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class ju extends Du{constructor(t,e,r){super(t,e,new.target===ju||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class Bu extends ju{constructor(){super(...arguments),this.type="textNode"}}class zu extends Iu{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,a=t.canvas;if(i.setDefaults(t),a.style&&void 0!==t.font&&n&&void 0!==n.getComputedStyle){t.font=n.getComputedStyle(a).getPropertyValue("font");var s=new uu(r,"fontSize",Ru.parse(t.font).fontSize);s.hasValue()&&(r.rootEmSize=s.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),c=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?Do(l.getString()):null,g=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,d=0,v=0,y=0;f&&(p=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=p,y=d,p=0,d=0)),i.viewPort.setCurrent(o,u),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:u,minX:p,minY:d,refX:h.getValue(),refY:c.getValue(),clip:g,clipX:v,clipY:y}),f&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),a=this.getAttribute("viewBox"),s=this.getAttribute("style"),o=i.getNumber(0),u=n.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),n.setValue(e),a.hasValue()||a.setValue("0 0 ".concat(o||t," ").concat(u||e)),s.hasValue()){var c=this.getStyle("width"),l=this.getStyle("height");c.hasValue()&&c.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}class Uu extends ku{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),a=this.getAttribute("rx"),s=this.getAttribute("ry"),o=a.getPixels("x"),u=s.getPixels("y");if(a.hasValue()&&!s.hasValue()&&(u=o),s.hasValue()&&!a.hasValue()&&(o=u),o=Math.min(o,i/2),u=Math.min(u,n/2),t){var h=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+h*o,r,e+i,r+u-h*u,e+i,r+u),t.lineTo(e+i,r+n-u),t.bezierCurveTo(e+i,r+n-u+h*u,e+i-o+h*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-h*o,r+n,e,r+n-u+h*u,e,r+n-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-h*u,e+o-h*o,r,e+o,r),t.closePath())}return new _u(e,r,e+i,r+n)}getMarkers(){return null}}class Fu extends ku{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new _u(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class Hu extends ku{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),a=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,a),t.bezierCurveTo(n+r,a+e*i,n+e*r,a+i,n,a+i),t.bezierCurveTo(n-e*r,a+i,n-r,a+e*i,n-r,a),t.bezierCurveTo(n-r,a-e*i,n-e*r,a-i,n,a-i),t.bezierCurveTo(n+e*r,a-i,n+r,a-e*i,n+r,a),t.closePath()),new _u(n-r,a-i,n+r,a+i)}getMarkers(){return null}}class Xu extends ku{constructor(){super(...arguments),this.type="line"}getPoints(){return[new cu(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new cu(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new _u(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Yu extends ku{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=cu.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new _u(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach((e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)})),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach(((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])})),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Wu extends Yu{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class qu extends Cu{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),a=new zu(this.document,null);a.attributes.viewBox=new uu(this.document,"viewBox",this.getAttribute("viewBox").getValue()),a.attributes.width=new uu(this.document,"width","".concat(i,"px")),a.attributes.height=new uu(this.document,"height","".concat(n,"px")),a.attributes.transform=new uu(this.document,"transform",this.getAttribute("patternTransform").getValue()),a.children=this.children;var s=this.document.createCanvas(i,n),o=s.getContext("2d"),u=this.getAttribute("x"),h=this.getAttribute("y");u.hasValue()&&h.hasValue()&&o.translate(u.getPixels("x",!0),h.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var c=-1;c<=1;c++)for(var l=-1;l<=1;l++)o.save(),a.attributes.x=new uu(this.document,"x",c*s.width),a.attributes.y=new uu(this.document,"y",l*s.height),a.render(o),o.restore();return t.createPattern(s,"repeat")}}class $u extends Cu{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,a=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===a&&t.rotate(r),"strokeWidth"===s&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new zu(this.document,null);o.type=this.type,o.attributes.viewBox=new uu(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new uu(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new uu(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new uu(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new uu(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new uu(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new uu(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new uu(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===s&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===a&&t.rotate(-r),t.translate(-i,-n)}}}class Gu extends Cu{constructor(){super(...arguments),this.type="defs"}render(){}}class Qu extends Iu{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new _u;return this.children.forEach((r=>{e.addBoundingBox(r.getBoundingBox(t))})),e}}class Zu extends Cu{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach((t=>{"stop"===t.type&&i.push(t)}))}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,a=this.getGradient(t,e);if(!a)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach((t=>{a.addColorStop(t.offset,this.addParentOpacity(r,t.color))})),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=s.screen,[h]=u.viewPorts,c=new Uu(s,null);c.attributes.x=new uu(s,"x",-o/3),c.attributes.y=new uu(s,"y",-o/3),c.attributes.width=new uu(s,"width",o),c.attributes.height=new uu(s,"height",o);var l=new Qu(s,null);l.attributes.transform=new uu(s,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[c];var f=new zu(s,null);f.attributes.x=new uu(s,"x",0),f.attributes.y=new uu(s,"y",0),f.attributes.width=new uu(s,"width",h.width),f.attributes.height=new uu(s,"height",h.height),f.children=[l];var g=s.createCanvas(h.width,h.height),p=g.getContext("2d");return p.fillStyle=a,f.render(p),p.createPattern(g,"no-repeat")}return a}inheritStopContainer(t){this.attributesToInherit.forEach((e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())}))}addParentOpacity(t,e){return t.hasValue()?new uu(this.document,"color",e).addOpacity(t).getColor():e}}class Ku extends Zu{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),a=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===s&&a===o?null:t.createLinearGradient(n,a,s,o)}}class Ju extends Zu{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),a=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=n,o=a;this.getAttribute("fx").hasValue()&&(s=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return t.createRadialGradient(s,o,h,n,a,u)}}class th extends Cu{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),a=this.getStyle("stop-color",!0);""===a.getString()&&a.setValue("#000"),n.hasValue()&&(a=a.addOpacity(n)),this.offset=i,this.color=a.getColor()}}class eh extends Cu{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new uu(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var a=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var o=s.getString();a="".concat(o,"(").concat(a,")")}r.setValue(a),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),a=Math.ceil(i);r.from=new uu(t,"from",parseFloat(e.getValue()[n])),r.to=new uu(t,"to",parseFloat(e.getValue()[a])),r.progress=(i-n)/(a-n)}else r.from=this.from,r.to=this.to;return r}}class rh extends eh{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new $s(e.getColor()),n=new $s(r.getColor());if(i.ok&&n.ok){var a=i.r+(n.r-i.r)*t,s=i.g+(n.g-i.g)*t,o=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(a),", ").concat(Math.floor(s),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class ih extends eh{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=Do(e.getString()),n=Do(r.getString());return i.map(((e,r)=>e+(n[r]-e)*t)).join(" ")}}class nh extends Cu{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs=Object.create(null),this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var a of n)switch(a.type){case"font-face":this.fontFace=a;var s=a.getStyle("font-family");s.hasValue()&&(i[s.getString()]=this);break;case"missing-glyph":this.missingGlyph=a;break;case"glyph":var o=a;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]=Object.create(null)),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class ah extends Cu{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class sh extends ku{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class oh extends Du{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class uh extends Du{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],a=i.length>0&&Array.from(i).every((t=>3===t.nodeType));this.hasText=a,this.text=a?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,a=new uu(e,"fontSize",Ru.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new _u(r,i-a.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var s=new Qu(this.document,null);s.children=this.children,s.parent=this,s.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function hh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function ch(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hh(Object(r),!0).forEach((function(e){xs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class lh extends Du{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach((e=>{var{type:r,points:i}=e;switch(r){case Vu.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case Vu.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case Vu.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case Vu.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case Vu.ARC:var[n,a,s,o,u,h,c,l]=i,f=s>o?s:o,g=s>o?1:s/o,p=s>o?o/s:1;t&&(t.translate(n,a),t.rotate(c),t.scale(g,p),t.arc(0,0,f,u,u+h,Boolean(1-l)),t.scale(1/g,1/p),t.rotate(-c),t.translate(-n,-a));break;case Vu.CLOSE_PATH:t&&t.closePath()}}))}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach(((i,n)=>{var{p0:a,p1:s,rotation:o,text:u}=i;t.save(),t.translate(a.x,a.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(a.x,a.y+r/8),t.lineTo(s.x,s.y+r/5))})),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,a,s,o,u){var h=a,c=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(c+=(i-r)/n),u>-1&&(h+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(h,l,0),g=this.getEquidistantPointOnPath(h+c,l,0),p={p0:f,p1:g},d=f&&g?Math.atan2(g.y-f.y,g.x-f.x):0;if(s){var v=Math.cos(Math.PI/2+d)*s,y=Math.cos(-d)*s;p.p0=ch(ch({},f),{},{x:f.x+v,y:f.y+y}),p.p1=ch(ch({},g),{},{x:g.x+v,y:g.y+y})}return{offset:h+=c,segment:p,rotation:d}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map((t=>t.getPixels("x"))),a=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),h=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(h=o.getPixels()):h=u.getPixels();var c=[],l=e.length;this.letterSpacingCache=c;for(var f=0;f<l;f++)c.push(void 0!==n[f]?n[f]:h);var g=c.reduce(((t,e,r)=>0===r?0:t+e||0),0),p=this.measureText(t),d=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;"middle"!==s&&"center"!==s||(m=-d/2),"end"!==s&&"right"!==s||(m=-d),m+=y,r.forEach(((e,n)=>{var{offset:o,segment:u,rotation:h}=this.findSegmentToFitChar(t,s,d,v,i,m,a,e,n);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[n],p0:u.p0,p1:u.p1,rotation:h})}))}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,a=i?i.y:0,s=r.next(),o=s.type,u=[];switch(s.type){case Vu.MOVE_TO:this.pathM(r,u);break;case Vu.LINE_TO:o=this.pathL(r,u);break;case Vu.HORIZ_LINE_TO:o=this.pathH(r,u);break;case Vu.VERT_LINE_TO:o=this.pathV(r,u);break;case Vu.CURVE_TO:this.pathC(r,u);break;case Vu.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case Vu.QUAD_TO:this.pathQ(r,u);break;case Vu.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case Vu.ARC:u=this.pathA(r);break;case Vu.CLOSE_PATH:ku.pathZ(r)}s.type!==Vu.CLOSE_PATH?e.push({type:o,points:u,start:{x:n,y:a},pathLength:this.calcLength(n,a,o,u)}):e.push({type:Vu.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=ku.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=ku.pathL(t).point;return e.push(r,i),Vu.LINE_TO}pathH(t,e){var{x:r,y:i}=ku.pathH(t).point;return e.push(r,i),Vu.LINE_TO}pathV(t,e){var{x:r,y:i}=ku.pathV(t).point;return e.push(r,i),Vu.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=ku.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=ku.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),Vu.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=ku.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=ku.pathT(t);return e.push(r.x,r.y,i.x,i.y),Vu.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:a,a1:s,ad:o}=ku.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[a.x,a.y,e,r,s,o,n,i]}calcLength(t,e,r,i){var n=0,a=null,s=null,o=0;switch(r){case Vu.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case Vu.CURVE_TO:for(n=0,a=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)s=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case Vu.QUAD_TO:for(n=0,a=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)s=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return n;case Vu.ARC:n=0;var u=i[4],h=i[5],c=i[4]+h,l=Math.PI/180;if(Math.abs(u-c)<l&&(l=Math.abs(u-c)),a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),h<0)for(o=u-l;o>c;o-=l)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;else for(o=u+l;o<c;o+=l)s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(a.x,a.y,s.x,s.y),a=s;return s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],c,0),n+=this.getLineLength(a.x,a.y,s.x,s.y)}return 0}getPointOnLine(t,e,r,i,n){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+Zo),u=Math.sqrt(t*t/(1+o*o));i<e&&(u*=-1);var h=o*u,c=null;if(i===e)c={x:a,y:s+h};else if((s-r)/(a-e+Zo)===o)c={x:a+u,y:s+h};else{var l,f,g=this.getLineLength(e,r,i,n);if(g<Zo)return null;var p=(a-e)*(i-e)+(s-r)*(n-r);l=e+(p/=g*g)*(i-e),f=r+p*(n-r);var d=this.getLineLength(a,s,l,f),v=Math.sqrt(t*t-d*d);u=Math.sqrt(v*v/(1+o*o)),i<e&&(u*=-1),c={x:l+u,y:f+(h=o*u)}}return c}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var a of n){if(!a||!(a.pathLength<5e-5||r+a.pathLength+5e-5<t)){var s=t-r,o=0;switch(a.type){case Vu.LINE_TO:i=this.getPointOnLine(s,a.start.x,a.start.y,a.points[0],a.points[1],a.start.x,a.start.y);break;case Vu.ARC:var u=a.points[4],h=a.points[5],c=a.points[4]+h;if(o=u+s/a.pathLength*h,h<0&&o<c||h>=0&&o>c)break;i=this.getPointOnEllipticalArc(a.points[0],a.points[1],a.points[2],a.points[3],o,a.points[6]);break;case Vu.CURVE_TO:(o=s/a.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3],a.points[4],a.points[5]);break;case Vu.QUAD_TO:(o=s/a.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,a.start.x,a.start.y,a.points[0],a.points[1],a.points[2],a.points[3])}if(i)return i;break}r+=a.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce(((t,e)=>e.pathLength>0?t+e.pathLength:t),0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,a,s,o,u){return{x:o*eu(t)+a*ru(t)+i*iu(t)+e*nu(t),y:u*eu(t)+s*ru(t)+n*iu(t)+r*nu(t)}}getPointOnQuadraticBezier(t,e,r,i,n,a,s){return{x:a*au(t)+i*su(t)+e*ou(t),y:s*au(t)+n*su(t)+r*ou(t)}}getPointOnEllipticalArc(t,e,r,i,n,a){var s=Math.cos(a),o=Math.sin(a),u=r*Math.cos(n),h=i*Math.sin(n);return{x:t+(u*s-h*o),y:e+(u*o+h*s)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var a=0,s=0;s<=r;s+=i){var o=this.getPointOnPath(s),u=this.getPointOnPath(s+i);o&&u&&((a+=this.getLineLength(o.x,o.y,u.x,u.y))>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:s}),a-=n))}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var fh=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class gh extends Iu{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(t){var e=this;return Wn((function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(i){}e.loaded=!0}))()}loadSvg(t){var e=this;return Wn((function*(){var r=fh.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield e.document.fetch(t),a=yield n.text();e.image=a}catch(s){}e.loaded=!0}))()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),a=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&s&&o){if(t.save(),t.translate(n,a),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var h=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:o,desiredHeight:h.height}),this.loaded&&(void 0===h.complete||h.complete)&&t.drawImage(h,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new _u(t,e,t+r,e+i)}}class ph extends Iu{constructor(){super(...arguments),this.type="symbol"}render(t){}}class dh{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return Wn((function*(){try{var{document:i}=r,n=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(n).forEach((e=>{var r=i.createElement(e);i.definitions[t]=r}))}catch(a){}r.loaded=!0}))()}}class vh extends Cu{constructor(t,e,r){super(t,e,r),this.type="style",Io(Array.from(e.childNodes).map((t=>t.textContent)).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach((e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),a=i[1].split(";");n.forEach((e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(a.forEach((e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),a=e.substr(r+1,e.length-r).trim();n&&a&&(i[n]=new uu(t,n,a))})),t.styles[r]=i,t.stylesSpecificity[r]=Qo(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach((e=>{if(e.indexOf('format("svg")')>0){var r=zo(e);r&&new dh(t).load(n,r)}}))}}}))}}))}}vh.parseExternalUrl=zo;class yh extends Iu{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new zu(e,null)).attributes.viewBox=new uu(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new uu(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new uu(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new uu(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),a=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new uu(e,"width",n.getString())),a.hasValue()&&(i.attributes.height=new uu(e,"height",a.getString()))}var s=i.parent;i.parent=this,i.render(t),i.parent=s}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return Au.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function mh(t,e,r,i,n,a){return t[r*i*4+4*e+a]}function xh(t,e,r,i,n,a,s){t[r*i*4+4*e+a]=s}function bh(t,e,r){return t[e]*r}function wh(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class Sh extends Cu{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=Do(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var a=i[0]*Math.PI/180;i=[wh(a,.213,.787,-.213),wh(a,.715,-.715,-.715),wh(a,.072,-.072,.928),0,0,wh(a,.213,-.213,.143),wh(a,.715,.285,.14),wh(a,.072,-.072,-.283),0,0,wh(a,.213,-.213,-.787),wh(a,.715,-.715,.715),wh(a,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:a,matrix:s}=this,o=t.getImageData(0,0,i,n),u=0;u<n;u++)for(var h=0;h<i;h++){var c=mh(o.data,h,u,i,0,0),l=mh(o.data,h,u,i,0,1),f=mh(o.data,h,u,i,0,2),g=mh(o.data,h,u,i,0,3),p=bh(s,0,c)+bh(s,1,l)+bh(s,2,f)+bh(s,3,g)+bh(s,4,1),d=bh(s,5,c)+bh(s,6,l)+bh(s,7,f)+bh(s,8,g)+bh(s,9,1),v=bh(s,10,c)+bh(s,11,l)+bh(s,12,f)+bh(s,13,g)+bh(s,14,1),y=bh(s,15,c)+bh(s,16,l)+bh(s,17,f)+bh(s,18,g)+bh(s,19,1);a&&(p=0,d=0,v=0,y*=g/255),xh(o.data,h,u,i,0,0,p),xh(o.data,h,u,i,0,1,d),xh(o.data,h,u,i,0,2,v),xh(o.data,h,u,i,0,3,y)}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class Th extends Cu{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),s=this.getStyle("height").getPixels("y");if(!a&&!s){var o=new _u;this.children.forEach((e=>{o.addBoundingBox(e.getBoundingBox(t))})),i=Math.floor(o.x1),n=Math.floor(o.y1),a=Math.floor(o.width),s=Math.floor(o.height)}var u=this.removeStyles(e,Th.ignoreStyles),h=r.createCanvas(i+a,n+s),c=h.getContext("2d");r.screen.setDefaults(c),this.renderChildren(c),new Sh(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(c,0,0,i+a,n+s);var l=r.createCanvas(i+a,n+s),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=c.createPattern(h,"no-repeat"),f.fillRect(0,0,i+a,n+s),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,i+a,n+s),this.restoreStyles(e,u)}render(t){}}Th.ignoreStyles=["mask","transform","clip-path"];var Oh=()=>{};class Ah extends Cu{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=Oh,r.closePath=Oh),Reflect.apply(i,t,[]),this.children.forEach((i=>{if(void 0!==i.path){var a=void 0!==i.elementTransform?i.elementTransform():null;a||(a=Au.fromElement(e,i)),a&&a.apply(t),i.path(t),r&&(r.closePath=n),a&&a.unapply(t)}})),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class Ch extends Cu{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var a=0,s=0;i.forEach((t=>{var e=t.extraFilterDistance||0;a=Math.max(a,e),s=Math.max(s,e)}));var o=Math.floor(n.width),u=Math.floor(n.height),h=o+2*a,c=u+2*s;if(!(h<1||c<1)){var l=Math.floor(n.x),f=Math.floor(n.y),g=this.removeStyles(e,Ch.ignoreStyles),p=r.createCanvas(h,c),d=p.getContext("2d");r.screen.setDefaults(d),d.translate(-l+a,-f+s),e.render(d),i.forEach((t=>{"function"==typeof t.apply&&t.apply(d,0,0,h,c)})),t.drawImage(p,0,0,h,c,l-a,f-s,h,c),this.restoreStyles(e,g)}}}render(t){}}Ch.ignoreStyles=["filter","transform","clip-path"];class Ph extends Cu{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class Eh extends Cu{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class Nh extends Cu{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class Mh extends Cu{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,n){var{document:a,blurRadius:s}=this,o=a.window?a.window.document.body:null,u=t.canvas;u.id=a.getUniqueId(),o&&(u.style.display="none",o.appendChild(u)),Ro(u,e,r,i,n,s),o&&o.removeChild(u)}}class Rh extends Cu{constructor(){super(...arguments),this.type="title"}}class _h extends Cu{constructor(){super(...arguments),this.type="desc"}}var Vh={svg:zu,rect:Uu,circle:Fu,ellipse:Hu,line:Xu,polyline:Yu,polygon:Wu,path:ku,pattern:qu,marker:$u,defs:Gu,linearGradient:Ku,radialGradient:Ju,stop:th,animate:eh,animateColor:rh,animateTransform:ih,font:nh,"font-face":ah,"missing-glyph":sh,glyph:Lu,text:Du,tspan:ju,tref:oh,a:uh,textPath:lh,image:gh,g:Qu,symbol:ph,style:vh,use:yh,mask:Th,clipPath:Ah,filter:Ch,feDropShadow:Ph,feMorphology:Eh,feComposite:Nh,feColorMatrix:Sh,feGaussianBlur:Mh,title:Rh,desc:_h};function Ih(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function kh(){return kh=Wn((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise(((e,i)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,n,a)=>{i(a)},r.src=t}))})),kh.apply(this,arguments)}class Lh{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=Lh.createCanvas,createImage:n=Lh.createImage,anonymousCrossOrigin:a}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions=Object.create(null),this.styles=Object.create(null),this.stylesSpecificity=Object.create(null),this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,a),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,i)=>t(r,"boolean"==typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every((t=>t.loaded))}isFontsLoaded(){return this.fonts.every((t=>t.loaded))}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=Lh.elementTypes[e];return void 0!==r?new r(this,t):new Pu(this,t)}createTextNode(t){return new Bu(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ih(Object(r),!0).forEach((function(e){xs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ih(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({document:this},t))}}function Dh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function jh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Dh(Object(r),!0).forEach((function(e){xs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Dh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}Lh.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},Lh.createImage=function(t){return kh.apply(this,arguments)},Lh.elementTypes=Vh;class Bh{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new yu(r),this.screen=new pu(t,r),this.options=r;var i=new Lh(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return Wn((function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=new yu(i),a=yield n.parse(e);return new Bh(t,a,i)}))()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new yu(r).parseFromString(e);return new Bh(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Bh.from(t,e,jh(jh({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Bh.fromString(t,e,jh(jh({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return Wn((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(jh({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,jh(jh({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}export{uh as AElement,rh as AnimateColorElement,eh as AnimateElement,ih as AnimateTransformElement,_u as BoundingBox,eu as CB1,ru as CB2,iu as CB3,nu as CB4,Bh as Canvg,Fu as CircleElement,Ah as ClipPathElement,Gu as DefsElement,_h as DescElement,Lh as Document,Cu as Element,Hu as EllipseElement,Sh as FeColorMatrixElement,Nh as FeCompositeElement,Ph as FeDropShadowElement,Mh as FeGaussianBlurElement,Eh as FeMorphologyElement,Ch as FilterElement,Ru as Font,nh as FontElement,ah as FontFaceElement,Qu as GElement,Lu as GlyphElement,Zu as GradientElement,gh as ImageElement,Xu as LineElement,Ku as LinearGradientElement,$u as MarkerElement,Th as MaskElement,wu as Matrix,sh as MissingGlyphElement,lu as Mouse,Zo as PSEUDO_ZERO,yu as Parser,ku as PathElement,Vu as PathParser,qu as PatternElement,cu as Point,Wu as PolygonElement,Yu as PolylineElement,uu as Property,au as QB1,su as QB2,ou as QB3,Ju as RadialGradientElement,Uu as RectElement,Iu as RenderedElement,xu as Rotate,zu as SVGElement,dh as SVGFontLoader,bu as Scale,pu as Screen,Su as Skew,Tu as SkewX,Ou as SkewY,th as StopElement,vh as StyleElement,ph as SymbolElement,oh as TRefElement,ju as TSpanElement,Du as TextElement,lh as TextPathElement,Rh as TitleElement,Au as Transform,mu as Translate,Pu as UnknownElement,yh as UseElement,hu as ViewPort,Io as compressSpaces,Bh as default,Qo as getSelectorSpecificity,Bo as normalizeAttributeName,Uo as normalizeColor,zo as parseExternalUrl,Vo as presets,Do as toNumbers,ko as trimLeft,Lo as trimRight,Ko as vectorMagnitude,tu as vectorsAngle,Jo as vectorsRatio};
