import{c as t,g as e,r as n}from"./react-vendor-CFHJfABC.js";import{r,R as i}from"./router-vendor-BEZ3q80F.js";var a,o={exports:{}},s={},l={},u={};function c(){return a||(a=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e._registerNode=e.Konva=e.glob=void 0;const n=Math.PI/180;e.glob=void 0!==t?t:"undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope?self:{},e.Konva={_global:e.glob,version:"9.3.20",isBrowser:"undefined"!=typeof window&&("[object Window]"==={}.toString.call(window)||"[object global]"==={}.toString.call(window)),isUnminified:/param/.test(function(t){}.toString()),dblClickWindow:400,getAngle:t=>e.Konva.angleDeg?t*n:t,enableTrace:!1,pointerEventsEnabled:!0,autoDrawEnabled:!0,hitOnDragEnabled:!1,capturePointerEventsEnabled:!1,_mouseListenClick:!1,_touchListenClick:!1,_pointerListenClick:!1,_mouseInDblClickWindow:!1,_touchInDblClickWindow:!1,_pointerInDblClickWindow:!1,_mouseDblClickPointerId:null,_touchDblClickPointerId:null,_pointerDblClickPointerId:null,_fixTextRendering:!1,pixelRatio:"undefined"!=typeof window&&window.devicePixelRatio||1,dragDistance:3,angleDeg:!0,showWarnings:!0,dragButtons:[0,1],isDragging:()=>e.Konva.DD.isDragging,isTransforming(){var t;return null===(t=e.Konva.Transformer)||void 0===t?void 0:t.isTransforming()},isDragReady:()=>!!e.Konva.DD.node,releaseCanvasOnDestroy:!0,document:e.glob.document,_injectGlobal(t){e.glob.Konva=t}};e._registerNode=t=>{e.Konva[t.prototype.getClassName()]=t},e.Konva._injectGlobal(e.Konva)}(u)),u}var h,d={};function f(){return h||(h=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Util=t.Transform=void 0;const e=c();class n{constructor(t=[1,0,0,1,0,0]){this.dirty=!1,this.m=t&&t.slice()||[1,0,0,1,0,0]}reset(){this.m[0]=1,this.m[1]=0,this.m[2]=0,this.m[3]=1,this.m[4]=0,this.m[5]=0}copy(){return new n(this.m)}copyInto(t){t.m[0]=this.m[0],t.m[1]=this.m[1],t.m[2]=this.m[2],t.m[3]=this.m[3],t.m[4]=this.m[4],t.m[5]=this.m[5]}point(t){const e=this.m;return{x:e[0]*t.x+e[2]*t.y+e[4],y:e[1]*t.x+e[3]*t.y+e[5]}}translate(t,e){return this.m[4]+=this.m[0]*t+this.m[2]*e,this.m[5]+=this.m[1]*t+this.m[3]*e,this}scale(t,e){return this.m[0]*=t,this.m[1]*=t,this.m[2]*=e,this.m[3]*=e,this}rotate(t){const e=Math.cos(t),n=Math.sin(t),r=this.m[0]*e+this.m[2]*n,i=this.m[1]*e+this.m[3]*n,a=this.m[0]*-n+this.m[2]*e,o=this.m[1]*-n+this.m[3]*e;return this.m[0]=r,this.m[1]=i,this.m[2]=a,this.m[3]=o,this}getTranslation(){return{x:this.m[4],y:this.m[5]}}skew(t,e){const n=this.m[0]+this.m[2]*e,r=this.m[1]+this.m[3]*e,i=this.m[2]+this.m[0]*t,a=this.m[3]+this.m[1]*t;return this.m[0]=n,this.m[1]=r,this.m[2]=i,this.m[3]=a,this}multiply(t){const e=this.m[0]*t.m[0]+this.m[2]*t.m[1],n=this.m[1]*t.m[0]+this.m[3]*t.m[1],r=this.m[0]*t.m[2]+this.m[2]*t.m[3],i=this.m[1]*t.m[2]+this.m[3]*t.m[3],a=this.m[0]*t.m[4]+this.m[2]*t.m[5]+this.m[4],o=this.m[1]*t.m[4]+this.m[3]*t.m[5]+this.m[5];return this.m[0]=e,this.m[1]=n,this.m[2]=r,this.m[3]=i,this.m[4]=a,this.m[5]=o,this}invert(){const t=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),e=this.m[3]*t,n=-this.m[1]*t,r=-this.m[2]*t,i=this.m[0]*t,a=t*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),o=t*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=e,this.m[1]=n,this.m[2]=r,this.m[3]=i,this.m[4]=a,this.m[5]=o,this}getMatrix(){return this.m}decompose(){const e=this.m[0],n=this.m[1],r=this.m[2],i=this.m[3],a=e*i-n*r,o={x:this.m[4],y:this.m[5],rotation:0,scaleX:0,scaleY:0,skewX:0,skewY:0};if(0!=e||0!=n){const t=Math.sqrt(e*e+n*n);o.rotation=n>0?Math.acos(e/t):-Math.acos(e/t),o.scaleX=t,o.scaleY=a/t,o.skewX=(e*r+n*i)/a,o.skewY=0}else if(0!=r||0!=i){const t=Math.sqrt(r*r+i*i);o.rotation=Math.PI/2-(i>0?Math.acos(-r/t):-Math.acos(r/t)),o.scaleX=a/t,o.scaleY=t,o.skewX=0,o.skewY=(e*r+n*i)/a}return o.rotation=t.Util._getRotation(o.rotation),o}}t.Transform=n;const r=Math.PI/180,i=180/Math.PI,a={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],transparent:[255,255,255,0],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]},o=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/;let s=[];const l="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||function(t){setTimeout(t,60)};t.Util={_isElement:t=>!(!t||1!=t.nodeType),_isFunction:t=>!!(t&&t.constructor&&t.call&&t.apply),_isPlainObject:t=>!!t&&t.constructor===Object,_isArray:t=>"[object Array]"===Object.prototype.toString.call(t),_isNumber:t=>"[object Number]"===Object.prototype.toString.call(t)&&!isNaN(t)&&isFinite(t),_isString:t=>"[object String]"===Object.prototype.toString.call(t),_isBoolean:t=>"[object Boolean]"===Object.prototype.toString.call(t),isObject:t=>t instanceof Object,isValidSelector(t){if("string"!=typeof t)return!1;const e=t[0];return"#"===e||"."===e||e===e.toUpperCase()},_sign:t=>0===t||t>0?1:-1,requestAnimFrame(t){s.push(t),1===s.length&&l((function(){const t=s;s=[],t.forEach((function(t){t()}))}))},createCanvasElement(){const t=document.createElement("canvas");try{t.style=t.style||{}}catch(e){}return t},createImageElement:()=>document.createElement("img"),_isInDocument(t){for(;t=t.parentNode;)if(t==document)return!0;return!1},_urlToImage(e,n){const r=t.Util.createImageElement();r.onload=function(){n(r)},r.src=e},_rgbToHex:(t,e,n)=>((1<<24)+(t<<16)+(e<<8)+n).toString(16).slice(1),_hexToRgb(t){t=t.replace("#","");const e=parseInt(t,16);return{r:e>>16&255,g:e>>8&255,b:255&e}},getRandomColor(){let t=(16777215*Math.random()|0).toString(16);for(;t.length<6;)t="0"+t;return"#"+t},getRGB(t){let e;return t in a?(e=a[t],{r:e[0],g:e[1],b:e[2]}):"#"===t[0]?this._hexToRgb(t.substring(1)):"rgb("===t.substr(0,4)?(e=o.exec(t.replace(/ /g,"")),{r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)}):{r:0,g:0,b:0}},colorToRGBA:e=>(e=e||"black",t.Util._namedColorToRBA(e)||t.Util._hex3ColorToRGBA(e)||t.Util._hex4ColorToRGBA(e)||t.Util._hex6ColorToRGBA(e)||t.Util._hex8ColorToRGBA(e)||t.Util._rgbColorToRGBA(e)||t.Util._rgbaColorToRGBA(e)||t.Util._hslColorToRGBA(e)),_namedColorToRBA(t){const e=a[t.toLowerCase()];return e?{r:e[0],g:e[1],b:e[2],a:1}:null},_rgbColorToRGBA(t){if(0===t.indexOf("rgb(")){const e=(t=t.match(/rgb\(([^)]+)\)/)[1]).split(/ *, */).map(Number);return{r:e[0],g:e[1],b:e[2],a:1}}},_rgbaColorToRGBA(t){if(0===t.indexOf("rgba(")){const e=(t=t.match(/rgba\(([^)]+)\)/)[1]).split(/ *, */).map(((t,e)=>"%"===t.slice(-1)?3===e?parseInt(t)/100:parseInt(t)/100*255:Number(t)));return{r:e[0],g:e[1],b:e[2],a:e[3]}}},_hex8ColorToRGBA(t){if("#"===t[0]&&9===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:parseInt(t.slice(7,9),16)/255}},_hex6ColorToRGBA(t){if("#"===t[0]&&7===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:1}},_hex4ColorToRGBA(t){if("#"===t[0]&&5===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:parseInt(t[4]+t[4],16)/255}},_hex3ColorToRGBA(t){if("#"===t[0]&&4===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:1}},_hslColorToRGBA(t){if(/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.test(t)){const[e,...n]=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(t),r=Number(n[0])/360,i=Number(n[1])/100,a=Number(n[2])/100;let o,s,l;if(0===i)return l=255*a,{r:Math.round(l),g:Math.round(l),b:Math.round(l),a:1};o=a<.5?a*(1+i):a+i-a*i;const u=2*a-o,c=[0,0,0];for(let t=0;t<3;t++)s=r+1/3*-(t-1),s<0&&s++,s>1&&s--,l=6*s<1?u+6*(o-u)*s:2*s<1?o:3*s<2?u+(o-u)*(2/3-s)*6:u,c[t]=255*l;return{r:Math.round(c[0]),g:Math.round(c[1]),b:Math.round(c[2]),a:1}}},haveIntersection:(t,e)=>!(e.x>t.x+t.width||e.x+e.width<t.x||e.y>t.y+t.height||e.y+e.height<t.y),cloneObject(t){const e={};for(const n in t)this._isPlainObject(t[n])?e[n]=this.cloneObject(t[n]):this._isArray(t[n])?e[n]=this.cloneArray(t[n]):e[n]=t[n];return e},cloneArray:t=>t.slice(0),degToRad:t=>t*r,radToDeg:t=>t*i,_degToRad:e=>(t.Util.warn("Util._degToRad is removed. Please use public Util.degToRad instead."),t.Util.degToRad(e)),_radToDeg:e=>(t.Util.warn("Util._radToDeg is removed. Please use public Util.radToDeg instead."),t.Util.radToDeg(e)),_getRotation:n=>e.Konva.angleDeg?t.Util.radToDeg(n):n,_capitalize:t=>t.charAt(0).toUpperCase()+t.slice(1),throw(t){throw new Error("Konva error: "+t)},error(t){},warn(t){e.Konva.showWarnings},each(t,e){for(const n in t)e(n,t[n])},_inRange:(t,e,n)=>e<=t&&t<n,_getProjectionToSegment(t,e,n,r,i,a){let o,s,l;const u=(t-n)*(t-n)+(e-r)*(e-r);if(0==u)o=t,s=e,l=(i-n)*(i-n)+(a-r)*(a-r);else{const c=((i-t)*(n-t)+(a-e)*(r-e))/u;c<0?(o=t,s=e,l=(t-i)*(t-i)+(e-a)*(e-a)):c>1?(o=n,s=r,l=(n-i)*(n-i)+(r-a)*(r-a)):(o=t+c*(n-t),s=e+c*(r-e),l=(o-i)*(o-i)+(s-a)*(s-a))}return[o,s,l]},_getProjectionToLine(e,n,r){const i=t.Util.cloneObject(e);let a=Number.MAX_VALUE;return n.forEach((function(o,s){if(!r&&s===n.length-1)return;const l=n[(s+1)%n.length],u=t.Util._getProjectionToSegment(o.x,o.y,l.x,l.y,e.x,e.y),c=u[0],h=u[1],d=u[2];d<a&&(i.x=c,i.y=h,a=d)})),i},_prepareArrayForTween(e,n,r){const i=[],a=[];if(e.length>n.length){const t=n;n=e,e=t}for(let t=0;t<e.length;t+=2)i.push({x:e[t],y:e[t+1]});for(let t=0;t<n.length;t+=2)a.push({x:n[t],y:n[t+1]});const o=[];return a.forEach((function(e){const n=t.Util._getProjectionToLine(e,i,r);o.push(n.x),o.push(n.y)})),o},_prepareToStringify(e){let n;e.visitedByCircularReferenceRemoval=!0;for(const r in e)if(e.hasOwnProperty(r)&&e[r]&&"object"==typeof e[r])if(n=Object.getOwnPropertyDescriptor(e,r),e[r].visitedByCircularReferenceRemoval||t.Util._isElement(e[r])){if(!n.configurable)return null;delete e[r]}else if(null===t.Util._prepareToStringify(e[r])){if(!n.configurable)return null;delete e[r]}return delete e.visitedByCircularReferenceRemoval,e},_assign(t,e){for(const n in e)t[n]=e[n];return t},_getFirstPointerId:t=>t.touches?t.changedTouches[0].identifier:t.pointerId||999,releaseCanvas(...t){e.Konva.releaseCanvasOnDestroy&&t.forEach((t=>{t.width=0,t.height=0}))},drawRoundedRectPath(t,e,n,r){let i=0,a=0,o=0,s=0;"number"==typeof r?i=a=o=s=Math.min(r,e/2,n/2):(i=Math.min(r[0]||0,e/2,n/2),a=Math.min(r[1]||0,e/2,n/2),s=Math.min(r[2]||0,e/2,n/2),o=Math.min(r[3]||0,e/2,n/2)),t.moveTo(i,0),t.lineTo(e-a,0),t.arc(e-a,a,a,3*Math.PI/2,0,!1),t.lineTo(e,n-s),t.arc(e-s,n-s,s,0,Math.PI/2,!1),t.lineTo(o,n),t.arc(o,n-o,o,Math.PI/2,Math.PI,!1),t.lineTo(0,i),t.arc(i,i,i,Math.PI,3*Math.PI/2,!1)}}}(d)),d}var g,p,m={},y={},v={};function b(){if(g)return v;g=1,Object.defineProperty(v,"__esModule",{value:!0}),v.RGBComponent=function(t){if(t>255)return 255;if(t<0)return 0;return Math.round(t)},v.alphaComponent=function(t){if(t>1)return 1;if(t<1e-4)return 1e-4;return t},v.getNumberValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isNumber(t)||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a number.'),t}},v.getNumberOrArrayOfNumbersValidator=function(r){if(t.Konva.isUnminified)return function(t,i){let a=e.Util._isNumber(t),o=e.Util._isArray(t)&&t.length==r;return a||o||e.Util.warn(n(t)+' is a not valid value for "'+i+'" attribute. The value should be a number or Array<number>('+r+")"),t}},v.getNumberOrAutoValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isNumber(t)||"auto"===t||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a number or "auto".'),t}},v.getStringValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isString(t)||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a string.'),t}},v.getStringOrGradientValidator=function(){if(t.Konva.isUnminified)return function(t,r){const i=e.Util._isString(t),a="[object CanvasGradient]"===Object.prototype.toString.call(t)||t&&t.addColorStop;return i||a||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a string or a native gradient.'),t}},v.getFunctionValidator=function(){if(t.Konva.isUnminified)return function(t,r){return e.Util._isFunction(t)||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a function.'),t}},v.getNumberArrayValidator=function(){if(t.Konva.isUnminified)return function(t,r){const i=Int8Array?Object.getPrototypeOf(Int8Array):null;return i&&t instanceof i||(e.Util._isArray(t)?t.forEach((function(t){e.Util._isNumber(t)||e.Util.warn('"'+r+'" attribute has non numeric element '+t+". Make sure that all elements are numbers.")})):e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a array of numbers.')),t}},v.getBooleanValidator=function(){if(t.Konva.isUnminified)return function(t,r){return!0===t||!1===t||e.Util.warn(n(t)+' is a not valid value for "'+r+'" attribute. The value should be a boolean.'),t}},v.getComponentValidator=function(r){if(t.Konva.isUnminified)return function(t,i){return null==t||e.Util.isObject(t)||e.Util.warn(n(t)+' is a not valid value for "'+i+'" attribute. The value should be an object with properties '+r),t}};const t=c(),e=f();function n(t){return e.Util._isString(t)?'"'+t+'"':"[object Number]"===Object.prototype.toString.call(t)||e.Util._isBoolean(t)?t:Object.prototype.toString.call(t)}return v}function _(){return p||(p=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Factory=void 0;const e=f(),n=b(),r="get",i="set";t.Factory={addGetterSetter(e,n,r,i,a){t.Factory.addGetter(e,n,r),t.Factory.addSetter(e,n,i,a),t.Factory.addOverloadedGetterSetter(e,n)},addGetter(t,n,i){var a=r+e.Util._capitalize(n);t.prototype[a]=t.prototype[a]||function(){const t=this.attrs[n];return void 0===t?i:t}},addSetter(n,r,a,o){var s=i+e.Util._capitalize(r);n.prototype[s]||t.Factory.overWriteSetter(n,r,a,o)},overWriteSetter(t,n,r,a){var o=i+e.Util._capitalize(n);t.prototype[o]=function(t){return r&&null!=t&&(t=r.call(this,t,n)),this._setAttr(n,t),a&&a.call(this),this}},addComponentsGetterSetter(a,o,s,l,u){const c=s.length,h=e.Util._capitalize,d=r+h(o),f=i+h(o);a.prototype[d]=function(){const t={};for(let e=0;e<c;e++){const n=s[e];t[n]=this.getAttr(o+h(n))}return t};const g=(0,n.getComponentValidator)(s);a.prototype[f]=function(t){const e=this.attrs[o];l&&(t=l.call(this,t,o)),g&&g.call(this,t,o);for(const n in t)t.hasOwnProperty(n)&&this._setAttr(o+h(n),t[n]);return t||s.forEach((t=>{this._setAttr(o+h(t),void 0)})),this._fireChangeEvent(o,e,t),u&&u.call(this),this},t.Factory.addOverloadedGetterSetter(a,o)},addOverloadedGetterSetter(t,n){var a=e.Util._capitalize(n),o=i+a,s=r+a;t.prototype[n]=function(){return arguments.length?(this[o](arguments[0]),this):this[s]()}},addDeprecatedGetterSetter(n,i,a,o){e.Util.error("Adding deprecated "+i);const s=r+e.Util._capitalize(i),l=i+" property is deprecated and will be removed soon. Look at Konva change log for more information.";n.prototype[s]=function(){e.Util.error(l);const t=this.attrs[i];return void 0===t?a:t},t.Factory.addSetter(n,i,o,(function(){e.Util.error(l)})),t.Factory.addOverloadedGetterSetter(n,i)},backCompat(t,n){e.Util.each(n,(function(n,a){const o=t.prototype[a],s=r+e.Util._capitalize(n),l=i+e.Util._capitalize(n);function u(){o.apply(this,arguments),e.Util.error('"'+n+'" method is deprecated and will be removed soon. Use ""'+a+'" instead.')}t.prototype[n]=u,t.prototype[s]=u,t.prototype[l]=u}))},afterSetFilter(){this._filterUpToDate=!1}}}(y)),y}var S,x,w={},C={};function k(){if(S)return C;S=1,Object.defineProperty(C,"__esModule",{value:!0}),C.HitContext=C.SceneContext=C.Context=void 0;const t=f(),e=c();const n=["arc","arcTo","beginPath","bezierCurveTo","clearRect","clip","closePath","createLinearGradient","createPattern","createRadialGradient","drawImage","ellipse","fill","fillText","getImageData","createImageData","lineTo","moveTo","putImageData","quadraticCurveTo","rect","roundRect","restore","rotate","save","scale","setLineDash","setTransform","stroke","strokeText","transform","translate"];let r=class{constructor(t){this.canvas=t,e.Konva.enableTrace&&(this.traceArr=[],this._enableTrace())}fillShape(t){t.fillEnabled()&&this._fill(t)}_fill(t){}strokeShape(t){t.hasStroke()&&this._stroke(t)}_stroke(t){}fillStrokeShape(t){t.attrs.fillAfterStrokeEnabled?(this.strokeShape(t),this.fillShape(t)):(this.fillShape(t),this.strokeShape(t))}getTrace(e,n){let r,i,a,o,s=this.traceArr,l=s.length,u="";for(r=0;r<l;r++)i=s[r],a=i.method,a?(o=i.args,u+=a,e?u+="()":t.Util._isArray(o[0])?u+="(["+o.join(",")+"])":(n&&(o=o.map((t=>"number"==typeof t?Math.floor(t):t))),u+="("+o.join(",")+")")):(u+=i.property,e||(u+="="+i.val)),u+=";";return u}clearTrace(){this.traceArr=[]}_trace(t){let e,n=this.traceArr;n.push(t),e=n.length,e>=100&&n.shift()}reset(){const t=this.getCanvas().getPixelRatio();this.setTransform(1*t,0,0,1*t,0,0)}getCanvas(){return this.canvas}clear(t){const e=this.getCanvas();t?this.clearRect(t.x||0,t.y||0,t.width||0,t.height||0):this.clearRect(0,0,e.getWidth()/e.pixelRatio,e.getHeight()/e.pixelRatio)}_applyLineCap(t){const e=t.attrs.lineCap;e&&this.setAttr("lineCap",e)}_applyOpacity(t){const e=t.getAbsoluteOpacity();1!==e&&this.setAttr("globalAlpha",e)}_applyLineJoin(t){const e=t.attrs.lineJoin;e&&this.setAttr("lineJoin",e)}setAttr(t,e){this._context[t]=e}arc(t,e,n,r,i,a){this._context.arc(t,e,n,r,i,a)}arcTo(t,e,n,r,i){this._context.arcTo(t,e,n,r,i)}beginPath(){this._context.beginPath()}bezierCurveTo(t,e,n,r,i,a){this._context.bezierCurveTo(t,e,n,r,i,a)}clearRect(t,e,n,r){this._context.clearRect(t,e,n,r)}clip(...t){this._context.clip.apply(this._context,t)}closePath(){this._context.closePath()}createImageData(t,e){const n=arguments;return 2===n.length?this._context.createImageData(t,e):1===n.length?this._context.createImageData(t):void 0}createLinearGradient(t,e,n,r){return this._context.createLinearGradient(t,e,n,r)}createPattern(t,e){return this._context.createPattern(t,e)}createRadialGradient(t,e,n,r,i,a){return this._context.createRadialGradient(t,e,n,r,i,a)}drawImage(t,e,n,r,i,a,o,s,l){const u=arguments,c=this._context;3===u.length?c.drawImage(t,e,n):5===u.length?c.drawImage(t,e,n,r,i):9===u.length&&c.drawImage(t,e,n,r,i,a,o,s,l)}ellipse(t,e,n,r,i,a,o,s){this._context.ellipse(t,e,n,r,i,a,o,s)}isPointInPath(t,e,n,r){return n?this._context.isPointInPath(n,t,e,r):this._context.isPointInPath(t,e,r)}fill(...t){this._context.fill.apply(this._context,t)}fillRect(t,e,n,r){this._context.fillRect(t,e,n,r)}strokeRect(t,e,n,r){this._context.strokeRect(t,e,n,r)}fillText(t,e,n,r){r?this._context.fillText(t,e,n,r):this._context.fillText(t,e,n)}measureText(t){return this._context.measureText(t)}getImageData(t,e,n,r){return this._context.getImageData(t,e,n,r)}lineTo(t,e){this._context.lineTo(t,e)}moveTo(t,e){this._context.moveTo(t,e)}rect(t,e,n,r){this._context.rect(t,e,n,r)}roundRect(t,e,n,r,i){this._context.roundRect(t,e,n,r,i)}putImageData(t,e,n){this._context.putImageData(t,e,n)}quadraticCurveTo(t,e,n,r){this._context.quadraticCurveTo(t,e,n,r)}restore(){this._context.restore()}rotate(t){this._context.rotate(t)}save(){this._context.save()}scale(t,e){this._context.scale(t,e)}setLineDash(t){this._context.setLineDash?this._context.setLineDash(t):"mozDash"in this._context?this._context.mozDash=t:"webkitLineDash"in this._context&&(this._context.webkitLineDash=t)}getLineDash(){return this._context.getLineDash()}setTransform(t,e,n,r,i,a){this._context.setTransform(t,e,n,r,i,a)}stroke(t){t?this._context.stroke(t):this._context.stroke()}strokeText(t,e,n,r){this._context.strokeText(t,e,n,r)}transform(t,e,n,r,i,a){this._context.transform(t,e,n,r,i,a)}translate(t,e){this._context.translate(t,e)}_enableTrace(){let e,r,i=this,a=n.length,o=this.setAttr;const s=function(e){let n,a=i[e];i[e]=function(){return r=function(e){const n=[],r=e.length,i=t.Util;for(let t=0;t<r;t++){let r=e[t];i._isNumber(r)?r=Math.round(1e3*r)/1e3:i._isString(r)||(r+=""),n.push(r)}return n}(Array.prototype.slice.call(arguments,0)),n=a.apply(i,arguments),i._trace({method:e,args:r}),n}};for(e=0;e<a;e++)s(n[e]);i.setAttr=function(){o.apply(i,arguments);const t=arguments[0];let e=arguments[1];"shadowOffsetX"!==t&&"shadowOffsetY"!==t&&"shadowBlur"!==t||(e/=this.canvas.getPixelRatio()),i._trace({property:t,val:e})}}_applyGlobalCompositeOperation(t){const e=t.attrs.globalCompositeOperation;!e||"source-over"===e||this.setAttr("globalCompositeOperation",e)}};C.Context=r,["fillStyle","strokeStyle","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","letterSpacing","lineCap","lineDashOffset","lineJoin","lineWidth","miterLimit","direction","font","textAlign","textBaseline","globalAlpha","globalCompositeOperation","imageSmoothingEnabled"].forEach((function(t){Object.defineProperty(r.prototype,t,{get(){return this._context[t]},set(e){this._context[t]=e}})}));C.SceneContext=class extends r{constructor(t,{willReadFrequently:e=!1}={}){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:e})}_fillColor(t){const e=t.fill();this.setAttr("fillStyle",e),t._fillFunc(this)}_fillPattern(t){this.setAttr("fillStyle",t._getFillPattern()),t._fillFunc(this)}_fillLinearGradient(t){const e=t._getLinearGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fillRadialGradient(t){const e=t._getRadialGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fill(t){const e=t.fill(),n=t.getFillPriority();if(e&&"color"===n)return void this._fillColor(t);const r=t.getFillPatternImage();if(r&&"pattern"===n)return void this._fillPattern(t);const i=t.getFillLinearGradientColorStops();if(i&&"linear-gradient"===n)return void this._fillLinearGradient(t);const a=t.getFillRadialGradientColorStops();a&&"radial-gradient"===n?this._fillRadialGradient(t):e?this._fillColor(t):r?this._fillPattern(t):i?this._fillLinearGradient(t):a&&this._fillRadialGradient(t)}_strokeLinearGradient(t){const e=t.getStrokeLinearGradientStartPoint(),n=t.getStrokeLinearGradientEndPoint(),r=t.getStrokeLinearGradientColorStops(),i=this.createLinearGradient(e.x,e.y,n.x,n.y);if(r){for(let t=0;t<r.length;t+=2)i.addColorStop(r[t],r[t+1]);this.setAttr("strokeStyle",i)}}_stroke(t){const e=t.dash(),n=t.getStrokeScaleEnabled();if(t.hasStroke()){if(!n){this.save();const t=this.getCanvas().getPixelRatio();this.setTransform(t,0,0,t,0,0)}this._applyLineCap(t),e&&t.dashEnabled()&&(this.setLineDash(e),this.setAttr("lineDashOffset",t.dashOffset())),this.setAttr("lineWidth",t.strokeWidth()),t.getShadowForStrokeEnabled()||this.setAttr("shadowColor","rgba(0,0,0,0)");t.getStrokeLinearGradientColorStops()?this._strokeLinearGradient(t):this.setAttr("strokeStyle",t.stroke()),t._strokeFunc(this),n||this.restore()}}_applyShadow(t){var e,n,r;const i=null!==(e=t.getShadowRGBA())&&void 0!==e?e:"black",a=null!==(n=t.getShadowBlur())&&void 0!==n?n:5,o=null!==(r=t.getShadowOffset())&&void 0!==r?r:{x:0,y:0},s=t.getAbsoluteScale(),l=this.canvas.getPixelRatio(),u=s.x*l,c=s.y*l;this.setAttr("shadowColor",i),this.setAttr("shadowBlur",a*Math.min(Math.abs(u),Math.abs(c))),this.setAttr("shadowOffsetX",o.x*u),this.setAttr("shadowOffsetY",o.y*c)}};return C.HitContext=class extends r{constructor(t){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:!0})}_fill(t){this.save(),this.setAttr("fillStyle",t.colorKey),t._fillFuncHit(this),this.restore()}strokeShape(t){t.hasHitStroke()&&this._stroke(t)}_stroke(t){if(t.hasHitStroke()){const e=t.getStrokeScaleEnabled();if(!e){this.save();const t=this.getCanvas().getPixelRatio();this.setTransform(t,0,0,t,0,0)}this._applyLineCap(t);const n=t.hitStrokeWidth(),r="auto"===n?t.strokeWidth():n;this.setAttr("lineWidth",r),this.setAttr("strokeStyle",t.colorKey),t._strokeFuncHit(this),e||this.restore()}}},C}function P(){if(x)return w;x=1,Object.defineProperty(w,"__esModule",{value:!0}),w.HitCanvas=w.SceneCanvas=w.Canvas=void 0;const t=f(),e=k(),n=c();let r;let i=class{constructor(e){this.pixelRatio=1,this.width=0,this.height=0,this.isCache=!1;const i=(e||{}).pixelRatio||n.Konva.pixelRatio||function(){if(r)return r;const e=t.Util.createCanvasElement(),i=e.getContext("2d");return r=(n.Konva._global.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1),t.Util.releaseCanvas(e),r}();this.pixelRatio=i,this._canvas=t.Util.createCanvasElement(),this._canvas.style.padding="0",this._canvas.style.margin="0",this._canvas.style.border="0",this._canvas.style.background="transparent",this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0"}getContext(){return this.context}getPixelRatio(){return this.pixelRatio}setPixelRatio(t){const e=this.pixelRatio;this.pixelRatio=t,this.setSize(this.getWidth()/e,this.getHeight()/e)}setWidth(t){this.width=this._canvas.width=t*this.pixelRatio,this._canvas.style.width=t+"px";const e=this.pixelRatio;this.getContext()._context.scale(e,e)}setHeight(t){this.height=this._canvas.height=t*this.pixelRatio,this._canvas.style.height=t+"px";const e=this.pixelRatio;this.getContext()._context.scale(e,e)}getWidth(){return this.width}getHeight(){return this.height}setSize(t,e){this.setWidth(t||0),this.setHeight(e||0)}toDataURL(e,n){try{return this._canvas.toDataURL(e,n)}catch(r){try{return this._canvas.toDataURL()}catch(i){return t.Util.error("Unable to get data URL. "+i.message+" For more info read https://konvajs.org/docs/posts/Tainted_Canvas.html."),""}}}};w.Canvas=i;w.SceneCanvas=class extends i{constructor(t={width:0,height:0,willReadFrequently:!1}){super(t),this.context=new e.SceneContext(this,{willReadFrequently:t.willReadFrequently}),this.setSize(t.width,t.height)}};return w.HitCanvas=class extends i{constructor(t={width:0,height:0}){super(t),this.hitCanvas=!0,this.context=new e.HitContext(this),this.setSize(t.width,t.height)}},w}var T,E,F={};function N(){return T||(T=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.DD=void 0;const e=c(),n=f();t.DD={get isDragging(){let e=!1;return t.DD._dragElements.forEach((t=>{"dragging"===t.dragStatus&&(e=!0)})),e},justDragged:!1,get node(){let e;return t.DD._dragElements.forEach((t=>{e=t.node})),e},_dragElements:new Map,_drag(e){const r=[];t.DD._dragElements.forEach(((t,i)=>{const{node:a}=t,o=a.getStage();o.setPointersPositions(e),void 0===t.pointerId&&(t.pointerId=n.Util._getFirstPointerId(e));const s=o._changedPointerPositions.find((e=>e.id===t.pointerId));if(s){if("dragging"!==t.dragStatus){const n=a.dragDistance();if(Math.max(Math.abs(s.x-t.startPointerPos.x),Math.abs(s.y-t.startPointerPos.y))<n)return;if(a.startDrag({evt:e}),!a.isDragging())return}a._setDragPosition(e,t),r.push(a)}})),r.forEach((t=>{t.fire("dragmove",{type:"dragmove",target:t,evt:e},!0)}))},_endDragBefore(n){const r=[];t.DD._dragElements.forEach((i=>{const{node:a}=i,o=a.getStage();n&&o.setPointersPositions(n);if(!o._changedPointerPositions.find((t=>t.id===i.pointerId)))return;"dragging"!==i.dragStatus&&"stopped"!==i.dragStatus||(t.DD.justDragged=!0,e.Konva._mouseListenClick=!1,e.Konva._touchListenClick=!1,e.Konva._pointerListenClick=!1,i.dragStatus="stopped");const s=i.node.getLayer()||i.node instanceof e.Konva.Stage&&i.node;s&&-1===r.indexOf(s)&&r.push(s)})),r.forEach((t=>{t.draw()}))},_endDragAfter(e){t.DD._dragElements.forEach(((n,r)=>{"stopped"===n.dragStatus&&n.node.fire("dragend",{type:"dragend",target:n.node,evt:e},!0),"dragging"!==n.dragStatus&&t.DD._dragElements.delete(r)}))}},e.Konva.isBrowser&&(window.addEventListener("mouseup",t.DD._endDragBefore,!0),window.addEventListener("touchend",t.DD._endDragBefore,!0),window.addEventListener("touchcancel",t.DD._endDragBefore,!0),window.addEventListener("mousemove",t.DD._drag),window.addEventListener("touchmove",t.DD._drag),window.addEventListener("mouseup",t.DD._endDragAfter,!1),window.addEventListener("touchend",t.DD._endDragAfter,!1),window.addEventListener("touchcancel",t.DD._endDragAfter,!1))}(F)),F}function M(){if(E)return m;E=1,Object.defineProperty(m,"__esModule",{value:!0}),m.Node=void 0;const t=f(),e=_(),n=P(),r=c(),i=N(),a=b(),o="absoluteOpacity",s="allEventListeners",l="absoluteTransform",u="absoluteScale",h="canvas",d="listening",g="mouseenter",p="mouseleave",y="Shape",v=" ",S="stage",x="transform",w="visible",C=["xChange.konva","yChange.konva","scaleXChange.konva","scaleYChange.konva","skewXChange.konva","skewYChange.konva","rotationChange.konva","offsetXChange.konva","offsetYChange.konva","transformsEnabledChange.konva"].join(v);let k=1,T=class e{constructor(t){this._id=k++,this.eventListeners={},this.attrs={},this.index=0,this._allEventListeners=null,this.parent=null,this._cache=new Map,this._attachedDepsListeners=new Map,this._lastPos=null,this._batchingTransformChange=!1,this._needClearTransformCache=!1,this._filterUpToDate=!1,this._isUnderCache=!1,this._dragEventId=null,this._shouldFireChangeEvents=!1,this.setAttrs(t),this._shouldFireChangeEvents=!0}hasChildren(){return!1}_clearCache(t){t!==x&&t!==l||!this._cache.get(t)?t?this._cache.delete(t):this._cache.clear():this._cache.get(t).dirty=!0}_getCache(t,e){let n=this._cache.get(t);return(void 0===n||(t===x||t===l)&&!0===n.dirty)&&(n=e.call(this),this._cache.set(t,n)),n}_calculate(t,e,n){if(!this._attachedDepsListeners.get(t)){const n=e.map((t=>t+"Change.konva")).join(v);this.on(n,(()=>{this._clearCache(t)})),this._attachedDepsListeners.set(t,!0)}return this._getCache(t,n)}_getCanvasCache(){return this._cache.get(h)}_clearSelfAndDescendantCache(t){this._clearCache(t),t===l&&this.fire("absoluteTransformChange")}clearCache(){if(this._cache.has(h)){const{scene:e,filter:n,hit:r}=this._cache.get(h);t.Util.releaseCanvas(e,n,r),this._cache.delete(h)}return this._clearSelfAndDescendantCache(),this._requestDraw(),this}cache(e){const r=e||{};let i={};void 0!==r.x&&void 0!==r.y&&void 0!==r.width&&void 0!==r.height||(i=this.getClientRect({skipTransform:!0,relativeTo:this.getParent()||void 0}));let a=Math.ceil(r.width||i.width),s=Math.ceil(r.height||i.height),l=r.pixelRatio,c=void 0===r.x?Math.floor(i.x):r.x,d=void 0===r.y?Math.floor(i.y):r.y,f=r.offset||0,g=r.drawBorder||!1,p=r.hitCanvasPixelRatio||1;if(!a||!s)return void t.Util.error("Can not cache the node. Width or height of the node equals 0. Caching is skipped.");a+=2*f+(Math.abs(Math.round(i.x)-c)>.5?1:0),s+=2*f+(Math.abs(Math.round(i.y)-d)>.5?1:0),c-=f,d-=f;const m=new n.SceneCanvas({pixelRatio:l,width:a,height:s}),y=new n.SceneCanvas({pixelRatio:l,width:0,height:0,willReadFrequently:!0}),v=new n.HitCanvas({pixelRatio:p,width:a,height:s}),b=m.getContext(),_=v.getContext();return v.isCache=!0,m.isCache=!0,this._cache.delete(h),this._filterUpToDate=!1,!1===r.imageSmoothingEnabled&&(m.getContext()._context.imageSmoothingEnabled=!1,y.getContext()._context.imageSmoothingEnabled=!1),b.save(),_.save(),b.translate(-c,-d),_.translate(-c,-d),this._isUnderCache=!0,this._clearSelfAndDescendantCache(o),this._clearSelfAndDescendantCache(u),this.drawScene(m,this),this.drawHit(v,this),this._isUnderCache=!1,b.restore(),_.restore(),g&&(b.save(),b.beginPath(),b.rect(0,0,a,s),b.closePath(),b.setAttr("strokeStyle","red"),b.setAttr("lineWidth",5),b.stroke(),b.restore()),this._cache.set(h,{scene:m,filter:y,hit:v,x:c,y:d}),this._requestDraw(),this}isCached(){return this._cache.has(h)}getClientRect(t){throw new Error('abstract "getClientRect" method call')}_transformedRect(t,e){const n=[{x:t.x,y:t.y},{x:t.x+t.width,y:t.y},{x:t.x+t.width,y:t.y+t.height},{x:t.x,y:t.y+t.height}];let r=1/0,i=1/0,a=-1/0,o=-1/0;const s=this.getAbsoluteTransform(e);return n.forEach((function(t){const e=s.point(t);void 0===r&&(r=a=e.x,i=o=e.y),r=Math.min(r,e.x),i=Math.min(i,e.y),a=Math.max(a,e.x),o=Math.max(o,e.y)})),{x:r,y:i,width:a-r,height:o-i}}_drawCachedSceneCanvas(t){t.save(),t._applyOpacity(this),t._applyGlobalCompositeOperation(this);const e=this._getCanvasCache();t.translate(e.x,e.y);const n=this._getCachedSceneCanvas(),r=n.pixelRatio;t.drawImage(n._canvas,0,0,n.width/r,n.height/r),t.restore()}_drawCachedHitCanvas(t){const e=this._getCanvasCache(),n=e.hit;t.save(),t.translate(e.x,e.y),t.drawImage(n._canvas,0,0,n.width/n.pixelRatio,n.height/n.pixelRatio),t.restore()}_getCachedSceneCanvas(){let e,n,r,i,a=this.filters(),o=this._getCanvasCache(),s=o.scene,l=o.filter,u=l.getContext();if(a){if(!this._filterUpToDate){const o=s.pixelRatio;l.setSize(s.width/s.pixelRatio,s.height/s.pixelRatio);try{for(e=a.length,u.clear(),u.drawImage(s._canvas,0,0,s.getWidth()/o,s.getHeight()/o),n=u.getImageData(0,0,l.getWidth(),l.getHeight()),r=0;r<e;r++)i=a[r],"function"==typeof i?(i.call(this,n),u.putImageData(n,0,0)):t.Util.error("Filter should be type of function, but got "+typeof i+" instead. Please check correct filters")}catch(c){t.Util.error("Unable to apply filter. "+c.message+" This post my help you https://konvajs.org/docs/posts/Tainted_Canvas.html.")}this._filterUpToDate=!0}return l}return s}on(t,e){if(this._cache&&this._cache.delete(s),3===arguments.length)return this._delegate.apply(this,arguments);let n,r,i,a,o,l=t.split(v),u=l.length;for(n=0;n<u;n++)r=l[n],i=r.split("."),a=i[0],o=i[1]||"",this.eventListeners[a]||(this.eventListeners[a]=[]),this.eventListeners[a].push({name:o,handler:e});return this}off(t,e){let n,r,i,a,o,l,u=(t||"").split(v),c=u.length;if(this._cache&&this._cache.delete(s),!t)for(r in this.eventListeners)this._off(r);for(n=0;n<c;n++)if(i=u[n],a=i.split("."),o=a[0],l=a[1],o)this.eventListeners[o]&&this._off(o,l,e);else for(r in this.eventListeners)this._off(r,l,e);return this}dispatchEvent(t){const e={target:this,type:t.type,evt:t};return this.fire(t.type,e),this}addEventListener(t,e){return this.on(t,(function(t){e.call(this,t.evt)})),this}removeEventListener(t){return this.off(t),this}_delegate(e,n,r){const i=this;this.on(e,(function(e){const a=e.target.findAncestors(n,!0,i);for(let n=0;n<a.length;n++)(e=t.Util.cloneObject(e)).currentTarget=a[n],r.call(a[n],e)}))}remove(){return this.isDragging()&&this.stopDrag(),i.DD._dragElements.delete(this._id),this._remove(),this}_clearCaches(){this._clearSelfAndDescendantCache(l),this._clearSelfAndDescendantCache(o),this._clearSelfAndDescendantCache(u),this._clearSelfAndDescendantCache(S),this._clearSelfAndDescendantCache(w),this._clearSelfAndDescendantCache(d)}_remove(){this._clearCaches();const t=this.getParent();t&&t.children&&(t.children.splice(this.index,1),t._setChildrenIndices(),this.parent=null)}destroy(){return this.remove(),this.clearCache(),this}getAttr(e){const n="get"+t.Util._capitalize(e);return t.Util._isFunction(this[n])?this[n]():this.attrs[e]}getAncestors(){let t=this.getParent(),e=[];for(;t;)e.push(t),t=t.getParent();return e}getAttrs(){return this.attrs||{}}setAttrs(e){return this._batchTransformChanges((()=>{let n,r;if(!e)return this;for(n in e)"children"!==n&&(r="set"+t.Util._capitalize(n),t.Util._isFunction(this[r])?this[r](e[n]):this._setAttr(n,e[n]))})),this}isListening(){return this._getCache(d,this._isListening)}_isListening(t){if(!this.listening())return!1;const e=this.getParent();return!e||e===t||this===t||e._isListening(t)}isVisible(){return this._getCache(w,this._isVisible)}_isVisible(t){if(!this.visible())return!1;const e=this.getParent();return!e||e===t||this===t||e._isVisible(t)}shouldDrawHit(t,e=!1){if(t)return this._isVisible(t)&&this._isListening(t);const n=this.getLayer();let a=!1;i.DD._dragElements.forEach((t=>{"dragging"===t.dragStatus&&("Stage"===t.node.nodeType||t.node.getLayer()===n)&&(a=!0)}));const o=!e&&!r.Konva.hitOnDragEnabled&&(a||r.Konva.isTransforming());return this.isListening()&&this.isVisible()&&!o}show(){return this.visible(!0),this}hide(){return this.visible(!1),this}getZIndex(){return this.index||0}getAbsoluteZIndex(){let t,e,n,r,i=this.getDepth(),a=this,o=0;const s=this.getStage();return"Stage"!==a.nodeType&&s&&function s(l){for(t=[],e=l.length,n=0;n<e;n++)r=l[n],o++,r.nodeType!==y&&(t=t.concat(r.getChildren().slice())),r._id===a._id&&(n=e);t.length>0&&t[0].getDepth()<=i&&s(t)}(s.getChildren()),o}getDepth(){let t=0,e=this.parent;for(;e;)t++,e=e.parent;return t}_batchTransformChanges(t){this._batchingTransformChange=!0,t(),this._batchingTransformChange=!1,this._needClearTransformCache&&(this._clearCache(x),this._clearSelfAndDescendantCache(l)),this._needClearTransformCache=!1}setPosition(t){return this._batchTransformChanges((()=>{this.x(t.x),this.y(t.y)})),this}getPosition(){return{x:this.x(),y:this.y()}}getRelativePointerPosition(){const t=this.getStage();if(!t)return null;const e=t.getPointerPosition();if(!e)return null;const n=this.getAbsoluteTransform().copy();return n.invert(),n.point(e)}getAbsolutePosition(e){let n=!1,r=this.parent;for(;r;){if(r.isCached()){n=!0;break}r=r.parent}n&&!e&&(e=!0);const i=this.getAbsoluteTransform(e).getMatrix(),a=new t.Transform,o=this.offset();return a.m=i.slice(),a.translate(o.x,o.y),a.getTranslation()}setAbsolutePosition(t){const{x:e,y:n,...r}=this._clearTransform();this.attrs.x=e,this.attrs.y=n,this._clearCache(x);const i=this._getAbsoluteTransform().copy();return i.invert(),i.translate(t.x,t.y),t={x:this.attrs.x+i.getTranslation().x,y:this.attrs.y+i.getTranslation().y},this._setTransform(r),this.setPosition({x:t.x,y:t.y}),this._clearCache(x),this._clearSelfAndDescendantCache(l),this}_setTransform(t){let e;for(e in t)this.attrs[e]=t[e]}_clearTransform(){const t={x:this.x(),y:this.y(),rotation:this.rotation(),scaleX:this.scaleX(),scaleY:this.scaleY(),offsetX:this.offsetX(),offsetY:this.offsetY(),skewX:this.skewX(),skewY:this.skewY()};return this.attrs.x=0,this.attrs.y=0,this.attrs.rotation=0,this.attrs.scaleX=1,this.attrs.scaleY=1,this.attrs.offsetX=0,this.attrs.offsetY=0,this.attrs.skewX=0,this.attrs.skewY=0,t}move(t){let e=t.x,n=t.y,r=this.x(),i=this.y();return void 0!==e&&(r+=e),void 0!==n&&(i+=n),this.setPosition({x:r,y:i}),this}_eachAncestorReverse(t,e){let n,r,i=[],a=this.getParent();if(!e||e._id!==this._id){for(i.unshift(this);a&&(!e||a._id!==e._id);)i.unshift(a),a=a.parent;for(n=i.length,r=0;r<n;r++)t(i[r])}}rotate(t){return this.rotation(this.rotation()+t),this}moveToTop(){if(!this.parent)return t.Util.warn("Node has no parent. moveToTop function is ignored."),!1;const e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.push(this),this.parent._setChildrenIndices(),!0)}moveUp(){if(!this.parent)return t.Util.warn("Node has no parent. moveUp function is ignored."),!1;const e=this.index;return e<this.parent.getChildren().length-1&&(this.parent.children.splice(e,1),this.parent.children.splice(e+1,0,this),this.parent._setChildrenIndices(),!0)}moveDown(){if(!this.parent)return t.Util.warn("Node has no parent. moveDown function is ignored."),!1;const e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.splice(e-1,0,this),this.parent._setChildrenIndices(),!0)}moveToBottom(){if(!this.parent)return t.Util.warn("Node has no parent. moveToBottom function is ignored."),!1;const e=this.index;return e>0&&(this.parent.children.splice(e,1),this.parent.children.unshift(this),this.parent._setChildrenIndices(),!0)}setZIndex(e){if(!this.parent)return t.Util.warn("Node has no parent. zIndex parameter is ignored."),this;(e<0||e>=this.parent.children.length)&&t.Util.warn("Unexpected value "+e+" for zIndex property. zIndex is just index of a node in children of its parent. Expected value is from 0 to "+(this.parent.children.length-1)+".");const n=this.index;return this.parent.children.splice(n,1),this.parent.children.splice(e,0,this),this.parent._setChildrenIndices(),this}getAbsoluteOpacity(){return this._getCache(o,this._getAbsoluteOpacity)}_getAbsoluteOpacity(){let t=this.opacity();const e=this.getParent();return e&&!e._isUnderCache&&(t*=e.getAbsoluteOpacity()),t}moveTo(t){return this.getParent()!==t&&(this._remove(),t.add(this)),this}toObject(){let e,n,r,i,a,o=this.getAttrs();const s={attrs:{},className:this.getClassName()};for(e in o)n=o[e],a=t.Util.isObject(n)&&!t.Util._isPlainObject(n)&&!t.Util._isArray(n),a||(r="function"==typeof this[e]&&this[e],delete o[e],i=r?r.call(this):null,o[e]=n,i!==n&&(s.attrs[e]=n));return t.Util._prepareToStringify(s)}toJSON(){return JSON.stringify(this.toObject())}getParent(){return this.parent}findAncestors(t,e,n){const r=[];e&&this._isMatch(t)&&r.push(this);let i=this.parent;for(;i;){if(i===n)return r;i._isMatch(t)&&r.push(i),i=i.parent}return r}isAncestorOf(t){return!1}findAncestor(t,e,n){return this.findAncestors(t,e,n)[0]}_isMatch(e){if(!e)return!1;if("function"==typeof e)return e(this);let n,r,i=e.replace(/ /g,"").split(","),a=i.length;for(n=0;n<a;n++)if(r=i[n],t.Util.isValidSelector(r)||(t.Util.warn('Selector "'+r+'" is invalid. Allowed selectors examples are "#foo", ".bar" or "Group".'),t.Util.warn('If you have a custom shape with such className, please change it to start with upper letter like "Triangle".'),t.Util.warn("Konva is awesome, right?")),"#"===r.charAt(0)){if(this.id()===r.slice(1))return!0}else if("."===r.charAt(0)){if(this.hasName(r.slice(1)))return!0}else if(this.className===r||this.nodeType===r)return!0;return!1}getLayer(){const t=this.getParent();return t?t.getLayer():null}getStage(){return this._getCache(S,this._getStage)}_getStage(){const t=this.getParent();return t?t.getStage():null}fire(t,e={},n){return e.target=e.target||this,n?this._fireAndBubble(t,e):this._fire(t,e),this}getAbsoluteTransform(t){return t?this._getAbsoluteTransform(t):this._getCache(l,this._getAbsoluteTransform)}_getAbsoluteTransform(e){let n;if(e)return n=new t.Transform,this._eachAncestorReverse((function(t){const e=t.transformsEnabled();"all"===e?n.multiply(t.getTransform()):"position"===e&&n.translate(t.x()-t.offsetX(),t.y()-t.offsetY())}),e),n;{n=this._cache.get(l)||new t.Transform,this.parent?this.parent.getAbsoluteTransform().copyInto(n):n.reset();const e=this.transformsEnabled();if("all"===e)n.multiply(this.getTransform());else if("position"===e){const t=this.attrs.x||0,e=this.attrs.y||0,r=this.attrs.offsetX||0,i=this.attrs.offsetY||0;n.translate(t-r,e-i)}return n.dirty=!1,n}}getAbsoluteScale(t){let e=this;for(;e;)e._isUnderCache&&(t=e),e=e.getParent();const n=this.getAbsoluteTransform(t).decompose();return{x:n.scaleX,y:n.scaleY}}getAbsoluteRotation(){return this.getAbsoluteTransform().decompose().rotation}getTransform(){return this._getCache(x,this._getTransform)}_getTransform(){var e,n;const i=this._cache.get(x)||new t.Transform;i.reset();const a=this.x(),o=this.y(),s=r.Konva.getAngle(this.rotation()),l=null!==(e=this.attrs.scaleX)&&void 0!==e?e:1,u=null!==(n=this.attrs.scaleY)&&void 0!==n?n:1,c=this.attrs.skewX||0,h=this.attrs.skewY||0,d=this.attrs.offsetX||0,f=this.attrs.offsetY||0;return 0===a&&0===o||i.translate(a,o),0!==s&&i.rotate(s),0===c&&0===h||i.skew(c,h),1===l&&1===u||i.scale(l,u),0===d&&0===f||i.translate(-1*d,-1*f),i.dirty=!1,i}clone(e){let n,r,i,a,o,s=t.Util.cloneObject(this.attrs);for(n in e)s[n]=e[n];const l=new this.constructor(s);for(n in this.eventListeners)for(r=this.eventListeners[n],i=r.length,a=0;a<i;a++)o=r[a],o.name.indexOf("konva")<0&&(l.eventListeners[n]||(l.eventListeners[n]=[]),l.eventListeners[n].push(o));return l}_toKonvaCanvas(t){t=t||{};const e=this.getClientRect(),r=this.getStage(),i=void 0!==t.x?t.x:Math.floor(e.x),a=void 0!==t.y?t.y:Math.floor(e.y),o=t.pixelRatio||1,s=new n.SceneCanvas({width:t.width||Math.ceil(e.width)||(r?r.width():0),height:t.height||Math.ceil(e.height)||(r?r.height():0),pixelRatio:o}),l=s.getContext(),u=new n.SceneCanvas({width:s.width/s.pixelRatio+Math.abs(i),height:s.height/s.pixelRatio+Math.abs(a),pixelRatio:s.pixelRatio});return!1===t.imageSmoothingEnabled&&(l._context.imageSmoothingEnabled=!1),l.save(),(i||a)&&l.translate(-1*i,-1*a),this.drawScene(s,void 0,u),l.restore(),s}toCanvas(t){return this._toKonvaCanvas(t)._canvas}toDataURL(t){const e=(t=t||{}).mimeType||null,n=t.quality||null,r=this._toKonvaCanvas(t).toDataURL(e,n);return t.callback&&t.callback(r),r}toImage(e){return new Promise(((n,r)=>{try{const r=null==e?void 0:e.callback;r&&delete e.callback,t.Util._urlToImage(this.toDataURL(e),(function(t){n(t),null==r||r(t)}))}catch(i){r(i)}}))}toBlob(t){return new Promise(((e,n)=>{try{const n=null==t?void 0:t.callback;n&&delete t.callback,this.toCanvas(t).toBlob((t=>{e(t),null==n||n(t)}),null==t?void 0:t.mimeType,null==t?void 0:t.quality)}catch(r){n(r)}}))}setSize(t){return this.width(t.width),this.height(t.height),this}getSize(){return{width:this.width(),height:this.height()}}getClassName(){return this.className||this.nodeType}getType(){return this.nodeType}getDragDistance(){return void 0!==this.attrs.dragDistance?this.attrs.dragDistance:this.parent?this.parent.getDragDistance():r.Konva.dragDistance}_off(t,e,n){let r,i,a,o=this.eventListeners[t];for(r=0;r<o.length;r++)if(i=o[r].name,a=o[r].handler,!("konva"===i&&"konva"!==e||e&&i!==e||n&&n!==a)){if(o.splice(r,1),0===o.length){delete this.eventListeners[t];break}r--}}_fireChangeEvent(t,e,n){this._fire(t+"Change",{oldVal:e,newVal:n})}addName(t){if(!this.hasName(t)){const e=this.name(),n=e?e+" "+t:t;this.name(n)}return this}hasName(t){if(!t)return!1;const e=this.name();if(!e)return!1;return-1!==(e||"").split(/\s/g).indexOf(t)}removeName(t){const e=(this.name()||"").split(/\s/g),n=e.indexOf(t);return-1!==n&&(e.splice(n,1),this.name(e.join(" "))),this}setAttr(e,n){const r=this["set"+t.Util._capitalize(e)];return t.Util._isFunction(r)?r.call(this,n):this._setAttr(e,n),this}_requestDraw(){if(r.Konva.autoDrawEnabled){const t=this.getLayer()||this.getStage();null==t||t.batchDraw()}}_setAttr(e,n){const r=this.attrs[e];(r!==n||t.Util.isObject(n))&&(null==n?delete this.attrs[e]:this.attrs[e]=n,this._shouldFireChangeEvents&&this._fireChangeEvent(e,r,n),this._requestDraw())}_setComponentAttr(t,e,n){let r;void 0!==n&&(r=this.attrs[t],r||(this.attrs[t]=this.getAttr(t)),this.attrs[t][e]=n,this._fireChangeEvent(t,r,n))}_fireAndBubble(t,e,n){e&&this.nodeType===y&&(e.target=this);if(!((t===g||t===p)&&(n&&(this===n||this.isAncestorOf&&this.isAncestorOf(n))||"Stage"===this.nodeType&&!n))){this._fire(t,e);const r=(t===g||t===p)&&n&&n.isAncestorOf&&n.isAncestorOf(this)&&!n.isAncestorOf(this.parent);(e&&!e.cancelBubble||!e)&&this.parent&&this.parent.isListening()&&!r&&(n&&n.parent?this._fireAndBubble.call(this.parent,t,e,n):this._fireAndBubble.call(this.parent,t,e))}}_getProtoListeners(t){var e,n,r;const i=null!==(e=this._cache.get(s))&&void 0!==e?e:{};let a=null==i?void 0:i[t];if(void 0===a){a=[];let e=Object.getPrototypeOf(this);for(;e;){const i=null!==(r=null===(n=e.eventListeners)||void 0===n?void 0:n[t])&&void 0!==r?r:[];a.push(...i),e=Object.getPrototypeOf(e)}i[t]=a,this._cache.set(s,i)}return a}_fire(t,e){(e=e||{}).currentTarget=this,e.type=t;const n=this._getProtoListeners(t);if(n)for(var r=0;r<n.length;r++)n[r].handler.call(this,e);const i=this.eventListeners[t];if(i)for(r=0;r<i.length;r++)i[r].handler.call(this,e)}draw(){return this.drawScene(),this.drawHit(),this}_createDragElement(t){const e=t?t.pointerId:void 0,n=this.getStage(),r=this.getAbsolutePosition();if(!n)return;const a=n._getPointerById(e)||n._changedPointerPositions[0]||r;i.DD._dragElements.set(this._id,{node:this,startPointerPos:a,offset:{x:a.x-r.x,y:a.y-r.y},dragStatus:"ready",pointerId:e})}startDrag(t,e=!0){i.DD._dragElements.has(this._id)||this._createDragElement(t);i.DD._dragElements.get(this._id).dragStatus="dragging",this.fire("dragstart",{type:"dragstart",target:this,evt:t&&t.evt},e)}_setDragPosition(e,n){const r=this.getStage()._getPointerById(n.pointerId);if(!r)return;let i={x:r.x-n.offset.x,y:r.y-n.offset.y};const a=this.dragBoundFunc();if(void 0!==a){const n=a.call(this,i,e);n?i=n:t.Util.warn("dragBoundFunc did not return any value. That is unexpected behavior. You must return new absolute position from dragBoundFunc.")}this._lastPos&&this._lastPos.x===i.x&&this._lastPos.y===i.y||(this.setAbsolutePosition(i),this._requestDraw()),this._lastPos=i}stopDrag(t){const e=i.DD._dragElements.get(this._id);e&&(e.dragStatus="stopped"),i.DD._endDragBefore(t),i.DD._endDragAfter(t)}setDraggable(t){this._setAttr("draggable",t),this._dragChange()}isDragging(){const t=i.DD._dragElements.get(this._id);return!!t&&"dragging"===t.dragStatus}_listenDrag(){this._dragCleanup(),this.on("mousedown.konva touchstart.konva",(function(t){if(!(!(void 0!==t.evt.button)||r.Konva.dragButtons.indexOf(t.evt.button)>=0))return;if(this.isDragging())return;let e=!1;i.DD._dragElements.forEach((t=>{this.isAncestorOf(t.node)&&(e=!0)})),e||this._createDragElement(t)}))}_dragChange(){if(this.attrs.draggable)this._listenDrag();else{this._dragCleanup();if(!this.getStage())return;const t=i.DD._dragElements.get(this._id),e=t&&"dragging"===t.dragStatus,n=t&&"ready"===t.dragStatus;e?this.stopDrag():n&&i.DD._dragElements.delete(this._id)}}_dragCleanup(){this.off("mousedown.konva"),this.off("touchstart.konva")}isClientRectOnScreen(e={x:0,y:0}){const n=this.getStage();if(!n)return!1;const r={x:-e.x,y:-e.y,width:n.width()+2*e.x,height:n.height()+2*e.y};return t.Util.haveIntersection(r,this.getClientRect())}static create(e,n){return t.Util._isString(e)&&(e=JSON.parse(e)),this._createNode(e,n)}static _createNode(n,i){let a,o,s,l=e.prototype.getClassName.call(n),u=n.children;i&&(n.attrs.container=i),r.Konva[l]||(t.Util.warn('Can not find a node with class name "'+l+'". Fallback to "Shape".'),l="Shape");if(a=new(0,r.Konva[l])(n.attrs),u)for(o=u.length,s=0;s<o;s++)a.add(e._createNode(u[s]));return a}};m.Node=T,T.prototype.nodeType="Node",T.prototype._attrsAffectingSize=[],T.prototype.eventListeners={},T.prototype.on.call(T.prototype,C,(function(){this._batchingTransformChange?this._needClearTransformCache=!0:(this._clearCache(x),this._clearSelfAndDescendantCache(l))})),T.prototype.on.call(T.prototype,"visibleChange.konva",(function(){this._clearSelfAndDescendantCache(w)})),T.prototype.on.call(T.prototype,"listeningChange.konva",(function(){this._clearSelfAndDescendantCache(d)})),T.prototype.on.call(T.prototype,"opacityChange.konva",(function(){this._clearSelfAndDescendantCache(o)}));const F=e.Factory.addGetterSetter;return F(T,"zIndex"),F(T,"absolutePosition"),F(T,"position"),F(T,"x",0,(0,a.getNumberValidator)()),F(T,"y",0,(0,a.getNumberValidator)()),F(T,"globalCompositeOperation","source-over",(0,a.getStringValidator)()),F(T,"opacity",1,(0,a.getNumberValidator)()),F(T,"name","",(0,a.getStringValidator)()),F(T,"id","",(0,a.getStringValidator)()),F(T,"rotation",0,(0,a.getNumberValidator)()),e.Factory.addComponentsGetterSetter(T,"scale",["x","y"]),F(T,"scaleX",1,(0,a.getNumberValidator)()),F(T,"scaleY",1,(0,a.getNumberValidator)()),e.Factory.addComponentsGetterSetter(T,"skew",["x","y"]),F(T,"skewX",0,(0,a.getNumberValidator)()),F(T,"skewY",0,(0,a.getNumberValidator)()),e.Factory.addComponentsGetterSetter(T,"offset",["x","y"]),F(T,"offsetX",0,(0,a.getNumberValidator)()),F(T,"offsetY",0,(0,a.getNumberValidator)()),F(T,"dragDistance",void 0,(0,a.getNumberValidator)()),F(T,"width",0,(0,a.getNumberValidator)()),F(T,"height",0,(0,a.getNumberValidator)()),F(T,"listening",!0,(0,a.getBooleanValidator)()),F(T,"preventDefault",!0,(0,a.getBooleanValidator)()),F(T,"filters",void 0,(function(t){return this._filterUpToDate=!1,t})),F(T,"visible",!0,(0,a.getBooleanValidator)()),F(T,"transformsEnabled","all",(0,a.getStringValidator)()),F(T,"size"),F(T,"dragBoundFunc"),F(T,"draggable",!1,(0,a.getBooleanValidator)()),e.Factory.backCompat(T,{rotateDeg:"rotate",setRotationDeg:"setRotation",getRotationDeg:"getRotation"}),m}var A,R={};function L(){if(A)return R;A=1,Object.defineProperty(R,"__esModule",{value:!0}),R.Container=void 0;const t=_(),e=M(),n=b();let r=class extends e.Node{constructor(){super(...arguments),this.children=[]}getChildren(t){if(!t)return this.children||[];const e=this.children||[],n=[];return e.forEach((function(e){t(e)&&n.push(e)})),n}hasChildren(){return this.getChildren().length>0}removeChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.remove()})),this.children=[],this._requestDraw(),this}destroyChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.destroy()})),this.children=[],this._requestDraw(),this}add(...t){if(0===t.length)return this;if(t.length>1){for(let e=0;e<t.length;e++)this.add(t[e]);return this}const e=t[0];return e.getParent()?(e.moveTo(this),this):(this._validateAdd(e),e.index=this.getChildren().length,e.parent=this,e._clearCaches(),this.getChildren().push(e),this._fire("add",{child:e}),this._requestDraw(),this)}destroy(){return this.hasChildren()&&this.destroyChildren(),super.destroy(),this}find(t){return this._generalFind(t,!1)}findOne(t){const e=this._generalFind(t,!0);return e.length>0?e[0]:void 0}_generalFind(t,e){const n=[];return this._descendants((r=>{const i=r._isMatch(t);return i&&n.push(r),!(!i||!e)})),n}_descendants(t){let e=!1;const n=this.getChildren();for(const r of n){if(e=t(r),e)return!0;if(r.hasChildren()&&(e=r._descendants(t),e))return!0}return!1}toObject(){const t=e.Node.prototype.toObject.call(this);return t.children=[],this.getChildren().forEach((e=>{t.children.push(e.toObject())})),t}isAncestorOf(t){let e=t.getParent();for(;e;){if(e._id===this._id)return!0;e=e.getParent()}return!1}clone(t){const n=e.Node.prototype.clone.call(this,t);return this.getChildren().forEach((function(t){n.add(t.clone())})),n}getAllIntersections(t){const e=[];return this.find("Shape").forEach((n=>{n.isVisible()&&n.intersects(t)&&e.push(n)})),e}_clearSelfAndDescendantCache(t){var e;super._clearSelfAndDescendantCache(t),this.isCached()||null===(e=this.children)||void 0===e||e.forEach((function(e){e._clearSelfAndDescendantCache(t)}))}_setChildrenIndices(){var t;null===(t=this.children)||void 0===t||t.forEach((function(t,e){t.index=e})),this._requestDraw()}drawScene(t,e,n){const r=this.getLayer(),i=t||r&&r.getCanvas(),a=i&&i.getContext(),o=this._getCanvasCache(),s=o&&o.scene,l=i&&i.isCache;if(!this.isVisible()&&!l)return this;if(s){a.save();const t=this.getAbsoluteTransform(e).getMatrix();a.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedSceneCanvas(a),a.restore()}else this._drawChildren("drawScene",i,e,n);return this}drawHit(t,e){if(!this.shouldDrawHit(e))return this;const n=this.getLayer(),r=t||n&&n.hitCanvas,i=r&&r.getContext(),a=this._getCanvasCache();if(a&&a.hit){i.save();const t=this.getAbsoluteTransform(e).getMatrix();i.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedHitCanvas(i),i.restore()}else this._drawChildren("drawHit",r,e);return this}_drawChildren(t,e,n,r){var i;const a=e&&e.getContext(),o=this.clipWidth(),s=this.clipHeight(),l=this.clipFunc(),u="number"==typeof o&&"number"==typeof s||l,c=n===this;if(u){a.save();const t=this.getAbsoluteTransform(n);let e,r=t.getMatrix();if(a.transform(r[0],r[1],r[2],r[3],r[4],r[5]),a.beginPath(),l)e=l.call(this,a,this);else{const t=this.clipX(),e=this.clipY();a.rect(t||0,e||0,o,s)}a.clip.apply(a,e),r=t.copy().invert().getMatrix(),a.transform(r[0],r[1],r[2],r[3],r[4],r[5])}const h=!c&&"source-over"!==this.globalCompositeOperation()&&"drawScene"===t;h&&(a.save(),a._applyGlobalCompositeOperation(this)),null===(i=this.children)||void 0===i||i.forEach((function(i){i[t](e,n,r)})),h&&a.restore(),u&&a.restore()}getClientRect(t={}){var e;const n=t.skipTransform,r=t.relativeTo;let i,a,o,s,l={x:1/0,y:1/0,width:0,height:0};const u=this;null===(e=this.children)||void 0===e||e.forEach((function(e){if(!e.visible())return;const n=e.getClientRect({relativeTo:u,skipShadow:t.skipShadow,skipStroke:t.skipStroke});0===n.width&&0===n.height||(void 0===i?(i=n.x,a=n.y,o=n.x+n.width,s=n.y+n.height):(i=Math.min(i,n.x),a=Math.min(a,n.y),o=Math.max(o,n.x+n.width),s=Math.max(s,n.y+n.height)))}));const c=this.find("Shape");let h=!1;for(let d=0;d<c.length;d++){if(c[d]._isVisible(this)){h=!0;break}}return l=h&&void 0!==i?{x:i,y:a,width:o-i,height:s-a}:{x:0,y:0,width:0,height:0},n?l:this._transformedRect(l,r)}};return R.Container=r,t.Factory.addComponentsGetterSetter(r,"clip",["x","y","width","height"]),t.Factory.addGetterSetter(r,"clipX",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipY",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipWidth",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipHeight",void 0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(r,"clipFunc"),R}var D,z,I={},G={};function O(){if(D)return G;D=1,Object.defineProperty(G,"__esModule",{value:!0}),G.getCapturedShape=function(t){return e.get(t)},G.createEvent=r,G.hasPointerCapture=function(t,n){return e.get(t)===n},G.setPointerCapture=function(t,a){i(t);if(!a.getStage())return;e.set(t,a),n&&a._fire("gotpointercapture",r(new PointerEvent("gotpointercapture")))},G.releaseCapture=i;const t=c(),e=new Map,n=void 0!==t.Konva._global.PointerEvent;function r(t){return{evt:t,pointerId:t.pointerId}}function i(t,i){const a=e.get(t);if(!a)return;const o=a.getStage();o&&o.content,e.delete(t),n&&a._fire("lostpointercapture",r(new PointerEvent("lostpointercapture")))}return G}var U,B,H={},V={};function j(){return U||(U=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Shape=t.shapes=void 0;const e=c(),n=f(),r=_(),i=M(),a=b(),o=c(),s=O(),l="hasShadow",u="shadowRGBA",h="patternImage",d="linearGradient",g="radialGradient";let p;function m(){return p||(p=n.Util.createCanvasElement().getContext("2d"),p)}t.shapes={};class y extends i.Node{constructor(e){let r;for(super(e);r=n.Util.getRandomColor(),!r||r in t.shapes;);this.colorKey=r,t.shapes[r]=this}getContext(){return n.Util.warn("shape.getContext() method is deprecated. Please do not use it."),this.getLayer().getContext()}getCanvas(){return n.Util.warn("shape.getCanvas() method is deprecated. Please do not use it."),this.getLayer().getCanvas()}getSceneFunc(){return this.attrs.sceneFunc||this._sceneFunc}getHitFunc(){return this.attrs.hitFunc||this._hitFunc}hasShadow(){return this._getCache(l,this._hasShadow)}_hasShadow(){return this.shadowEnabled()&&0!==this.shadowOpacity()&&!!(this.shadowColor()||this.shadowBlur()||this.shadowOffsetX()||this.shadowOffsetY())}_getFillPattern(){return this._getCache(h,this.__getFillPattern)}__getFillPattern(){if(this.fillPatternImage()){const t=m().createPattern(this.fillPatternImage(),this.fillPatternRepeat()||"repeat");if(t&&t.setTransform){const r=new n.Transform;r.translate(this.fillPatternX(),this.fillPatternY()),r.rotate(e.Konva.getAngle(this.fillPatternRotation())),r.scale(this.fillPatternScaleX(),this.fillPatternScaleY()),r.translate(-1*this.fillPatternOffsetX(),-1*this.fillPatternOffsetY());const i=r.getMatrix(),a="undefined"==typeof DOMMatrix?{a:i[0],b:i[1],c:i[2],d:i[3],e:i[4],f:i[5]}:new DOMMatrix(i);t.setTransform(a)}return t}}_getLinearGradient(){return this._getCache(d,this.__getLinearGradient)}__getLinearGradient(){const t=this.fillLinearGradientColorStops();if(t){const e=m(),n=this.fillLinearGradientStartPoint(),r=this.fillLinearGradientEndPoint(),i=e.createLinearGradient(n.x,n.y,r.x,r.y);for(let a=0;a<t.length;a+=2)i.addColorStop(t[a],t[a+1]);return i}}_getRadialGradient(){return this._getCache(g,this.__getRadialGradient)}__getRadialGradient(){const t=this.fillRadialGradientColorStops();if(t){const e=m(),n=this.fillRadialGradientStartPoint(),r=this.fillRadialGradientEndPoint(),i=e.createRadialGradient(n.x,n.y,this.fillRadialGradientStartRadius(),r.x,r.y,this.fillRadialGradientEndRadius());for(let a=0;a<t.length;a+=2)i.addColorStop(t[a],t[a+1]);return i}}getShadowRGBA(){return this._getCache(u,this._getShadowRGBA)}_getShadowRGBA(){if(!this.hasShadow())return;const t=n.Util.colorToRGBA(this.shadowColor());return t?"rgba("+t.r+","+t.g+","+t.b+","+t.a*(this.shadowOpacity()||1)+")":void 0}hasFill(){return this._calculate("hasFill",["fillEnabled","fill","fillPatternImage","fillLinearGradientColorStops","fillRadialGradientColorStops"],(()=>this.fillEnabled()&&!!(this.fill()||this.fillPatternImage()||this.fillLinearGradientColorStops()||this.fillRadialGradientColorStops())))}hasStroke(){return this._calculate("hasStroke",["strokeEnabled","strokeWidth","stroke","strokeLinearGradientColorStops"],(()=>this.strokeEnabled()&&this.strokeWidth()&&!(!this.stroke()&&!this.strokeLinearGradientColorStops())))}hasHitStroke(){const t=this.hitStrokeWidth();return"auto"===t?this.hasStroke():this.strokeEnabled()&&!!t}intersects(t){const e=this.getStage();if(!e)return!1;const n=e.bufferHitCanvas;n.getContext().clear(),this.drawHit(n,void 0,!0);return n.context.getImageData(Math.round(t.x),Math.round(t.y),1,1).data[3]>0}destroy(){return i.Node.prototype.destroy.call(this),delete t.shapes[this.colorKey],delete this.colorKey,this}_useBufferCanvas(t){var e;if(!(null===(e=this.attrs.perfectDrawEnabled)||void 0===e||e))return!1;const n=t||this.hasFill(),r=this.hasStroke(),i=1!==this.getAbsoluteOpacity();if(n&&r&&i)return!0;const a=this.hasShadow(),o=this.shadowForStrokeEnabled();return!!(n&&r&&a&&o)}setStrokeHitEnabled(t){n.Util.warn("strokeHitEnabled property is deprecated. Please use hitStrokeWidth instead."),t?this.hitStrokeWidth("auto"):this.hitStrokeWidth(0)}getStrokeHitEnabled(){return 0!==this.hitStrokeWidth()}getSelfRect(){const t=this.size();return{x:this._centroid?-t.width/2:0,y:this._centroid?-t.height/2:0,width:t.width,height:t.height}}getClientRect(t={}){let e=!1,n=this.getParent();for(;n;){if(n.isCached()){e=!0;break}n=n.getParent()}const r=t.skipTransform,i=t.relativeTo||e&&this.getStage()||void 0,a=this.getSelfRect(),o=!t.skipStroke&&this.hasStroke()&&this.strokeWidth()||0,s=a.width+o,l=a.height+o,u=!t.skipShadow&&this.hasShadow(),c=u?this.shadowOffsetX():0,h=u?this.shadowOffsetY():0,d=s+Math.abs(c),f=l+Math.abs(h),g=u&&this.shadowBlur()||0,p={width:d+2*g,height:f+2*g,x:-(o/2+g)+Math.min(c,0)+a.x,y:-(o/2+g)+Math.min(h,0)+a.y};return r?p:this._transformedRect(p,i)}drawScene(t,e,n){const r=this.getLayer();let i,a,o=t||r.getCanvas(),s=o.getContext(),l=this._getCanvasCache(),u=this.getSceneFunc(),c=this.hasShadow();const h=o.isCache,d=e===this;if(!this.isVisible()&&!d)return this;if(l){s.save();const t=this.getAbsoluteTransform(e).getMatrix();return s.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedSceneCanvas(s),s.restore(),this}if(!u)return this;if(s.save(),this._useBufferCanvas()&&!h){i=this.getStage();const t=n||i.bufferCanvas;a=t.getContext(),a.clear(),a.save(),a._applyLineJoin(this);var f=this.getAbsoluteTransform(e).getMatrix();a.transform(f[0],f[1],f[2],f[3],f[4],f[5]),u.call(this,a,this),a.restore();const r=t.pixelRatio;c&&s._applyShadow(this),s._applyOpacity(this),s._applyGlobalCompositeOperation(this),s.drawImage(t._canvas,0,0,t.width/r,t.height/r)}else{if(s._applyLineJoin(this),!d){f=this.getAbsoluteTransform(e).getMatrix();s.transform(f[0],f[1],f[2],f[3],f[4],f[5]),s._applyOpacity(this),s._applyGlobalCompositeOperation(this)}c&&s._applyShadow(this),u.call(this,s,this)}return s.restore(),this}drawHit(t,e,r=!1){if(!this.shouldDrawHit(e,r))return this;const i=this.getLayer(),a=t||i.hitCanvas,o=a&&a.getContext(),s=this.hitFunc()||this.sceneFunc(),l=this._getCanvasCache(),u=l&&l.hit;if(this.colorKey||n.Util.warn("Looks like your canvas has a destroyed shape in it. Do not reuse shape after you destroyed it. If you want to reuse shape you should call remove() instead of destroy()"),u){o.save();const t=this.getAbsoluteTransform(e).getMatrix();return o.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedHitCanvas(o),o.restore(),this}if(!s)return this;o.save(),o._applyLineJoin(this);if(!(this===e)){const t=this.getAbsoluteTransform(e).getMatrix();o.transform(t[0],t[1],t[2],t[3],t[4],t[5])}return s.call(this,o,this),o.restore(),this}drawHitFromCache(t=0){const e=this._getCanvasCache(),r=this._getCachedSceneCanvas(),i=e.hit,a=i.getContext(),o=i.getWidth(),s=i.getHeight();a.clear(),a.drawImage(r._canvas,0,0,o,s);try{const e=a.getImageData(0,0,o,s),r=e.data,i=r.length,l=n.Util._hexToRgb(this.colorKey);for(let n=0;n<i;n+=4){r[n+3]>t?(r[n]=l.r,r[n+1]=l.g,r[n+2]=l.b,r[n+3]=255):r[n+3]=0}a.putImageData(e,0,0)}catch(l){n.Util.error("Unable to draw hit graph from cached scene canvas. "+l.message)}return this}hasPointerCapture(t){return s.hasPointerCapture(t,this)}setPointerCapture(t){s.setPointerCapture(t,this)}releaseCapture(t){s.releaseCapture(t,this)}}t.Shape=y,y.prototype._fillFunc=function(t){const e=this.attrs.fillRule;e?t.fill(e):t.fill()},y.prototype._strokeFunc=function(t){t.stroke()},y.prototype._fillFuncHit=function(t){const e=this.attrs.fillRule;e?t.fill(e):t.fill()},y.prototype._strokeFuncHit=function(t){t.stroke()},y.prototype._centroid=!1,y.prototype.nodeType="Shape",(0,o._registerNode)(y),y.prototype.eventListeners={},y.prototype.on.call(y.prototype,"shadowColorChange.konva shadowBlurChange.konva shadowOffsetChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(l)})),y.prototype.on.call(y.prototype,"shadowColorChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(u)})),y.prototype.on.call(y.prototype,"fillPriorityChange.konva fillPatternImageChange.konva fillPatternRepeatChange.konva fillPatternScaleXChange.konva fillPatternScaleYChange.konva fillPatternOffsetXChange.konva fillPatternOffsetYChange.konva fillPatternXChange.konva fillPatternYChange.konva fillPatternRotationChange.konva",(function(){this._clearCache(h)})),y.prototype.on.call(y.prototype,"fillPriorityChange.konva fillLinearGradientColorStopsChange.konva fillLinearGradientStartPointXChange.konva fillLinearGradientStartPointYChange.konva fillLinearGradientEndPointXChange.konva fillLinearGradientEndPointYChange.konva",(function(){this._clearCache(d)})),y.prototype.on.call(y.prototype,"fillPriorityChange.konva fillRadialGradientColorStopsChange.konva fillRadialGradientStartPointXChange.konva fillRadialGradientStartPointYChange.konva fillRadialGradientEndPointXChange.konva fillRadialGradientEndPointYChange.konva fillRadialGradientStartRadiusChange.konva fillRadialGradientEndRadiusChange.konva",(function(){this._clearCache(g)})),r.Factory.addGetterSetter(y,"stroke",void 0,(0,a.getStringOrGradientValidator)()),r.Factory.addGetterSetter(y,"strokeWidth",2,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillAfterStrokeEnabled",!1),r.Factory.addGetterSetter(y,"hitStrokeWidth","auto",(0,a.getNumberOrAutoValidator)()),r.Factory.addGetterSetter(y,"strokeHitEnabled",!0,(0,a.getBooleanValidator)()),r.Factory.addGetterSetter(y,"perfectDrawEnabled",!0,(0,a.getBooleanValidator)()),r.Factory.addGetterSetter(y,"shadowForStrokeEnabled",!0,(0,a.getBooleanValidator)()),r.Factory.addGetterSetter(y,"lineJoin"),r.Factory.addGetterSetter(y,"lineCap"),r.Factory.addGetterSetter(y,"sceneFunc"),r.Factory.addGetterSetter(y,"hitFunc"),r.Factory.addGetterSetter(y,"dash"),r.Factory.addGetterSetter(y,"dashOffset",0,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"shadowColor",void 0,(0,a.getStringValidator)()),r.Factory.addGetterSetter(y,"shadowBlur",0,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"shadowOpacity",1,(0,a.getNumberValidator)()),r.Factory.addComponentsGetterSetter(y,"shadowOffset",["x","y"]),r.Factory.addGetterSetter(y,"shadowOffsetX",0,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"shadowOffsetY",0,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternImage"),r.Factory.addGetterSetter(y,"fill",void 0,(0,a.getStringOrGradientValidator)()),r.Factory.addGetterSetter(y,"fillPatternX",0,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternY",0,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillLinearGradientColorStops"),r.Factory.addGetterSetter(y,"strokeLinearGradientColorStops"),r.Factory.addGetterSetter(y,"fillRadialGradientStartRadius",0),r.Factory.addGetterSetter(y,"fillRadialGradientEndRadius",0),r.Factory.addGetterSetter(y,"fillRadialGradientColorStops"),r.Factory.addGetterSetter(y,"fillPatternRepeat","repeat"),r.Factory.addGetterSetter(y,"fillEnabled",!0),r.Factory.addGetterSetter(y,"strokeEnabled",!0),r.Factory.addGetterSetter(y,"shadowEnabled",!0),r.Factory.addGetterSetter(y,"dashEnabled",!0),r.Factory.addGetterSetter(y,"strokeScaleEnabled",!0),r.Factory.addGetterSetter(y,"fillPriority","color"),r.Factory.addComponentsGetterSetter(y,"fillPatternOffset",["x","y"]),r.Factory.addGetterSetter(y,"fillPatternOffsetX",0,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternOffsetY",0,(0,a.getNumberValidator)()),r.Factory.addComponentsGetterSetter(y,"fillPatternScale",["x","y"]),r.Factory.addGetterSetter(y,"fillPatternScaleX",1,(0,a.getNumberValidator)()),r.Factory.addGetterSetter(y,"fillPatternScaleY",1,(0,a.getNumberValidator)()),r.Factory.addComponentsGetterSetter(y,"fillLinearGradientStartPoint",["x","y"]),r.Factory.addComponentsGetterSetter(y,"strokeLinearGradientStartPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillLinearGradientStartPointX",0),r.Factory.addGetterSetter(y,"strokeLinearGradientStartPointX",0),r.Factory.addGetterSetter(y,"fillLinearGradientStartPointY",0),r.Factory.addGetterSetter(y,"strokeLinearGradientStartPointY",0),r.Factory.addComponentsGetterSetter(y,"fillLinearGradientEndPoint",["x","y"]),r.Factory.addComponentsGetterSetter(y,"strokeLinearGradientEndPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillLinearGradientEndPointX",0),r.Factory.addGetterSetter(y,"strokeLinearGradientEndPointX",0),r.Factory.addGetterSetter(y,"fillLinearGradientEndPointY",0),r.Factory.addGetterSetter(y,"strokeLinearGradientEndPointY",0),r.Factory.addComponentsGetterSetter(y,"fillRadialGradientStartPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillRadialGradientStartPointX",0),r.Factory.addGetterSetter(y,"fillRadialGradientStartPointY",0),r.Factory.addComponentsGetterSetter(y,"fillRadialGradientEndPoint",["x","y"]),r.Factory.addGetterSetter(y,"fillRadialGradientEndPointX",0),r.Factory.addGetterSetter(y,"fillRadialGradientEndPointY",0),r.Factory.addGetterSetter(y,"fillPatternRotation",0),r.Factory.addGetterSetter(y,"fillRule",void 0,(0,a.getStringValidator)()),r.Factory.backCompat(y,{dashArray:"dash",getDashArray:"getDash",setDashArray:"getDash",drawFunc:"sceneFunc",getDrawFunc:"getSceneFunc",setDrawFunc:"setSceneFunc",drawHitFunc:"hitFunc",getDrawHitFunc:"getHitFunc",setDrawHitFunc:"setHitFunc"})}(V)),V}function W(){if(B)return H;B=1,Object.defineProperty(H,"__esModule",{value:!0}),H.Layer=void 0;const t=f(),e=L(),n=M(),r=_(),i=P(),a=b(),o=j(),s=c(),l=[{x:0,y:0},{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],u=l.length;class h extends e.Container{constructor(t){super(t),this.canvas=new i.SceneCanvas,this.hitCanvas=new i.HitCanvas({pixelRatio:1}),this._waitingForDraw=!1,this.on("visibleChange.konva",this._checkVisibility),this._checkVisibility(),this.on("imageSmoothingEnabledChange.konva",this._setSmoothEnabled),this._setSmoothEnabled()}createPNGStream(){return this.canvas._canvas.createPNGStream()}getCanvas(){return this.canvas}getNativeCanvasElement(){return this.canvas._canvas}getHitCanvas(){return this.hitCanvas}getContext(){return this.getCanvas().getContext()}clear(t){return this.getContext().clear(t),this.getHitCanvas().getContext().clear(t),this}setZIndex(t){super.setZIndex(t);const e=this.getStage();return e&&e.content&&(e.content.removeChild(this.getNativeCanvasElement()),t<e.children.length-1?e.content.insertBefore(this.getNativeCanvasElement(),e.children[t+1].getCanvas()._canvas):e.content.appendChild(this.getNativeCanvasElement())),this}moveToTop(){n.Node.prototype.moveToTop.call(this);const t=this.getStage();return t&&t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.appendChild(this.getNativeCanvasElement())),!0}moveUp(){if(!n.Node.prototype.moveUp.call(this))return!1;const t=this.getStage();return!(!t||!t.content)&&(t.content.removeChild(this.getNativeCanvasElement()),this.index<t.children.length-1?t.content.insertBefore(this.getNativeCanvasElement(),t.children[this.index+1].getCanvas()._canvas):t.content.appendChild(this.getNativeCanvasElement()),!0)}moveDown(){if(n.Node.prototype.moveDown.call(this)){const t=this.getStage();if(t){const e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[this.index+1].getCanvas()._canvas))}return!0}return!1}moveToBottom(){if(n.Node.prototype.moveToBottom.call(this)){const t=this.getStage();if(t){const e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[1].getCanvas()._canvas))}return!0}return!1}getLayer(){return this}remove(){const e=this.getNativeCanvasElement();return n.Node.prototype.remove.call(this),e&&e.parentNode&&t.Util._isInDocument(e)&&e.parentNode.removeChild(e),this}getStage(){return this.parent}setSize({width:t,height:e}){return this.canvas.setSize(t,e),this.hitCanvas.setSize(t,e),this._setSmoothEnabled(),this}_validateAdd(e){const n=e.getType();"Group"!==n&&"Shape"!==n&&t.Util.throw("You may only add groups and shapes to a layer.")}_toKonvaCanvas(t){return(t=t||{}).width=t.width||this.getWidth(),t.height=t.height||this.getHeight(),t.x=void 0!==t.x?t.x:this.x(),t.y=void 0!==t.y?t.y:this.y(),n.Node.prototype._toKonvaCanvas.call(this,t)}_checkVisibility(){const t=this.visible();this.canvas._canvas.style.display=t?"block":"none"}_setSmoothEnabled(){this.getContext()._context.imageSmoothingEnabled=this.imageSmoothingEnabled()}getWidth(){if(this.parent)return this.parent.width()}setWidth(){t.Util.warn('Can not change width of layer. Use "stage.width(value)" function instead.')}getHeight(){if(this.parent)return this.parent.height()}setHeight(){t.Util.warn('Can not change height of layer. Use "stage.height(value)" function instead.')}batchDraw(){return this._waitingForDraw||(this._waitingForDraw=!0,t.Util.requestAnimFrame((()=>{this.draw(),this._waitingForDraw=!1}))),this}getIntersection(t){if(!this.isListening()||!this.isVisible())return null;let e=1,n=!1;for(;;){for(let r=0;r<u;r++){const i=l[r],a=this._getIntersection({x:t.x+i.x*e,y:t.y+i.y*e}),o=a.shape;if(o)return o;if(n=!!a.antialiased,!a.antialiased)break}if(!n)return null;e+=1}}_getIntersection(e){const n=this.hitCanvas.pixelRatio,r=this.hitCanvas.context.getImageData(Math.round(e.x*n),Math.round(e.y*n),1,1).data,i=r[3];if(255===i){const e=t.Util._rgbToHex(r[0],r[1],r[2]),n=o.shapes["#"+e];return n?{shape:n}:{antialiased:!0}}return i>0?{antialiased:!0}:{}}drawScene(t,n){const r=this.getLayer(),i=t||r&&r.getCanvas();return this._fire("beforeDraw",{node:this}),this.clearBeforeDraw()&&i.getContext().clear(),e.Container.prototype.drawScene.call(this,i,n),this._fire("draw",{node:this}),this}drawHit(t,n){const r=this.getLayer(),i=t||r&&r.hitCanvas;return r&&r.clearBeforeDraw()&&r.getHitCanvas().getContext().clear(),e.Container.prototype.drawHit.call(this,i,n),this}enableHitGraph(){return this.hitGraphEnabled(!0),this}disableHitGraph(){return this.hitGraphEnabled(!1),this}setHitGraphEnabled(e){t.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening(e)}getHitGraphEnabled(e){return t.Util.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening()}toggleHitCanvas(){if(!this.parent||!this.parent.content)return;const t=this.parent;!!this.hitCanvas._canvas.parentNode?t.content.removeChild(this.hitCanvas._canvas):t.content.appendChild(this.hitCanvas._canvas)}destroy(){return t.Util.releaseCanvas(this.getNativeCanvasElement(),this.getHitCanvas()._canvas),super.destroy()}}return H.Layer=h,h.prototype.nodeType="Layer",(0,s._registerNode)(h),r.Factory.addGetterSetter(h,"imageSmoothingEnabled",!0),r.Factory.addGetterSetter(h,"clearBeforeDraw",!0),r.Factory.addGetterSetter(h,"hitGraphEnabled",!0,(0,a.getBooleanValidator)()),H}var K,q={};var Y,X={};function Q(){if(Y)return X;Y=1,Object.defineProperty(X,"__esModule",{value:!0}),X.Group=void 0;const t=f(),e=L(),n=c();let r=class extends e.Container{_validateAdd(e){const n=e.getType();"Group"!==n&&"Shape"!==n&&t.Util.throw("You may only add groups and shapes to groups.")}};return X.Group=r,r.prototype.nodeType="Group",(0,n._registerNode)(r),X}var $,J={};function Z(){if($)return J;$=1,Object.defineProperty(J,"__esModule",{value:!0}),J.Animation=void 0;const t=c(),e=f(),n=t.glob.performance&&t.glob.performance.now?function(){return t.glob.performance.now()}:function(){return(new Date).getTime()};let r=class t{constructor(e,r){this.id=t.animIdCounter++,this.frame={time:0,timeDiff:0,lastTime:n(),frameRate:0},this.func=e,this.setLayers(r)}setLayers(t){let e=[];return t&&(e=Array.isArray(t)?t:[t]),this.layers=e,this}getLayers(){return this.layers}addLayer(t){const e=this.layers,n=e.length;for(let r=0;r<n;r++)if(e[r]._id===t._id)return!1;return this.layers.push(t),!0}isRunning(){const e=t.animations,n=e.length;for(let t=0;t<n;t++)if(e[t].id===this.id)return!0;return!1}start(){return this.stop(),this.frame.timeDiff=0,this.frame.lastTime=n(),t._addAnimation(this),this}stop(){return t._removeAnimation(this),this}_updateFrameObject(t){this.frame.timeDiff=t-this.frame.lastTime,this.frame.lastTime=t,this.frame.time+=this.frame.timeDiff,this.frame.frameRate=1e3/this.frame.timeDiff}static _addAnimation(t){this.animations.push(t),this._handleAnimation()}static _removeAnimation(t){const e=t.id,n=this.animations,r=n.length;for(let i=0;i<r;i++)if(n[i].id===e){this.animations.splice(i,1);break}}static _runFrames(){const t={},e=this.animations;for(let r=0;r<e.length;r++){const i=e[r],a=i.layers,o=i.func;i._updateFrameObject(n());const s=a.length;let l;if(l=!o||!1!==o.call(i,i.frame),l)for(let e=0;e<s;e++){const n=a[e];void 0!==n._id&&(t[n._id]=n)}}for(const n in t)t.hasOwnProperty(n)&&t[n].batchDraw()}static _animationLoop(){const n=t;n.animations.length?(n._runFrames(),e.Util.requestAnimFrame(n._animationLoop)):n.animRunning=!1}static _handleAnimation(){this.animRunning||(this.animRunning=!0,e.Util.requestAnimFrame(this._animationLoop))}};return J.Animation=r,r.animations=[],r.animIdCounter=0,r.animRunning=!1,J}var tt,et,nt={};function rt(){return et||(et=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Konva=void 0;const e=c(),n=f(),r=M(),i=L(),a=(z||(z=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Stage=t.stages=void 0;const e=f(),n=_(),r=L(),i=c(),a=P(),o=N(),s=c(),l=O(),u="mouseleave",h="mouseover",d="mouseenter",g="mousemove",p="mousedown",m="mouseup",y="pointermove",v="pointerdown",b="pointerup",S="pointercancel",x="pointerout",w="pointerleave",C="pointerover",k="pointerenter",T="contextmenu",E="touchstart",F="touchend",M="touchmove",A="touchcancel",R="wheel",D=[[d,"_pointerenter"],[p,"_pointerdown"],[g,"_pointermove"],[m,"_pointerup"],[u,"_pointerleave"],[E,"_pointerdown"],[M,"_pointermove"],[F,"_pointerup"],[A,"_pointercancel"],[h,"_pointerover"],[R,"_wheel"],[T,"_contextmenu"],[v,"_pointerdown"],[y,"_pointermove"],[b,"_pointerup"],[S,"_pointercancel"],["lostpointercapture","_lostpointercapture"]],z={mouse:{[x]:"mouseout",[w]:u,[C]:h,[k]:d,[y]:g,[v]:p,[b]:m,[S]:"mousecancel",pointerclick:"click",pointerdblclick:"dblclick"},touch:{[x]:"touchout",[w]:"touchleave",[C]:"touchover",[k]:"touchenter",[y]:M,[v]:E,[b]:F,[S]:A,pointerclick:"tap",pointerdblclick:"dbltap"},pointer:{[x]:x,[w]:w,[C]:C,[k]:k,[y]:y,[v]:v,[b]:b,[S]:S,pointerclick:"pointerclick",pointerdblclick:"pointerdblclick"}},I=t=>t.indexOf("pointer")>=0?"pointer":t.indexOf("touch")>=0?"touch":"mouse",G=t=>{const e=I(t);return"pointer"===e?i.Konva.pointerEventsEnabled&&z.pointer:"touch"===e?z.touch:"mouse"===e?z.mouse:void 0};function U(t={}){return(t.clipFunc||t.clipWidth||t.clipHeight)&&e.Util.warn("Stage does not support clipping. Please use clip for Layers or Groups."),t}t.stages=[];class B extends r.Container{constructor(e){super(U(e)),this._pointerPositions=[],this._changedPointerPositions=[],this._buildDOM(),this._bindContentEvents(),t.stages.push(this),this.on("widthChange.konva heightChange.konva",this._resizeDOM),this.on("visibleChange.konva",this._checkVisibility),this.on("clipWidthChange.konva clipHeightChange.konva clipFuncChange.konva",(()=>{U(this.attrs)})),this._checkVisibility()}_validateAdd(t){const n="Layer"===t.getType(),r="FastLayer"===t.getType();n||r||e.Util.throw("You may only add layers to the stage.")}_checkVisibility(){if(!this.content)return;const t=this.visible()?"":"none";this.content.style.display=t}setContainer(t){if("string"==typeof t){if("."===t.charAt(0)){const e=t.slice(1);t=document.getElementsByClassName(e)[0]}else{var e;e="#"!==t.charAt(0)?t:t.slice(1),t=document.getElementById(e)}if(!t)throw"Can not find container in document with id "+e}return this._setAttr("container",t),this.content&&(this.content.parentElement&&this.content.parentElement.removeChild(this.content),t.appendChild(this.content)),this}shouldDrawHit(){return!0}clear(){const t=this.children,e=t.length;for(let n=0;n<e;n++)t[n].clear();return this}clone(t){return t||(t={}),t.container="undefined"!=typeof document&&document.createElement("div"),r.Container.prototype.clone.call(this,t)}destroy(){super.destroy();const n=this.content;n&&e.Util._isInDocument(n)&&this.container().removeChild(n);const r=t.stages.indexOf(this);return r>-1&&t.stages.splice(r,1),e.Util.releaseCanvas(this.bufferCanvas._canvas,this.bufferHitCanvas._canvas),this}getPointerPosition(){const t=this._pointerPositions[0]||this._changedPointerPositions[0];return t?{x:t.x,y:t.y}:(e.Util.warn("Pointer position is missing and not registered by the stage. Looks like it is outside of the stage container. You can set it manually from event: stage.setPointersPositions(event);"),null)}_getPointerById(t){return this._pointerPositions.find((e=>e.id===t))}getPointersPositions(){return this._pointerPositions}getStage(){return this}getContent(){return this.content}_toKonvaCanvas(t){(t=t||{}).x=t.x||0,t.y=t.y||0,t.width=t.width||this.width(),t.height=t.height||this.height();const e=new a.SceneCanvas({width:t.width,height:t.height,pixelRatio:t.pixelRatio||1}),n=e.getContext()._context,r=this.children;return(t.x||t.y)&&n.translate(-1*t.x,-1*t.y),r.forEach((function(e){if(!e.isVisible())return;const r=e._toKonvaCanvas(t);n.drawImage(r._canvas,t.x,t.y,r.getWidth()/r.getPixelRatio(),r.getHeight()/r.getPixelRatio())})),e}getIntersection(t){if(!t)return null;const e=this.children;for(let n=e.length-1;n>=0;n--){const r=e[n].getIntersection(t);if(r)return r}return null}_resizeDOM(){const t=this.width(),e=this.height();this.content&&(this.content.style.width=t+"px",this.content.style.height=e+"px"),this.bufferCanvas.setSize(t,e),this.bufferHitCanvas.setSize(t,e),this.children.forEach((n=>{n.setSize({width:t,height:e}),n.draw()}))}add(t,...n){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.add(arguments[t]);return this}super.add(t);const r=this.children.length;return r>5&&e.Util.warn("The stage has "+r+" layers. Recommended maximum number of layers is 3-5. Adding more layers into the stage may drop the performance. Rethink your tree structure, you can use Konva.Group."),t.setSize({width:this.width(),height:this.height()}),t.draw(),i.Konva.isBrowser&&this.content.appendChild(t.canvas._canvas),this}getParent(){return null}getLayer(){return null}hasPointerCapture(t){return l.hasPointerCapture(t,this)}setPointerCapture(t){l.setPointerCapture(t,this)}releaseCapture(t){l.releaseCapture(t,this)}getLayers(){return this.children}_bindContentEvents(){i.Konva.isBrowser&&D.forEach((([t,e])=>{this.content.addEventListener(t,(t=>{this[e](t)}),{passive:!1})}))}_pointerenter(t){this.setPointersPositions(t);const e=G(t.type);e&&this._fire(e.pointerenter,{evt:t,target:this,currentTarget:this})}_pointerover(t){this.setPointersPositions(t);const e=G(t.type);e&&this._fire(e.pointerover,{evt:t,target:this,currentTarget:this})}_getTargetShape(t){let e=this[t+"targetShape"];return e&&!e.getStage()&&(e=null),e}_pointerleave(t){const e=G(t.type),n=I(t.type);if(!e)return;this.setPointersPositions(t);const r=this._getTargetShape(n),a=!(i.Konva.isDragging()||i.Konva.isTransforming())||i.Konva.hitOnDragEnabled;r&&a?(r._fireAndBubble(e.pointerout,{evt:t}),r._fireAndBubble(e.pointerleave,{evt:t}),this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this[n+"targetShape"]=null):a&&(this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this._fire(e.pointerout,{evt:t,target:this,currentTarget:this})),this.pointerPos=null,this._pointerPositions=[]}_pointerdown(t){const e=G(t.type),n=I(t.type);if(!e)return;this.setPointersPositions(t);let r=!1;this._changedPointerPositions.forEach((a=>{const s=this.getIntersection(a);if(o.DD.justDragged=!1,i.Konva["_"+n+"ListenClick"]=!0,!s||!s.isListening())return void(this[n+"ClickStartShape"]=void 0);i.Konva.capturePointerEventsEnabled&&s.setPointerCapture(a.id),this[n+"ClickStartShape"]=s,s._fireAndBubble(e.pointerdown,{evt:t,pointerId:a.id}),r=!0;const l=t.type.indexOf("touch")>=0;s.preventDefault()&&t.cancelable&&l&&t.preventDefault()})),r||this._fire(e.pointerdown,{evt:t,target:this,currentTarget:this,pointerId:this._pointerPositions[0].id})}_pointermove(t){const e=G(t.type),n=I(t.type);if(!e)return;if(i.Konva.isDragging()&&o.DD.node.preventDefault()&&t.cancelable&&t.preventDefault(),this.setPointersPositions(t),(i.Konva.isDragging()||i.Konva.isTransforming())&&!i.Konva.hitOnDragEnabled)return;const r={};let a=!1;const s=this._getTargetShape(n);this._changedPointerPositions.forEach((i=>{const o=l.getCapturedShape(i.id)||this.getIntersection(i),u=i.id,c={evt:t,pointerId:u},h=s!==o;if(h&&s&&(s._fireAndBubble(e.pointerout,{...c},o),s._fireAndBubble(e.pointerleave,{...c},o)),o){if(r[o._id])return;r[o._id]=!0}o&&o.isListening()?(a=!0,h&&(o._fireAndBubble(e.pointerover,{...c},s),o._fireAndBubble(e.pointerenter,{...c},s),this[n+"targetShape"]=o),o._fireAndBubble(e.pointermove,{...c})):s&&(this._fire(e.pointerover,{evt:t,target:this,currentTarget:this,pointerId:u}),this[n+"targetShape"]=null)})),a||this._fire(e.pointermove,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id})}_pointerup(t){const e=G(t.type),n=I(t.type);if(!e)return;this.setPointersPositions(t);const r=this[n+"ClickStartShape"],a=this[n+"ClickEndShape"],s={};let u=!1;this._changedPointerPositions.forEach((c=>{const h=l.getCapturedShape(c.id)||this.getIntersection(c);if(h){if(h.releaseCapture(c.id),s[h._id])return;s[h._id]=!0}const d=c.id,f={evt:t,pointerId:d};let g=!1;i.Konva["_"+n+"InDblClickWindow"]?(g=!0,clearTimeout(this[n+"DblTimeout"])):o.DD.justDragged||(i.Konva["_"+n+"InDblClickWindow"]=!0,clearTimeout(this[n+"DblTimeout"])),this[n+"DblTimeout"]=setTimeout((function(){i.Konva["_"+n+"InDblClickWindow"]=!1}),i.Konva.dblClickWindow),h&&h.isListening()?(u=!0,this[n+"ClickEndShape"]=h,h._fireAndBubble(e.pointerup,{...f}),i.Konva["_"+n+"ListenClick"]&&r&&r===h&&(h._fireAndBubble(e.pointerclick,{...f}),g&&a&&a===h&&h._fireAndBubble(e.pointerdblclick,{...f}))):(this[n+"ClickEndShape"]=null,i.Konva["_"+n+"ListenClick"]&&this._fire(e.pointerclick,{evt:t,target:this,currentTarget:this,pointerId:d}),g&&this._fire(e.pointerdblclick,{evt:t,target:this,currentTarget:this,pointerId:d}))})),u||this._fire(e.pointerup,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),i.Konva["_"+n+"ListenClick"]=!1,t.cancelable&&"touch"!==n&&"pointer"!==n&&t.preventDefault()}_contextmenu(t){this.setPointersPositions(t);const e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(T,{evt:t}):this._fire(T,{evt:t,target:this,currentTarget:this})}_wheel(t){this.setPointersPositions(t);const e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(R,{evt:t}):this._fire(R,{evt:t,target:this,currentTarget:this})}_pointercancel(t){this.setPointersPositions(t);const e=l.getCapturedShape(t.pointerId)||this.getIntersection(this.getPointerPosition());e&&e._fireAndBubble(b,l.createEvent(t)),l.releaseCapture(t.pointerId)}_lostpointercapture(t){l.releaseCapture(t.pointerId)}setPointersPositions(t){const n=this._getContentPosition();let r=null,i=null;void 0!==(t=t||window.event).touches?(this._pointerPositions=[],this._changedPointerPositions=[],Array.prototype.forEach.call(t.touches,(t=>{this._pointerPositions.push({id:t.identifier,x:(t.clientX-n.left)/n.scaleX,y:(t.clientY-n.top)/n.scaleY})})),Array.prototype.forEach.call(t.changedTouches||t.touches,(t=>{this._changedPointerPositions.push({id:t.identifier,x:(t.clientX-n.left)/n.scaleX,y:(t.clientY-n.top)/n.scaleY})}))):(r=(t.clientX-n.left)/n.scaleX,i=(t.clientY-n.top)/n.scaleY,this.pointerPos={x:r,y:i},this._pointerPositions=[{x:r,y:i,id:e.Util._getFirstPointerId(t)}],this._changedPointerPositions=[{x:r,y:i,id:e.Util._getFirstPointerId(t)}])}_setPointerPosition(t){e.Util.warn('Method _setPointerPosition is deprecated. Use "stage.setPointersPositions(event)" instead.'),this.setPointersPositions(t)}_getContentPosition(){if(!this.content||!this.content.getBoundingClientRect)return{top:0,left:0,scaleX:1,scaleY:1};const t=this.content.getBoundingClientRect();return{top:t.top,left:t.left,scaleX:t.width/this.content.clientWidth||1,scaleY:t.height/this.content.clientHeight||1}}_buildDOM(){if(this.bufferCanvas=new a.SceneCanvas({width:this.width(),height:this.height()}),this.bufferHitCanvas=new a.HitCanvas({pixelRatio:1,width:this.width(),height:this.height()}),!i.Konva.isBrowser)return;const t=this.container();if(!t)throw"Stage has no container. A container is required.";t.innerHTML="",this.content=document.createElement("div"),this.content.style.position="relative",this.content.style.userSelect="none",this.content.className="konvajs-content",this.content.setAttribute("role","presentation"),t.appendChild(this.content),this._resizeDOM()}cache(){return e.Util.warn("Cache function is not allowed for stage. You may use cache only for layers, groups and shapes."),this}clearCache(){return this}batchDraw(){return this.getChildren().forEach((function(t){t.batchDraw()})),this}}t.Stage=B,B.prototype.nodeType="Stage",(0,s._registerNode)(B),n.Factory.addGetterSetter(B,"container"),i.Konva.isBrowser&&document.addEventListener("visibilitychange",(()=>{t.stages.forEach((t=>{t.batchDraw()}))}))}(I)),I),o=W(),s=function(){if(K)return q;K=1,Object.defineProperty(q,"__esModule",{value:!0}),q.FastLayer=void 0;const t=f(),e=W(),n=c();let r=class extends e.Layer{constructor(e){super(e),this.listening(!1),t.Util.warn('Konva.Fast layer is deprecated. Please use "new Konva.Layer({ listening: false })" instead.')}};return q.FastLayer=r,r.prototype.nodeType="FastLayer",(0,n._registerNode)(r),q}(),l=Q(),u=N(),h=j(),d=Z(),g=(tt||(tt=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Easings=t.Tween=void 0;const e=f(),n=Z(),r=M(),i=c(),a={node:1,duration:1,easing:1,onFinish:1,yoyo:1},o=["fill","stroke","shadowColor"];let s=0;class l{constructor(t,e,n,r,i,a,o){this.prop=t,this.propFunc=e,this.begin=r,this._pos=r,this.duration=a,this._change=0,this.prevPos=0,this.yoyo=o,this._time=0,this._position=0,this._startTime=0,this._finish=0,this.func=n,this._change=i-this.begin,this.pause()}fire(t){const e=this[t];e&&e()}setTime(t){t>this.duration?this.yoyo?(this._time=this.duration,this.reverse()):this.finish():t<0?this.yoyo?(this._time=0,this.play()):this.reset():(this._time=t,this.update())}getTime(){return this._time}setPosition(t){this.prevPos=this._pos,this.propFunc(t),this._pos=t}getPosition(t){return void 0===t&&(t=this._time),this.func(t,this.begin,this._change,this.duration)}play(){this.state=2,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onPlay")}reverse(){this.state=3,this._time=this.duration-this._time,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onReverse")}seek(t){this.pause(),this._time=t,this.update(),this.fire("onSeek")}reset(){this.pause(),this._time=0,this.update(),this.fire("onReset")}finish(){this.pause(),this._time=this.duration,this.update(),this.fire("onFinish")}update(){this.setPosition(this.getPosition(this._time)),this.fire("onUpdate")}onEnterFrame(){const t=this.getTimer()-this._startTime;2===this.state?this.setTime(t):3===this.state&&this.setTime(this.duration-t)}pause(){this.state=1,this.fire("onPause")}getTimer(){return(new Date).getTime()}}class u{constructor(r){const o=this,c=r.node,h=c._id,d=r.easing||t.Easings.Linear,f=!!r.yoyo;let g,p;g=void 0===r.duration?.3:0===r.duration?.001:r.duration,this.node=c,this._id=s++;const m=c.getLayer()||(c instanceof i.Konva.Stage?c.getLayers():null);for(p in m||e.Util.error("Tween constructor have `node` that is not in a layer. Please add node into layer first."),this.anim=new n.Animation((function(){o.tween.onEnterFrame()}),m),this.tween=new l(p,(function(t){o._tweenFunc(t)}),d,0,1,1e3*g,f),this._addListeners(),u.attrs[h]||(u.attrs[h]={}),u.attrs[h][this._id]||(u.attrs[h][this._id]={}),u.tweens[h]||(u.tweens[h]={}),r)void 0===a[p]&&this._addAttr(p,r[p]);this.reset(),this.onFinish=r.onFinish,this.onReset=r.onReset,this.onUpdate=r.onUpdate}_addAttr(t,n){const r=this.node,i=r._id;let a,s,l,c,h;const d=u.tweens[i][t];d&&delete u.attrs[i][d][t];let f=r.getAttr(t);if(e.Util._isArray(n))if(a=[],s=Math.max(n.length,f.length),"points"===t&&n.length!==f.length&&(n.length>f.length?(c=f,f=e.Util._prepareArrayForTween(f,n,r.closed())):(l=n,n=e.Util._prepareArrayForTween(n,f,r.closed()))),0===t.indexOf("fill"))for(let o=0;o<s;o++)if(o%2==0)a.push(n[o]-f[o]);else{const t=e.Util.colorToRGBA(f[o]);h=e.Util.colorToRGBA(n[o]),f[o]=t,a.push({r:h.r-t.r,g:h.g-t.g,b:h.b-t.b,a:h.a-t.a})}else for(let e=0;e<s;e++)a.push(n[e]-f[e]);else-1!==o.indexOf(t)?(f=e.Util.colorToRGBA(f),h=e.Util.colorToRGBA(n),a={r:h.r-f.r,g:h.g-f.g,b:h.b-f.b,a:h.a-f.a}):a=n-f;u.attrs[i][this._id][t]={start:f,diff:a,end:n,trueEnd:l,trueStart:c},u.tweens[i][t]=this._id}_tweenFunc(t){const n=this.node,r=u.attrs[n._id][this._id];let i,a,s,l,c,h,d,f;for(i in r){if(a=r[i],s=a.start,l=a.diff,f=a.end,e.Util._isArray(s))if(c=[],d=Math.max(s.length,f.length),0===i.indexOf("fill"))for(h=0;h<d;h++)h%2==0?c.push((s[h]||0)+l[h]*t):c.push("rgba("+Math.round(s[h].r+l[h].r*t)+","+Math.round(s[h].g+l[h].g*t)+","+Math.round(s[h].b+l[h].b*t)+","+(s[h].a+l[h].a*t)+")");else for(h=0;h<d;h++)c.push((s[h]||0)+l[h]*t);else c=-1!==o.indexOf(i)?"rgba("+Math.round(s.r+l.r*t)+","+Math.round(s.g+l.g*t)+","+Math.round(s.b+l.b*t)+","+(s.a+l.a*t)+")":s+l*t;n.setAttr(i,c)}}_addListeners(){this.tween.onPlay=()=>{this.anim.start()},this.tween.onReverse=()=>{this.anim.start()},this.tween.onPause=()=>{this.anim.stop()},this.tween.onFinish=()=>{const t=this.node,e=u.attrs[t._id][this._id];e.points&&e.points.trueEnd&&t.setAttr("points",e.points.trueEnd),this.onFinish&&this.onFinish.call(this)},this.tween.onReset=()=>{const t=this.node,e=u.attrs[t._id][this._id];e.points&&e.points.trueStart&&t.points(e.points.trueStart),this.onReset&&this.onReset()},this.tween.onUpdate=()=>{this.onUpdate&&this.onUpdate.call(this)}}play(){return this.tween.play(),this}reverse(){return this.tween.reverse(),this}reset(){return this.tween.reset(),this}seek(t){return this.tween.seek(1e3*t),this}pause(){return this.tween.pause(),this}finish(){return this.tween.finish(),this}destroy(){const t=this.node._id,e=this._id,n=u.tweens[t];this.pause();for(const r in n)delete u.tweens[t][r];delete u.attrs[t][e]}}t.Tween=u,u.attrs={},u.tweens={},r.Node.prototype.to=function(t){const e=t.onFinish;t.node=this,t.onFinish=function(){this.destroy(),e&&e()},new u(t).play()},t.Easings={BackEaseIn(t,e,n,r){const i=1.70158;return n*(t/=r)*t*((i+1)*t-i)+e},BackEaseOut(t,e,n,r){const i=1.70158;return n*((t=t/r-1)*t*((i+1)*t+i)+1)+e},BackEaseInOut(t,e,n,r){let i=1.70158;return(t/=r/2)<1?n/2*(t*t*((1+(i*=1.525))*t-i))+e:n/2*((t-=2)*t*((1+(i*=1.525))*t+i)+2)+e},ElasticEaseIn(t,e,n,r,i,a){let o=0;return 0===t?e:1===(t/=r)?e+n:(a||(a=.3*r),!i||i<Math.abs(n)?(i=n,o=a/4):o=a/(2*Math.PI)*Math.asin(n/i),-i*Math.pow(2,10*(t-=1))*Math.sin((t*r-o)*(2*Math.PI)/a)+e)},ElasticEaseOut(t,e,n,r,i,a){let o=0;return 0===t?e:1===(t/=r)?e+n:(a||(a=.3*r),!i||i<Math.abs(n)?(i=n,o=a/4):o=a/(2*Math.PI)*Math.asin(n/i),i*Math.pow(2,-10*t)*Math.sin((t*r-o)*(2*Math.PI)/a)+n+e)},ElasticEaseInOut(t,e,n,r,i,a){let o=0;return 0===t?e:2==(t/=r/2)?e+n:(a||(a=r*(.3*1.5)),!i||i<Math.abs(n)?(i=n,o=a/4):o=a/(2*Math.PI)*Math.asin(n/i),t<1?i*Math.pow(2,10*(t-=1))*Math.sin((t*r-o)*(2*Math.PI)/a)*-.5+e:i*Math.pow(2,-10*(t-=1))*Math.sin((t*r-o)*(2*Math.PI)/a)*.5+n+e)},BounceEaseOut:(t,e,n,r)=>(t/=r)<1/2.75?n*(7.5625*t*t)+e:t<2/2.75?n*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?n*(7.5625*(t-=2.25/2.75)*t+.9375)+e:n*(7.5625*(t-=2.625/2.75)*t+.984375)+e,BounceEaseIn:(e,n,r,i)=>r-t.Easings.BounceEaseOut(i-e,0,r,i)+n,BounceEaseInOut:(e,n,r,i)=>e<i/2?.5*t.Easings.BounceEaseIn(2*e,0,r,i)+n:.5*t.Easings.BounceEaseOut(2*e-i,0,r,i)+.5*r+n,EaseIn:(t,e,n,r)=>n*(t/=r)*t+e,EaseOut:(t,e,n,r)=>-n*(t/=r)*(t-2)+e,EaseInOut:(t,e,n,r)=>(t/=r/2)<1?n/2*t*t+e:-n/2*(--t*(t-2)-1)+e,StrongEaseIn:(t,e,n,r)=>n*(t/=r)*t*t*t*t+e,StrongEaseOut:(t,e,n,r)=>n*((t=t/r-1)*t*t*t*t+1)+e,StrongEaseInOut:(t,e,n,r)=>(t/=r/2)<1?n/2*t*t*t*t*t+e:n/2*((t-=2)*t*t*t*t+2)+e,Linear:(t,e,n,r)=>n*t/r+e}}(nt)),nt),p=k(),m=P();t.Konva=n.Util._assign(e.Konva,{Util:n.Util,Transform:n.Transform,Node:r.Node,Container:i.Container,Stage:a.Stage,stages:a.stages,Layer:o.Layer,FastLayer:s.FastLayer,Group:l.Group,DD:u.DD,Shape:h.Shape,shapes:h.shapes,Animation:d.Animation,Tween:g.Tween,Easings:g.Easings,Context:p.Context,Canvas:m.Canvas}),t.default=t.Konva}(l)),l}var it,at={};var ot,st={},lt={};function ut(){if(ot)return lt;ot=1,Object.defineProperty(lt,"__esModule",{value:!0}),lt.Line=void 0;const t=_(),e=c(),n=j(),r=b();function i(t,e,n,r,i,a,o){const s=Math.sqrt(Math.pow(n-t,2)+Math.pow(r-e,2)),l=Math.sqrt(Math.pow(i-n,2)+Math.pow(a-r,2)),u=o*s/(s+l),c=o*l/(s+l);return[n-u*(i-t),r-u*(a-e),n+c*(i-t),r+c*(a-e)]}function a(t,e){const n=t.length,r=[];for(let a=2;a<n-2;a+=2){const n=i(t[a-2],t[a-1],t[a],t[a+1],t[a+2],t[a+3],e);isNaN(n[0])||(r.push(n[0]),r.push(n[1]),r.push(t[a]),r.push(t[a+1]),r.push(n[2]),r.push(n[3]))}return r}class o extends n.Shape{constructor(t){super(t),this.on("pointsChange.konva tensionChange.konva closedChange.konva bezierChange.konva",(function(){this._clearCache("tensionPoints")}))}_sceneFunc(t){let e,n,r,i=this.points(),a=i.length,o=this.tension(),s=this.closed(),l=this.bezier();if(a){if(t.beginPath(),t.moveTo(i[0],i[1]),0!==o&&a>4){for(e=this.getTensionPoints(),n=e.length,r=s?0:4,s||t.quadraticCurveTo(e[0],e[1],e[2],e[3]);r<n-2;)t.bezierCurveTo(e[r++],e[r++],e[r++],e[r++],e[r++],e[r++]);s||t.quadraticCurveTo(e[n-2],e[n-1],i[a-2],i[a-1])}else if(l)for(r=2;r<a;)t.bezierCurveTo(i[r++],i[r++],i[r++],i[r++],i[r++],i[r++]);else for(r=2;r<a;r+=2)t.lineTo(i[r],i[r+1]);s?(t.closePath(),t.fillStrokeShape(this)):t.strokeShape(this)}}getTensionPoints(){return this._getCache("tensionPoints",this._getTensionPoints)}_getTensionPoints(){return this.closed()?this._getTensionPointsClosed():a(this.points(),this.tension())}_getTensionPointsClosed(){const t=this.points(),e=t.length,n=this.tension(),r=i(t[e-2],t[e-1],t[0],t[1],t[2],t[3],n),o=i(t[e-4],t[e-3],t[e-2],t[e-1],t[0],t[1],n),s=a(t,n);return[r[2],r[3]].concat(s).concat([o[0],o[1],t[e-2],t[e-1],o[2],o[3],r[0],r[1],t[0],t[1]])}getWidth(){return this.getSelfRect().width}getHeight(){return this.getSelfRect().height}getSelfRect(){let t=this.points();if(t.length<4)return{x:t[0]||0,y:t[1]||0,width:0,height:0};t=0!==this.tension()?[t[0],t[1],...this._getTensionPoints(),t[t.length-2],t[t.length-1]]:this.points();let e,n,r=t[0],i=t[0],a=t[1],o=t[1];for(let s=0;s<t.length/2;s++)e=t[2*s],n=t[2*s+1],r=Math.min(r,e),i=Math.max(i,e),a=Math.min(a,n),o=Math.max(o,n);return{x:r,y:a,width:i-r,height:o-a}}}return lt.Line=o,o.prototype.className="Line",o.prototype._attrsAffectingSize=["points","bezier","tension"],(0,e._registerNode)(o),t.Factory.addGetterSetter(o,"closed",!1),t.Factory.addGetterSetter(o,"bezier",!1),t.Factory.addGetterSetter(o,"tension",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(o,"points",[],(0,r.getNumberArrayValidator)()),lt}var ct,ht,dt,ft={},gt={};function pt(){if(ht)return ft;ht=1,Object.defineProperty(ft,"__esModule",{value:!0}),ft.Path=void 0;const t=_(),e=j(),n=c(),r=(ct||(ct=1,function(t){function e(t,e,r){const i=n(1,r,t),a=n(1,r,e),o=i*i+a*a;return Math.sqrt(o)}Object.defineProperty(t,"__esModule",{value:!0}),t.t2length=t.getQuadraticArcLength=t.getCubicArcLength=t.binomialCoefficients=t.cValues=t.tValues=void 0,t.tValues=[[],[],[-.5773502691896257,.5773502691896257],[0,-.7745966692414834,.7745966692414834],[-.33998104358485626,.33998104358485626,-.8611363115940526,.8611363115940526],[0,-.5384693101056831,.5384693101056831,-.906179845938664,.906179845938664],[.6612093864662645,-.6612093864662645,-.2386191860831969,.2386191860831969,-.932469514203152,.932469514203152],[0,.4058451513773972,-.4058451513773972,-.7415311855993945,.7415311855993945,-.9491079123427585,.9491079123427585],[-.1834346424956498,.1834346424956498,-.525532409916329,.525532409916329,-.7966664774136267,.7966664774136267,-.9602898564975363,.9602898564975363],[0,-.8360311073266358,.8360311073266358,-.9681602395076261,.9681602395076261,-.3242534234038089,.3242534234038089,-.6133714327005904,.6133714327005904],[-.14887433898163122,.14887433898163122,-.4333953941292472,.4333953941292472,-.6794095682990244,.6794095682990244,-.8650633666889845,.8650633666889845,-.9739065285171717,.9739065285171717],[0,-.26954315595234496,.26954315595234496,-.5190961292068118,.5190961292068118,-.7301520055740494,.7301520055740494,-.8870625997680953,.8870625997680953,-.978228658146057,.978228658146057],[-.1252334085114689,.1252334085114689,-.3678314989981802,.3678314989981802,-.5873179542866175,.5873179542866175,-.7699026741943047,.7699026741943047,-.9041172563704749,.9041172563704749,-.9815606342467192,.9815606342467192],[0,-.2304583159551348,.2304583159551348,-.44849275103644687,.44849275103644687,-.6423493394403402,.6423493394403402,-.8015780907333099,.8015780907333099,-.9175983992229779,.9175983992229779,-.9841830547185881,.9841830547185881],[-.10805494870734367,.10805494870734367,-.31911236892788974,.31911236892788974,-.5152486363581541,.5152486363581541,-.6872929048116855,.6872929048116855,-.827201315069765,.827201315069765,-.9284348836635735,.9284348836635735,-.9862838086968123,.9862838086968123],[0,-.20119409399743451,.20119409399743451,-.3941513470775634,.3941513470775634,-.5709721726085388,.5709721726085388,-.7244177313601701,.7244177313601701,-.8482065834104272,.8482065834104272,-.937273392400706,.937273392400706,-.9879925180204854,.9879925180204854],[-.09501250983763744,.09501250983763744,-.2816035507792589,.2816035507792589,-.45801677765722737,.45801677765722737,-.6178762444026438,.6178762444026438,-.755404408355003,.755404408355003,-.8656312023878318,.8656312023878318,-.9445750230732326,.9445750230732326,-.9894009349916499,.9894009349916499],[0,-.17848418149584785,.17848418149584785,-.3512317634538763,.3512317634538763,-.5126905370864769,.5126905370864769,-.6576711592166907,.6576711592166907,-.7815140038968014,.7815140038968014,-.8802391537269859,.8802391537269859,-.9506755217687678,.9506755217687678,-.9905754753144174,.9905754753144174],[-.0847750130417353,.0847750130417353,-.2518862256915055,.2518862256915055,-.41175116146284263,.41175116146284263,-.5597708310739475,.5597708310739475,-.6916870430603532,.6916870430603532,-.8037049589725231,.8037049589725231,-.8926024664975557,.8926024664975557,-.9558239495713977,.9558239495713977,-.9915651684209309,.9915651684209309],[0,-.16035864564022537,.16035864564022537,-.31656409996362983,.31656409996362983,-.46457074137596094,.46457074137596094,-.600545304661681,.600545304661681,-.7209661773352294,.7209661773352294,-.8227146565371428,.8227146565371428,-.9031559036148179,.9031559036148179,-.96020815213483,.96020815213483,-.9924068438435844,.9924068438435844],[-.07652652113349734,.07652652113349734,-.22778585114164507,.22778585114164507,-.37370608871541955,.37370608871541955,-.5108670019508271,.5108670019508271,-.636053680726515,.636053680726515,-.7463319064601508,.7463319064601508,-.8391169718222188,.8391169718222188,-.912234428251326,.912234428251326,-.9639719272779138,.9639719272779138,-.9931285991850949,.9931285991850949],[0,-.1455618541608951,.1455618541608951,-.2880213168024011,.2880213168024011,-.4243421202074388,.4243421202074388,-.5516188358872198,.5516188358872198,-.6671388041974123,.6671388041974123,-.7684399634756779,.7684399634756779,-.8533633645833173,.8533633645833173,-.9200993341504008,.9200993341504008,-.9672268385663063,.9672268385663063,-.9937521706203895,.9937521706203895],[-.06973927331972223,.06973927331972223,-.20786042668822127,.20786042668822127,-.34193582089208424,.34193582089208424,-.469355837986757,.469355837986757,-.5876404035069116,.5876404035069116,-.6944872631866827,.6944872631866827,-.7878168059792081,.7878168059792081,-.8658125777203002,.8658125777203002,-.926956772187174,.926956772187174,-.9700604978354287,.9700604978354287,-.9942945854823992,.9942945854823992],[0,-.1332568242984661,.1332568242984661,-.26413568097034495,.26413568097034495,-.3903010380302908,.3903010380302908,-.5095014778460075,.5095014778460075,-.6196098757636461,.6196098757636461,-.7186613631319502,.7186613631319502,-.8048884016188399,.8048884016188399,-.8767523582704416,.8767523582704416,-.9329710868260161,.9329710868260161,-.9725424712181152,.9725424712181152,-.9947693349975522,.9947693349975522],[-.06405689286260563,.06405689286260563,-.1911188674736163,.1911188674736163,-.3150426796961634,.3150426796961634,-.4337935076260451,.4337935076260451,-.5454214713888396,.5454214713888396,-.6480936519369755,.6480936519369755,-.7401241915785544,.7401241915785544,-.820001985973903,.820001985973903,-.8864155270044011,.8864155270044011,-.9382745520027328,.9382745520027328,-.9747285559713095,.9747285559713095,-.9951872199970213,.9951872199970213]],t.cValues=[[],[],[1,1],[.8888888888888888,.5555555555555556,.5555555555555556],[.6521451548625461,.6521451548625461,.34785484513745385,.34785484513745385],[.5688888888888889,.47862867049936647,.47862867049936647,.23692688505618908,.23692688505618908],[.3607615730481386,.3607615730481386,.46791393457269104,.46791393457269104,.17132449237917036,.17132449237917036],[.4179591836734694,.3818300505051189,.3818300505051189,.27970539148927664,.27970539148927664,.1294849661688697,.1294849661688697],[.362683783378362,.362683783378362,.31370664587788727,.31370664587788727,.22238103445337448,.22238103445337448,.10122853629037626,.10122853629037626],[.3302393550012598,.1806481606948574,.1806481606948574,.08127438836157441,.08127438836157441,.31234707704000286,.31234707704000286,.26061069640293544,.26061069640293544],[.29552422471475287,.29552422471475287,.26926671930999635,.26926671930999635,.21908636251598204,.21908636251598204,.1494513491505806,.1494513491505806,.06667134430868814,.06667134430868814],[.2729250867779006,.26280454451024665,.26280454451024665,.23319376459199048,.23319376459199048,.18629021092773426,.18629021092773426,.1255803694649046,.1255803694649046,.05566856711617366,.05566856711617366],[.24914704581340277,.24914704581340277,.2334925365383548,.2334925365383548,.20316742672306592,.20316742672306592,.16007832854334622,.16007832854334622,.10693932599531843,.10693932599531843,.04717533638651183,.04717533638651183],[.2325515532308739,.22628318026289723,.22628318026289723,.2078160475368885,.2078160475368885,.17814598076194574,.17814598076194574,.13887351021978725,.13887351021978725,.09212149983772845,.09212149983772845,.04048400476531588,.04048400476531588],[.2152638534631578,.2152638534631578,.2051984637212956,.2051984637212956,.18553839747793782,.18553839747793782,.15720316715819355,.15720316715819355,.12151857068790319,.12151857068790319,.08015808715976021,.08015808715976021,.03511946033175186,.03511946033175186],[.2025782419255613,.19843148532711158,.19843148532711158,.1861610000155622,.1861610000155622,.16626920581699392,.16626920581699392,.13957067792615432,.13957067792615432,.10715922046717194,.10715922046717194,.07036604748810812,.07036604748810812,.03075324199611727,.03075324199611727],[.1894506104550685,.1894506104550685,.18260341504492358,.18260341504492358,.16915651939500254,.16915651939500254,.14959598881657674,.14959598881657674,.12462897125553388,.12462897125553388,.09515851168249279,.09515851168249279,.062253523938647894,.062253523938647894,.027152459411754096,.027152459411754096],[.17944647035620653,.17656270536699264,.17656270536699264,.16800410215645004,.16800410215645004,.15404576107681028,.15404576107681028,.13513636846852548,.13513636846852548,.11188384719340397,.11188384719340397,.08503614831717918,.08503614831717918,.0554595293739872,.0554595293739872,.02414830286854793,.02414830286854793],[.1691423829631436,.1691423829631436,.16427648374583273,.16427648374583273,.15468467512626524,.15468467512626524,.14064291467065065,.14064291467065065,.12255520671147846,.12255520671147846,.10094204410628717,.10094204410628717,.07642573025488905,.07642573025488905,.0497145488949698,.0497145488949698,.02161601352648331,.02161601352648331],[.1610544498487837,.15896884339395434,.15896884339395434,.15276604206585967,.15276604206585967,.1426067021736066,.1426067021736066,.12875396253933621,.12875396253933621,.11156664554733399,.11156664554733399,.09149002162245,.09149002162245,.06904454273764123,.06904454273764123,.0448142267656996,.0448142267656996,.019461788229726478,.019461788229726478],[.15275338713072584,.15275338713072584,.14917298647260374,.14917298647260374,.14209610931838204,.14209610931838204,.13168863844917664,.13168863844917664,.11819453196151841,.11819453196151841,.10193011981724044,.10193011981724044,.08327674157670475,.08327674157670475,.06267204833410907,.06267204833410907,.04060142980038694,.04060142980038694,.017614007139152118,.017614007139152118],[.14608113364969041,.14452440398997005,.14452440398997005,.13988739479107315,.13988739479107315,.13226893863333747,.13226893863333747,.12183141605372853,.12183141605372853,.10879729916714838,.10879729916714838,.09344442345603386,.09344442345603386,.0761001136283793,.0761001136283793,.057134425426857205,.057134425426857205,.036953789770852494,.036953789770852494,.016017228257774335,.016017228257774335],[.13925187285563198,.13925187285563198,.13654149834601517,.13654149834601517,.13117350478706238,.13117350478706238,.12325237681051242,.12325237681051242,.11293229608053922,.11293229608053922,.10041414444288096,.10041414444288096,.08594160621706773,.08594160621706773,.06979646842452049,.06979646842452049,.052293335152683286,.052293335152683286,.03377490158481415,.03377490158481415,.0146279952982722,.0146279952982722],[.13365457218610619,.1324620394046966,.1324620394046966,.12890572218808216,.12890572218808216,.12304908430672953,.12304908430672953,.11499664022241136,.11499664022241136,.10489209146454141,.10489209146454141,.09291576606003515,.09291576606003515,.07928141177671895,.07928141177671895,.06423242140852585,.06423242140852585,.04803767173108467,.04803767173108467,.030988005856979445,.030988005856979445,.013411859487141771,.013411859487141771],[.12793819534675216,.12793819534675216,.1258374563468283,.1258374563468283,.12167047292780339,.12167047292780339,.1155056680537256,.1155056680537256,.10744427011596563,.10744427011596563,.09761865210411388,.09761865210411388,.08619016153195327,.08619016153195327,.0733464814110803,.0733464814110803,.05929858491543678,.05929858491543678,.04427743881741981,.04427743881741981,.028531388628933663,.028531388628933663,.0123412297999872,.0123412297999872]],t.binomialCoefficients=[[1],[1,1],[1,2,1],[1,3,3,1]],t.getCubicArcLength=(n,r,i)=>{let a,o;const s=i/2;a=0;for(let l=0;l<20;l++)o=s*t.tValues[20][l]+s,a+=t.cValues[20][l]*e(n,r,o);return s*a},t.getQuadraticArcLength=(t,e,n)=>{void 0===n&&(n=1);const r=t[0]-2*t[1]+t[2],i=e[0]-2*e[1]+e[2],a=2*t[1]-2*t[0],o=2*e[1]-2*e[0],s=4*(r*r+i*i),l=4*(r*a+i*o),u=a*a+o*o;if(0===s)return n*Math.sqrt(Math.pow(t[2]-t[0],2)+Math.pow(e[2]-e[0],2));const c=l/(2*s),h=n+c,d=u/s-c*c,f=h*h+d>0?Math.sqrt(h*h+d):0,g=c*c+d>0?Math.sqrt(c*c+d):0,p=c+Math.sqrt(c*c+d)!==0?d*Math.log(Math.abs((h+f)/(c+g))):0;return Math.sqrt(s)/2*(h*f-c*g+p)};const n=(e,r,i)=>{const a=i.length-1;let o,s;if(0===a)return 0;if(0===e){s=0;for(let e=0;e<=a;e++)s+=t.binomialCoefficients[a][e]*Math.pow(1-r,a-e)*Math.pow(r,e)*i[e];return s}o=new Array(a);for(let t=0;t<a;t++)o[t]=a*(i[t+1]-i[t]);return n(e-1,r,o)};t.t2length=(t,e,n)=>{let r=1,i=t/e,a=(t-n(i))/e,o=0;for(;r>.001;){const s=n(i+a),l=Math.abs(t-s)/e;if(l<r)r=l,i+=a;else{const o=n(i-a),s=Math.abs(t-o)/e;s<r?(r=s,i-=a):a/=2}if(o++,o>500)break}return i}}(gt)),gt);let i=class t extends e.Shape{constructor(t){super(t),this.dataArray=[],this.pathLength=0,this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute()}))}_readDataAttribute(){this.dataArray=t.parsePathData(this.data()),this.pathLength=t.getPathLength(this.dataArray)}_sceneFunc(t){const e=this.dataArray;t.beginPath();let n=!1;for(let g=0;g<e.length;g++){const p=e[g].command,m=e[g].points;switch(p){case"L":t.lineTo(m[0],m[1]);break;case"M":t.moveTo(m[0],m[1]);break;case"C":t.bezierCurveTo(m[0],m[1],m[2],m[3],m[4],m[5]);break;case"Q":t.quadraticCurveTo(m[0],m[1],m[2],m[3]);break;case"A":var r=m[0],i=m[1],a=m[2],o=m[3],s=m[4],l=m[5],u=m[6],c=m[7],h=a>o?a:o,d=a>o?1:a/o,f=a>o?o/a:1;t.translate(r,i),t.rotate(u),t.scale(d,f),t.arc(0,0,h,s,s+l,1-c),t.scale(1/d,1/f),t.rotate(-u),t.translate(-r,-i);break;case"z":n=!0,t.closePath()}}n||this.hasFill()?t.fillStrokeShape(this):t.strokeShape(this)}getSelfRect(){let e=[];this.dataArray.forEach((function(n){if("A"===n.command){const r=n.points[4],i=n.points[5],a=n.points[4]+i;let o=Math.PI/180;if(Math.abs(r-a)<o&&(o=Math.abs(r-a)),i<0)for(let s=r-o;s>a;s-=o){const r=t.getPointOnEllipticalArc(n.points[0],n.points[1],n.points[2],n.points[3],s,0);e.push(r.x,r.y)}else for(let s=r+o;s<a;s+=o){const r=t.getPointOnEllipticalArc(n.points[0],n.points[1],n.points[2],n.points[3],s,0);e.push(r.x,r.y)}}else if("C"===n.command)for(let r=0;r<=1;r+=.01){const i=t.getPointOnCubicBezier(r,n.start.x,n.start.y,n.points[0],n.points[1],n.points[2],n.points[3],n.points[4],n.points[5]);e.push(i.x,i.y)}else e=e.concat(n.points)}));let n,r,i=e[0],a=e[0],o=e[1],s=e[1];for(let t=0;t<e.length/2;t++)n=e[2*t],r=e[2*t+1],isNaN(n)||(i=Math.min(i,n),a=Math.max(a,n)),isNaN(r)||(o=Math.min(o,r),s=Math.max(s,r));return{x:i,y:o,width:a-i,height:s-o}}getLength(){return this.pathLength}getPointAtLength(e){return t.getPointAtLengthOfDataArray(e,this.dataArray)}static getLineLength(t,e,n,r){return Math.sqrt((n-t)*(n-t)+(r-e)*(r-e))}static getPathLength(t){let e=0;for(let n=0;n<t.length;++n)e+=t[n].pathLength;return e}static getPointAtLengthOfDataArray(e,n){let i,a=0,o=n.length;if(!o)return null;for(;a<o&&e>n[a].pathLength;)e-=n[a].pathLength,++a;if(a===o)return i=n[a-1].points.slice(-2),{x:i[0],y:i[1]};if(e<.01)return i=n[a].points.slice(0,2),{x:i[0],y:i[1]};const s=n[a],l=s.points;switch(s.command){case"L":return t.getPointOnLine(e,s.start.x,s.start.y,l[0],l[1]);case"C":return t.getPointOnCubicBezier((0,r.t2length)(e,t.getPathLength(n),(t=>(0,r.getCubicArcLength)([s.start.x,l[0],l[2],l[4]],[s.start.y,l[1],l[3],l[5]],t))),s.start.x,s.start.y,l[0],l[1],l[2],l[3],l[4],l[5]);case"Q":return t.getPointOnQuadraticBezier((0,r.t2length)(e,t.getPathLength(n),(t=>(0,r.getQuadraticArcLength)([s.start.x,l[0],l[2]],[s.start.y,l[1],l[3]],t))),s.start.x,s.start.y,l[0],l[1],l[2],l[3]);case"A":var u=l[0],c=l[1],h=l[2],d=l[3],f=l[4],g=l[5],p=l[6];return f+=g*e/s.pathLength,t.getPointOnEllipticalArc(u,c,h,d,f,p)}return null}static getPointOnLine(t,e,n,r,i,a,o){a=null!=a?a:e,o=null!=o?o:n;const s=this.getLineLength(e,n,r,i);if(s<1e-10)return{x:e,y:n};if(r===e)return{x:a,y:o+(i>n?t:-t)};const l=(i-n)/(r-e),u=Math.sqrt(t*t/(1+l*l))*(r<e?-1:1),c=l*u;if(Math.abs(o-n-l*(a-e))<1e-10)return{x:a+u,y:o+c};const h=((a-e)*(r-e)+(o-n)*(i-n))/(s*s),d=e+h*(r-e),f=n+h*(i-n),g=this.getLineLength(a,o,d,f),p=Math.sqrt(t*t-g*g),m=Math.sqrt(p*p/(1+l*l))*(r<e?-1:1);return{x:d+m,y:f+l*m}}static getPointOnCubicBezier(t,e,n,r,i,a,o,s,l){function u(t){return t*t*t}function c(t){return 3*t*t*(1-t)}function h(t){return 3*t*(1-t)*(1-t)}function d(t){return(1-t)*(1-t)*(1-t)}return{x:s*u(t)+a*c(t)+r*h(t)+e*d(t),y:l*u(t)+o*c(t)+i*h(t)+n*d(t)}}static getPointOnQuadraticBezier(t,e,n,r,i,a,o){function s(t){return t*t}function l(t){return 2*t*(1-t)}function u(t){return(1-t)*(1-t)}return{x:a*s(t)+r*l(t)+e*u(t),y:o*s(t)+i*l(t)+n*u(t)}}static getPointOnEllipticalArc(t,e,n,r,i,a){const o=Math.cos(a),s=Math.sin(a),l=n*Math.cos(i),u=r*Math.sin(i);return{x:t+(l*o-u*s),y:e+(l*s+u*o)}}static parsePathData(t){if(!t)return[];let e=t;const n=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"];e=e.replace(new RegExp(" ","g"),",");for(var r=0;r<n.length;r++)e=e.replace(new RegExp(n[r],"g"),"|"+n[r]);const i=e.split("|"),a=[],o=[];let s=0,l=0;const u=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi;let c;for(r=1;r<i.length;r++){let t=i[r],e=t.charAt(0);for(t=t.slice(1),o.length=0;c=u.exec(t);)o.push(c[0]);const n=[];for(let r=0,i=o.length;r<i;r++){if("00"===o[r]){n.push(0,0);continue}const t=parseFloat(o[r]);isNaN(t)?n.push(0):n.push(t)}for(;n.length>0&&!isNaN(n[0]);){let t="",r=[];const i=s,o=l;var h,d,f,g,p,m,y,v,b,_;switch(e){case"l":s+=n.shift(),l+=n.shift(),t="L",r.push(s,l);break;case"L":s=n.shift(),l=n.shift(),r.push(s,l);break;case"m":var S=n.shift(),x=n.shift();if(s+=S,l+=x,t="M",a.length>2&&"z"===a[a.length-1].command)for(let t=a.length-2;t>=0;t--)if("M"===a[t].command){s=a[t].points[0]+S,l=a[t].points[1]+x;break}r.push(s,l),e="l";break;case"M":s=n.shift(),l=n.shift(),t="M",r.push(s,l),e="L";break;case"h":s+=n.shift(),t="L",r.push(s,l);break;case"H":s=n.shift(),t="L",r.push(s,l);break;case"v":l+=n.shift(),t="L",r.push(s,l);break;case"V":l=n.shift(),t="L",r.push(s,l);break;case"C":r.push(n.shift(),n.shift(),n.shift(),n.shift()),s=n.shift(),l=n.shift(),r.push(s,l);break;case"c":r.push(s+n.shift(),l+n.shift(),s+n.shift(),l+n.shift()),s+=n.shift(),l+=n.shift(),t="C",r.push(s,l);break;case"S":d=s,f=l,"C"===(h=a[a.length-1]).command&&(d=s+(s-h.points[2]),f=l+(l-h.points[3])),r.push(d,f,n.shift(),n.shift()),s=n.shift(),l=n.shift(),t="C",r.push(s,l);break;case"s":d=s,f=l,"C"===(h=a[a.length-1]).command&&(d=s+(s-h.points[2]),f=l+(l-h.points[3])),r.push(d,f,s+n.shift(),l+n.shift()),s+=n.shift(),l+=n.shift(),t="C",r.push(s,l);break;case"Q":r.push(n.shift(),n.shift()),s=n.shift(),l=n.shift(),r.push(s,l);break;case"q":r.push(s+n.shift(),l+n.shift()),s+=n.shift(),l+=n.shift(),t="Q",r.push(s,l);break;case"T":d=s,f=l,"Q"===(h=a[a.length-1]).command&&(d=s+(s-h.points[0]),f=l+(l-h.points[1])),s=n.shift(),l=n.shift(),t="Q",r.push(d,f,s,l);break;case"t":d=s,f=l,"Q"===(h=a[a.length-1]).command&&(d=s+(s-h.points[0]),f=l+(l-h.points[1])),s+=n.shift(),l+=n.shift(),t="Q",r.push(d,f,s,l);break;case"A":g=n.shift(),p=n.shift(),m=n.shift(),y=n.shift(),v=n.shift(),b=s,_=l,s=n.shift(),l=n.shift(),t="A",r=this.convertEndpointToCenterParameterization(b,_,s,l,y,v,g,p,m);break;case"a":g=n.shift(),p=n.shift(),m=n.shift(),y=n.shift(),v=n.shift(),b=s,_=l,s+=n.shift(),l+=n.shift(),t="A",r=this.convertEndpointToCenterParameterization(b,_,s,l,y,v,g,p,m)}a.push({command:t||e,points:r,start:{x:i,y:o},pathLength:this.calcLength(i,o,t||e,r)})}"z"!==e&&"Z"!==e||a.push({command:"z",points:[],start:void 0,pathLength:0})}return a}static calcLength(e,n,i,a){let o,s,l,u;const c=t;switch(i){case"L":return c.getLineLength(e,n,a[0],a[1]);case"C":return(0,r.getCubicArcLength)([e,a[0],a[2],a[4]],[n,a[1],a[3],a[5]],1);case"Q":return(0,r.getQuadraticArcLength)([e,a[0],a[2]],[n,a[1],a[3]],1);case"A":o=0;var h=a[4],d=a[5],f=a[4]+d,g=Math.PI/180;if(Math.abs(h-f)<g&&(g=Math.abs(h-f)),s=c.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],h,0),d<0)for(u=h-g;u>f;u-=g)l=c.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),o+=c.getLineLength(s.x,s.y,l.x,l.y),s=l;else for(u=h+g;u<f;u+=g)l=c.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),o+=c.getLineLength(s.x,s.y,l.x,l.y),s=l;return l=c.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],f,0),o+=c.getLineLength(s.x,s.y,l.x,l.y),o}return 0}static convertEndpointToCenterParameterization(t,e,n,r,i,a,o,s,l){const u=l*(Math.PI/180),c=Math.cos(u)*(t-n)/2+Math.sin(u)*(e-r)/2,h=-1*Math.sin(u)*(t-n)/2+Math.cos(u)*(e-r)/2,d=c*c/(o*o)+h*h/(s*s);d>1&&(o*=Math.sqrt(d),s*=Math.sqrt(d));let f=Math.sqrt((o*o*(s*s)-o*o*(h*h)-s*s*(c*c))/(o*o*(h*h)+s*s*(c*c)));i===a&&(f*=-1),isNaN(f)&&(f=0);const g=f*o*h/s,p=f*-s*c/o,m=(t+n)/2+Math.cos(u)*g-Math.sin(u)*p,y=(e+r)/2+Math.sin(u)*g+Math.cos(u)*p,v=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},b=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(v(t)*v(e))},_=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(b(t,e))},S=_([1,0],[(c-g)/o,(h-p)/s]),x=[(c-g)/o,(h-p)/s],w=[(-1*c-g)/o,(-1*h-p)/s];let C=_(x,w);return b(x,w)<=-1&&(C=Math.PI),b(x,w)>=1&&(C=0),0===a&&C>0&&(C-=2*Math.PI),1===a&&C<0&&(C+=2*Math.PI),[m,y,o,s,S,C,u,a]}};return ft.Path=i,i.prototype.className="Path",i.prototype._attrsAffectingSize=["data"],(0,n._registerNode)(i),t.Factory.addGetterSetter(i,"data"),ft}var mt,yt={};var vt,bt={};var _t,St={};var xt,wt={};var Ct,kt={};function Pt(){if(Ct)return kt;Ct=1,Object.defineProperty(kt,"__esModule",{value:!0}),kt.Rect=void 0;const t=_(),e=j(),n=c(),r=f(),i=b();let a=class extends e.Shape{_sceneFunc(t){const e=this.cornerRadius(),n=this.width(),i=this.height();t.beginPath(),e?r.Util.drawRoundedRectPath(t,n,i,e):t.rect(0,0,n,i),t.closePath(),t.fillStrokeShape(this)}};return kt.Rect=a,a.prototype.className="Rect",(0,n._registerNode)(a),t.Factory.addGetterSetter(a,"cornerRadius",0,(0,i.getNumberOrArrayOfNumbersValidator)(4)),kt}var Tt,Et={};var Ft,Nt={};var Mt,At={};var Rt,Lt={};var Dt,zt={};function It(){if(Dt)return zt;Dt=1,Object.defineProperty(zt,"__esModule",{value:!0}),zt.Text=void 0,zt.stringToArray=o;const t=f(),e=_(),n=j(),r=c(),i=b(),a=c();function o(t){return[...t].reduce(((t,e,n,r)=>{if(new RegExp("\\p{Emoji}","u").test(e)){const i=r[n+1];i&&new RegExp("\\p{Emoji_Modifier}|\\u200D","u").test(i)?(t.push(e+i),r[n+1]=""):t.push(e)}else new RegExp("\\p{Regional_Indicator}{2}","u").test(e+(r[n+1]||""))?t.push(e+r[n+1]):n>0&&new RegExp("\\p{Mn}|\\p{Me}|\\p{Mc}","u").test(e)?t[t.length-1]+=e:e&&t.push(e);return t}),[])}const s="auto",l="inherit",u="justify",h="left",d="middle",g="normal",p=" ",m="none",y=["direction","fontFamily","fontSize","fontStyle","fontVariant","padding","align","verticalAlign","lineHeight","text","width","height","wrap","ellipsis","letterSpacing"],v=y.length;let S;function x(){return S||(S=t.Util.createCanvasElement().getContext("2d"),S)}let w=class extends n.Shape{constructor(t){super(function(t){return(t=t||{}).fillLinearGradientColorStops||t.fillRadialGradientColorStops||t.fillPatternImage||(t.fill=t.fill||"black"),t}(t)),this._partialTextX=0,this._partialTextY=0;for(let e=0;e<v;e++)this.on(y[e]+"Change.konva",this._setTextData);this._setTextData()}_sceneFunc(t){const e=this.textArr,n=e.length;if(!this.text())return;let i,a=this.padding(),s=this.fontSize(),c=this.lineHeight()*s,f=this.verticalAlign(),g=this.direction(),p=0,m=this.align(),y=this.getWidth(),v=this.letterSpacing(),b=this.fill(),_=this.textDecoration(),S=-1!==_.indexOf("underline"),x=-1!==_.indexOf("line-through");g=g===l?t.direction:g;let w=c/2,C=d;if(r.Konva._fixTextRendering){const t=this.measureSize("M");C="alphabetic",w=(t.fontBoundingBoxAscent-t.fontBoundingBoxDescent)/2+c/2}var k=0,P=0;for("rtl"===g&&t.setAttr("direction",g),t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",C),t.setAttr("textAlign",h),f===d?p=(this.getHeight()-n*c-2*a)/2:"bottom"===f&&(p=this.getHeight()-n*c-2*a),t.translate(a,p+a),i=0;i<n;i++){k=0,P=0;var T,E,F=e[i],N=F.text,M=F.width,A=F.lastInParagraph;if(t.save(),"right"===m?k+=y-M-2*a:"center"===m&&(k+=(y-M-2*a)/2),S){t.save(),t.beginPath();const e=k,n=w+P+(r.Konva._fixTextRendering?Math.round(s/4):Math.round(s/2));t.moveTo(e,n),T=N.split(" ").length-1,E=m!==u||A?M:y-2*a,t.lineTo(e+Math.round(E),n),t.lineWidth=s/15;const i=this._getLinearGradient();t.strokeStyle=i||b,t.stroke(),t.restore()}if(x){t.save(),t.beginPath();const e=r.Konva._fixTextRendering?-Math.round(s/4):0;t.moveTo(k,w+P+e),T=N.split(" ").length-1,E=m!==u||A?M:y-2*a,t.lineTo(k+Math.round(E),w+P+e),t.lineWidth=s/15;const n=this._getLinearGradient();t.strokeStyle=n||b,t.stroke(),t.restore()}if("rtl"===g||0===v&&m!==u)0!==v&&t.setAttr("letterSpacing",`${v}px`),this._partialTextX=k,this._partialTextY=w+P,this._partialText=N,t.fillStrokeShape(this);else{T=N.split(" ").length-1;const e=o(N);for(let n=0;n<e.length;n++){const r=e[n];" "!==r||A||m!==u||(k+=(y-2*a-M)/T),this._partialTextX=k,this._partialTextY=w+P,this._partialText=r,t.fillStrokeShape(this),k+=this.measureSize(r).width+v}}t.restore(),n>1&&(w+=c)}}_hitFunc(t){const e=this.getWidth(),n=this.getHeight();t.beginPath(),t.rect(0,0,e,n),t.closePath(),t.fillStrokeShape(this)}setText(e){const n=t.Util._isString(e)?e:null==e?"":e+"";return this._setAttr("text",n),this}getWidth(){return this.attrs.width===s||void 0===this.attrs.width?this.getTextWidth()+2*this.padding():this.attrs.width}getHeight(){return this.attrs.height===s||void 0===this.attrs.height?this.fontSize()*this.textArr.length*this.lineHeight()+2*this.padding():this.attrs.height}getTextWidth(){return this.textWidth}getTextHeight(){return t.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}measureSize(t){var e,n,r,i,a,o,s,l,u,c,h;let d,f=x(),g=this.fontSize();f.save(),f.font=this._getContextFont(),d=f.measureText(t),f.restore();const p=g/100;return{actualBoundingBoxAscent:null!==(e=d.actualBoundingBoxAscent)&&void 0!==e?e:71.58203125*p,actualBoundingBoxDescent:null!==(n=d.actualBoundingBoxDescent)&&void 0!==n?n:0,actualBoundingBoxLeft:null!==(r=d.actualBoundingBoxLeft)&&void 0!==r?r:-7.421875*p,actualBoundingBoxRight:null!==(i=d.actualBoundingBoxRight)&&void 0!==i?i:75.732421875*p,alphabeticBaseline:null!==(a=d.alphabeticBaseline)&&void 0!==a?a:0,emHeightAscent:null!==(o=d.emHeightAscent)&&void 0!==o?o:100*p,emHeightDescent:null!==(s=d.emHeightDescent)&&void 0!==s?s:-20*p,fontBoundingBoxAscent:null!==(l=d.fontBoundingBoxAscent)&&void 0!==l?l:91*p,fontBoundingBoxDescent:null!==(u=d.fontBoundingBoxDescent)&&void 0!==u?u:21*p,hangingBaseline:null!==(c=d.hangingBaseline)&&void 0!==c?c:72.80000305175781*p,ideographicBaseline:null!==(h=d.ideographicBaseline)&&void 0!==h?h:-21*p,width:d.width,height:g}}_getContextFont(){return this.fontStyle()+p+this.fontVariant()+p+(this.fontSize()+"px ")+this.fontFamily().split(",").map((t=>{const e=(t=t.trim()).indexOf(" ")>=0,n=t.indexOf('"')>=0||t.indexOf("'")>=0;return e&&!n&&(t=`"${t}"`),t})).join(", ")}_addTextLine(t){this.align()===u&&(t=t.trim());const e=this._getTextWidth(t);return this.textArr.push({text:t,width:e,lastInParagraph:!1})}_getTextWidth(t){const e=this.letterSpacing(),n=t.length;return x().measureText(t).width+e*n}_setTextData(){let t=this.text().split("\n"),e=+this.fontSize(),n=0,r=this.lineHeight()*e,i=this.attrs.width,a=this.attrs.height,l=i!==s&&void 0!==i,u=a!==s&&void 0!==a,c=this.padding(),h=i-2*c,d=a-2*c,f=0,g=this.wrap(),y="char"!==g&&g!==m,v=this.ellipsis();this.textArr=[],x().font=this._getContextFont();const b=v?this._getTextWidth("…"):0;for(let s=0,m=t.length;s<m;++s){let e=t[s],i=this._getTextWidth(e);if(l&&i>h)for(;e.length>0;){let t=0,a=o(e).length,s="",l=0;for(;t<a;){const n=t+a>>>1,i=o(e).slice(0,n+1).join(""),c=this._getTextWidth(i);(v&&u&&f+r>d?c+b:c)<=h?(t=n+1,s=i,l=c):a=n}if(!s)break;if(y){const n=o(e),r=o(s),i=n[r.length];let a;if((i===p||"-"===i)&&l<=h)a=r.length;else{const t=r.lastIndexOf(p),e=r.lastIndexOf("-");a=Math.max(t,e)+1}a>0&&(t=a,s=n.slice(0,t).join(""),l=this._getTextWidth(s))}s=s.trimRight(),this._addTextLine(s),n=Math.max(n,l),f+=r;if(this._shouldHandleEllipsis(f)){this._tryToAddEllipsisToLastLine();break}if(e=o(e).slice(t).join("").trimLeft(),e.length>0&&(i=this._getTextWidth(e),i<=h)){this._addTextLine(e),f+=r,n=Math.max(n,i);break}}else this._addTextLine(e),f+=r,n=Math.max(n,i),this._shouldHandleEllipsis(f)&&s<m-1&&this._tryToAddEllipsisToLastLine();if(this.textArr[this.textArr.length-1]&&(this.textArr[this.textArr.length-1].lastInParagraph=!0),u&&f+r>d)break}this.textHeight=e,this.textWidth=n}_shouldHandleEllipsis(t){const e=+this.fontSize(),n=this.lineHeight()*e,r=this.attrs.height,i=r!==s&&void 0!==r,a=r-2*this.padding();return!(this.wrap()!==m)||i&&t+n>a}_tryToAddEllipsisToLastLine(){const t=this.attrs.width,e=t!==s&&void 0!==t,n=t-2*this.padding(),r=this.ellipsis(),i=this.textArr[this.textArr.length-1];if(i&&r){if(e){this._getTextWidth(i.text+"…")<n||(i.text=i.text.slice(0,i.text.length-3))}this.textArr.splice(this.textArr.length-1,1),this._addTextLine(i.text+"…")}}getStrokeScaleEnabled(){return!0}_useBufferCanvas(){const t=-1!==this.textDecoration().indexOf("underline")||-1!==this.textDecoration().indexOf("line-through"),e=this.hasShadow();return!(!t||!e)||super._useBufferCanvas()}};return zt.Text=w,w.prototype._fillFunc=function(t){t.fillText(this._partialText,this._partialTextX,this._partialTextY)},w.prototype._strokeFunc=function(t){t.setAttr("miterLimit",2),t.strokeText(this._partialText,this._partialTextX,this._partialTextY)},w.prototype.className="Text",w.prototype._attrsAffectingSize=["text","fontSize","padding","wrap","lineHeight","letterSpacing"],(0,a._registerNode)(w),e.Factory.overWriteSetter(w,"width",(0,i.getNumberOrAutoValidator)()),e.Factory.overWriteSetter(w,"height",(0,i.getNumberOrAutoValidator)()),e.Factory.addGetterSetter(w,"direction",l),e.Factory.addGetterSetter(w,"fontFamily","Arial"),e.Factory.addGetterSetter(w,"fontSize",12,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(w,"fontStyle",g),e.Factory.addGetterSetter(w,"fontVariant",g),e.Factory.addGetterSetter(w,"padding",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(w,"align",h),e.Factory.addGetterSetter(w,"verticalAlign","top"),e.Factory.addGetterSetter(w,"lineHeight",1,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(w,"wrap","word"),e.Factory.addGetterSetter(w,"ellipsis",!1,(0,i.getBooleanValidator)()),e.Factory.addGetterSetter(w,"letterSpacing",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(w,"text","",(0,i.getStringValidator)()),e.Factory.addGetterSetter(w,"textDecoration",""),zt}var Gt,Ot={};var Ut,Bt={};function Ht(){if(Ut)return Bt;Ut=1,Object.defineProperty(Bt,"__esModule",{value:!0}),Bt.Transformer=void 0;const t=f(),e=_(),n=M(),r=j(),i=Pt(),a=Q(),o=c(),s=b(),l=c(),u="tr-konva",h=["resizeEnabledChange","rotateAnchorOffsetChange","rotateEnabledChange","enabledAnchorsChange","anchorSizeChange","borderEnabledChange","borderStrokeChange","borderStrokeWidthChange","borderDashChange","anchorStrokeChange","anchorStrokeWidthChange","anchorFillChange","anchorCornerRadiusChange","ignoreStrokeChange","anchorStyleFuncChange"].map((t=>t+`.${u}`)).join(" "),d="nodesRect",g=["widthChange","heightChange","scaleXChange","scaleYChange","skewXChange","skewYChange","rotationChange","offsetXChange","offsetYChange","transformsEnabledChange","strokeWidthChange"],p={"top-left":-45,"top-center":0,"top-right":45,"middle-right":-90,"middle-left":90,"bottom-left":-135,"bottom-center":180,"bottom-right":135},m="ontouchstart"in o.Konva._global;const y=["top-left","top-center","top-right","middle-right","middle-left","bottom-left","bottom-center","bottom-right"];function v(t,e,n){const r=n.x+(t.x-n.x)*Math.cos(e)-(t.y-n.y)*Math.sin(e),i=n.y+(t.x-n.x)*Math.sin(e)+(t.y-n.y)*Math.cos(e);return{...t,rotation:t.rotation+e,x:r,y:i}}function S(t,e){const n=function(t){return{x:t.x+t.width/2*Math.cos(t.rotation)+t.height/2*Math.sin(-t.rotation),y:t.y+t.height/2*Math.cos(t.rotation)+t.width/2*Math.sin(t.rotation)}}(t);return v(t,e,n)}let x=0;class w extends a.Group{constructor(t){super(t),this._movingAnchorName=null,this._transforming=!1,this._createElements(),this._handleMouseMove=this._handleMouseMove.bind(this),this._handleMouseUp=this._handleMouseUp.bind(this),this.update=this.update.bind(this),this.on(h,this.update),this.getNode()&&this.update()}attachTo(t){return this.setNode(t),this}setNode(e){return t.Util.warn("tr.setNode(shape), tr.node(shape) and tr.attachTo(shape) methods are deprecated. Please use tr.nodes(nodesArray) instead."),this.setNodes([e])}getNode(){return this._nodes&&this._nodes[0]}_getEventNamespace(){return u+this._id}setNodes(e=[]){this._nodes&&this._nodes.length&&this.detach();const n=e.filter((e=>!e.isAncestorOf(this)||(t.Util.error("Konva.Transformer cannot be an a child of the node you are trying to attach"),!1)));this._nodes=e=n,1===e.length&&this.useSingleNodeRotation()?this.rotation(e[0].getAbsoluteRotation()):this.rotation(0),this._nodes.forEach((t=>{const e=()=>{1===this.nodes().length&&this.useSingleNodeRotation()&&this.rotation(this.nodes()[0].getAbsoluteRotation()),this._resetTransformCache(),this._transforming||this.isDragging()||this.update()};if(t._attrsAffectingSize.length){const n=t._attrsAffectingSize.map((t=>t+"Change."+this._getEventNamespace())).join(" ");t.on(n,e)}t.on(g.map((t=>t+`.${this._getEventNamespace()}`)).join(" "),e),t.on(`absoluteTransformChange.${this._getEventNamespace()}`,e),this._proxyDrag(t)})),this._resetTransformCache();return!!this.findOne(".top-left")&&this.update(),this}_proxyDrag(t){let e;t.on(`dragstart.${this._getEventNamespace()}`,(n=>{e=t.getAbsolutePosition(),this.isDragging()||t===this.findOne(".back")||this.startDrag(n,!1)})),t.on(`dragmove.${this._getEventNamespace()}`,(n=>{if(!e)return;const r=t.getAbsolutePosition(),i=r.x-e.x,a=r.y-e.y;this.nodes().forEach((e=>{if(e===t)return;if(e.isDragging())return;const r=e.getAbsolutePosition();e.setAbsolutePosition({x:r.x+i,y:r.y+a}),e.startDrag(n)})),e=null}))}getNodes(){return this._nodes||[]}getActiveAnchor(){return this._movingAnchorName}detach(){this._nodes&&this._nodes.forEach((t=>{t.off("."+this._getEventNamespace())})),this._nodes=[],this._resetTransformCache()}_resetTransformCache(){this._clearCache(d),this._clearCache("transform"),this._clearSelfAndDescendantCache("absoluteTransform")}_getNodeRect(){return this._getCache(d,this.__getNodeRect)}__getNodeShape(t,e=this.rotation(),n){const r=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),i=t.getAbsoluteScale(n),a=t.getAbsolutePosition(n),s=r.x*i.x-t.offsetX()*i.x,l=r.y*i.y-t.offsetY()*i.y,u=(o.Konva.getAngle(t.getAbsoluteRotation())+2*Math.PI)%(2*Math.PI);return v({x:a.x+s*Math.cos(u)+l*Math.sin(-u),y:a.y+l*Math.cos(u)+s*Math.sin(u),width:r.width*i.x,height:r.height*i.y,rotation:u},-o.Konva.getAngle(e),{x:0,y:0})}__getNodeRect(){if(!this.getNode())return{x:-1e8,y:-1e8,width:0,height:0,rotation:0};const e=[];this.nodes().map((t=>{const n=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),r=[{x:n.x,y:n.y},{x:n.x+n.width,y:n.y},{x:n.x+n.width,y:n.y+n.height},{x:n.x,y:n.y+n.height}],i=t.getAbsoluteTransform();r.forEach((function(t){const n=i.point(t);e.push(n)}))}));const n=new t.Transform;n.rotate(-o.Konva.getAngle(this.rotation()));let r=1/0,i=1/0,a=-1/0,s=-1/0;e.forEach((function(t){const e=n.point(t);void 0===r&&(r=a=e.x,i=s=e.y),r=Math.min(r,e.x),i=Math.min(i,e.y),a=Math.max(a,e.x),s=Math.max(s,e.y)})),n.invert();const l=n.point({x:r,y:i});return{x:l.x,y:l.y,width:a-r,height:s-i,rotation:o.Konva.getAngle(this.rotation())}}getX(){return this._getNodeRect().x}getY(){return this._getNodeRect().y}getWidth(){return this._getNodeRect().width}getHeight(){return this._getNodeRect().height}_createElements(){this._createBack(),y.forEach((t=>{this._createAnchor(t)})),this._createAnchor("rotater")}_createAnchor(e){const n=new i.Rect({stroke:"rgb(0, 161, 255)",fill:"white",strokeWidth:1,name:e+" _anchor",dragDistance:0,draggable:!0,hitStrokeWidth:m?10:"auto"}),r=this;n.on("mousedown touchstart",(function(t){r._handleMouseDown(t)})),n.on("dragstart",(t=>{n.stopDrag(),t.cancelBubble=!0})),n.on("dragend",(t=>{t.cancelBubble=!0})),n.on("mouseenter",(()=>{const r=o.Konva.getAngle(this.rotation()),i=this.rotateAnchorCursor(),a=function(e,n,r){if("rotater"===e)return r;n+=t.Util.degToRad(p[e]||0);const i=(t.Util.radToDeg(n)%360+360)%360;return t.Util._inRange(i,337.5,360)||t.Util._inRange(i,0,22.5)?"ns-resize":t.Util._inRange(i,22.5,67.5)?"nesw-resize":t.Util._inRange(i,67.5,112.5)?"ew-resize":t.Util._inRange(i,112.5,157.5)?"nwse-resize":t.Util._inRange(i,157.5,202.5)?"ns-resize":t.Util._inRange(i,202.5,247.5)?"nesw-resize":t.Util._inRange(i,247.5,292.5)?"ew-resize":t.Util._inRange(i,292.5,337.5)?"nwse-resize":(t.Util.error("Transformer has unknown angle for cursor detection: "+i),"pointer")}(e,r,i);n.getStage().content&&(n.getStage().content.style.cursor=a),this._cursorChange=!0})),n.on("mouseout",(()=>{n.getStage().content&&(n.getStage().content.style.cursor=""),this._cursorChange=!1})),this.add(n)}_createBack(){const e=new r.Shape({name:"back",width:0,height:0,draggable:!0,sceneFunc(e,n){const r=n.getParent(),i=r.padding();e.beginPath(),e.rect(-i,-i,n.width()+2*i,n.height()+2*i),e.moveTo(n.width()/2,-i),r.rotateEnabled()&&r.rotateLineVisible()&&e.lineTo(n.width()/2,-r.rotateAnchorOffset()*t.Util._sign(n.height())-i),e.fillStrokeShape(n)},hitFunc:(t,e)=>{if(!this.shouldOverdrawWholeArea())return;const n=this.padding();t.beginPath(),t.rect(-n,-n,e.width()+2*n,e.height()+2*n),t.fillStrokeShape(e)}});this.add(e),this._proxyDrag(e),e.on("dragstart",(t=>{t.cancelBubble=!0})),e.on("dragmove",(t=>{t.cancelBubble=!0})),e.on("dragend",(t=>{t.cancelBubble=!0})),this.on("dragmove",(t=>{this.update()}))}_handleMouseDown(t){if(this._transforming)return;this._movingAnchorName=t.target.name().split(" ")[0];const e=this._getNodeRect(),n=e.width,r=e.height,i=Math.sqrt(Math.pow(n,2)+Math.pow(r,2));this.sin=Math.abs(r/i),this.cos=Math.abs(n/i),"undefined"!=typeof window&&(window.addEventListener("mousemove",this._handleMouseMove),window.addEventListener("touchmove",this._handleMouseMove),window.addEventListener("mouseup",this._handleMouseUp,!0),window.addEventListener("touchend",this._handleMouseUp,!0)),this._transforming=!0;const a=t.target.getAbsolutePosition(),o=t.target.getStage().getPointerPosition();this._anchorDragOffset={x:o.x-a.x,y:o.y-a.y},x++,this._fire("transformstart",{evt:t.evt,target:this.getNode()}),this._nodes.forEach((e=>{e._fire("transformstart",{evt:t.evt,target:e})}))}_handleMouseMove(t){let e,n,r;const i=this.findOne("."+this._movingAnchorName),a=i.getStage();a.setPointersPositions(t);const s=a.getPointerPosition();let l={x:s.x-this._anchorDragOffset.x,y:s.y-this._anchorDragOffset.y};const u=i.getAbsolutePosition();this.anchorDragBoundFunc()&&(l=this.anchorDragBoundFunc()(u,l,t)),i.setAbsolutePosition(l);const c=i.getAbsolutePosition();if(u.x===c.x&&u.y===c.y)return;if("rotater"===this._movingAnchorName){const r=this._getNodeRect();e=i.x()-r.width/2,n=-i.y()+r.height/2;let a=Math.atan2(-n,e)+Math.PI/2;r.height<0&&(a-=Math.PI);const s=o.Konva.getAngle(this.rotation())+a,l=o.Konva.getAngle(this.rotationSnapTolerance()),u=function(t,e,n){let r=e;for(let i=0;i<t.length;i++){const a=o.Konva.getAngle(t[i]),s=Math.abs(a-e)%(2*Math.PI);Math.min(s,2*Math.PI-s)<n&&(r=a)}return r}(this.rotationSnaps(),s,l),c=S(r,u-r.rotation);return void this._fitNodesInto(c,t)}const h=this.shiftBehavior();let d;d="inverted"===h?this.keepRatio()&&!t.shiftKey:"none"===h?this.keepRatio():this.keepRatio()||t.shiftKey;var f=this.centeredScaling()||t.altKey;if("top-left"===this._movingAnchorName){if(d){var g=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-right").x(),y:this.findOne(".bottom-right").y()};r=Math.sqrt(Math.pow(g.x-i.x(),2)+Math.pow(g.y-i.y(),2));var p=this.findOne(".top-left").x()>g.x?-1:1,m=this.findOne(".top-left").y()>g.y?-1:1;e=r*this.cos*p,n=r*this.sin*m,this.findOne(".top-left").x(g.x-e),this.findOne(".top-left").y(g.y-n)}}else if("top-center"===this._movingAnchorName)this.findOne(".top-left").y(i.y());else if("top-right"===this._movingAnchorName){if(d){g=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-left").x(),y:this.findOne(".bottom-left").y()};r=Math.sqrt(Math.pow(i.x()-g.x,2)+Math.pow(g.y-i.y(),2));p=this.findOne(".top-right").x()<g.x?-1:1,m=this.findOne(".top-right").y()>g.y?-1:1;e=r*this.cos*p,n=r*this.sin*m,this.findOne(".top-right").x(g.x+e),this.findOne(".top-right").y(g.y-n)}var y=i.position();this.findOne(".top-left").y(y.y),this.findOne(".bottom-right").x(y.x)}else if("middle-left"===this._movingAnchorName)this.findOne(".top-left").x(i.x());else if("middle-right"===this._movingAnchorName)this.findOne(".bottom-right").x(i.x());else if("bottom-left"===this._movingAnchorName){if(d){g=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-right").x(),y:this.findOne(".top-right").y()};r=Math.sqrt(Math.pow(g.x-i.x(),2)+Math.pow(i.y()-g.y,2));p=g.x<i.x()?-1:1,m=i.y()<g.y?-1:1;e=r*this.cos*p,n=r*this.sin*m,i.x(g.x-e),i.y(g.y+n)}y=i.position(),this.findOne(".top-left").x(y.x),this.findOne(".bottom-right").y(y.y)}else if("bottom-center"===this._movingAnchorName)this.findOne(".bottom-right").y(i.y());else if("bottom-right"===this._movingAnchorName&&d){g=f?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-left").x(),y:this.findOne(".top-left").y()};r=Math.sqrt(Math.pow(i.x()-g.x,2)+Math.pow(i.y()-g.y,2));p=this.findOne(".bottom-right").x()<g.x?-1:1,m=this.findOne(".bottom-right").y()<g.y?-1:1;e=r*this.cos*p,n=r*this.sin*m,this.findOne(".bottom-right").x(g.x+e),this.findOne(".bottom-right").y(g.y+n)}if(f=this.centeredScaling()||t.altKey){const t=this.findOne(".top-left"),e=this.findOne(".bottom-right"),n=t.x(),r=t.y(),i=this.getWidth()-e.x(),a=this.getHeight()-e.y();e.move({x:-n,y:-r}),t.move({x:i,y:a})}const v=this.findOne(".top-left").getAbsolutePosition();e=v.x,n=v.y;const b=this.findOne(".bottom-right").x()-this.findOne(".top-left").x(),_=this.findOne(".bottom-right").y()-this.findOne(".top-left").y();this._fitNodesInto({x:e,y:n,width:b,height:_,rotation:o.Konva.getAngle(this.rotation())},t)}_handleMouseUp(t){this._removeEvents(t)}getAbsoluteTransform(){return this.getTransform()}_removeEvents(t){var e;if(this._transforming){this._transforming=!1,"undefined"!=typeof window&&(window.removeEventListener("mousemove",this._handleMouseMove),window.removeEventListener("touchmove",this._handleMouseMove),window.removeEventListener("mouseup",this._handleMouseUp,!0),window.removeEventListener("touchend",this._handleMouseUp,!0));const n=this.getNode();x--,this._fire("transformend",{evt:t,target:n}),null===(e=this.getLayer())||void 0===e||e.batchDraw(),n&&this._nodes.forEach((e=>{var n;e._fire("transformend",{evt:t,target:e}),null===(n=e.getLayer())||void 0===n||n.batchDraw()})),this._movingAnchorName=null}}_fitNodesInto(e,n){const r=this._getNodeRect();if(t.Util._inRange(e.width,2*-this.padding()-1,1))return void this.update();if(t.Util._inRange(e.height,2*-this.padding()-1,1))return void this.update();const i=new t.Transform;if(i.rotate(o.Konva.getAngle(this.rotation())),this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("left")>=0){const t=i.point({x:2*-this.padding(),y:0});e.x+=t.x,e.y+=t.y,e.width+=2*this.padding(),this._movingAnchorName=this._movingAnchorName.replace("left","right"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y}else if(this._movingAnchorName&&e.width<0&&this._movingAnchorName.indexOf("right")>=0){const t=i.point({x:2*this.padding(),y:0});this._movingAnchorName=this._movingAnchorName.replace("right","left"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.width+=2*this.padding()}if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("top")>=0){const t=i.point({x:0,y:2*-this.padding()});e.x+=t.x,e.y+=t.y,this._movingAnchorName=this._movingAnchorName.replace("top","bottom"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}else if(this._movingAnchorName&&e.height<0&&this._movingAnchorName.indexOf("bottom")>=0){const t=i.point({x:0,y:2*this.padding()});this._movingAnchorName=this._movingAnchorName.replace("bottom","top"),this._anchorDragOffset.x-=t.x,this._anchorDragOffset.y-=t.y,e.height+=2*this.padding()}if(this.boundBoxFunc()){const n=this.boundBoxFunc()(r,e);n?e=n:t.Util.warn("boundBoxFunc returned falsy. You should return new bound rect from it!")}const a=1e7,s=new t.Transform;s.translate(r.x,r.y),s.rotate(r.rotation),s.scale(r.width/a,r.height/a);const l=new t.Transform,u=e.width/a,c=e.height/a;!1===this.flipEnabled()?(l.translate(e.x,e.y),l.rotate(e.rotation),l.translate(e.width<0?e.width:0,e.height<0?e.height:0),l.scale(Math.abs(u),Math.abs(c))):(l.translate(e.x,e.y),l.rotate(e.rotation),l.scale(u,c));const h=l.multiply(s.invert());this._nodes.forEach((e=>{var n;const r=e.getParent().getAbsoluteTransform(),i=e.getTransform().copy();i.translate(e.offsetX(),e.offsetY());const a=new t.Transform;a.multiply(r.copy().invert()).multiply(h).multiply(r).multiply(i);const o=a.decompose();e.setAttrs(o),null===(n=e.getLayer())||void 0===n||n.batchDraw()})),this.rotation(t.Util._getRotation(e.rotation)),this._nodes.forEach((t=>{this._fire("transform",{evt:n,target:t}),t._fire("transform",{evt:n,target:t})})),this._resetTransformCache(),this.update(),this.getLayer().batchDraw()}forceUpdate(){this._resetTransformCache(),this.update()}_batchChangeChild(t,e){this.findOne(t).setAttrs(e)}update(){var e;const n=this._getNodeRect();this.rotation(t.Util._getRotation(n.rotation));const r=n.width,i=n.height,a=this.enabledAnchors(),o=this.resizeEnabled(),s=this.padding(),l=this.anchorSize(),u=this.find("._anchor");u.forEach((t=>{t.setAttrs({width:l,height:l,offsetX:l/2,offsetY:l/2,stroke:this.anchorStroke(),strokeWidth:this.anchorStrokeWidth(),fill:this.anchorFill(),cornerRadius:this.anchorCornerRadius()})})),this._batchChangeChild(".top-left",{x:0,y:0,offsetX:l/2+s,offsetY:l/2+s,visible:o&&a.indexOf("top-left")>=0}),this._batchChangeChild(".top-center",{x:r/2,y:0,offsetY:l/2+s,visible:o&&a.indexOf("top-center")>=0}),this._batchChangeChild(".top-right",{x:r,y:0,offsetX:l/2-s,offsetY:l/2+s,visible:o&&a.indexOf("top-right")>=0}),this._batchChangeChild(".middle-left",{x:0,y:i/2,offsetX:l/2+s,visible:o&&a.indexOf("middle-left")>=0}),this._batchChangeChild(".middle-right",{x:r,y:i/2,offsetX:l/2-s,visible:o&&a.indexOf("middle-right")>=0}),this._batchChangeChild(".bottom-left",{x:0,y:i,offsetX:l/2+s,offsetY:l/2-s,visible:o&&a.indexOf("bottom-left")>=0}),this._batchChangeChild(".bottom-center",{x:r/2,y:i,offsetY:l/2-s,visible:o&&a.indexOf("bottom-center")>=0}),this._batchChangeChild(".bottom-right",{x:r,y:i,offsetX:l/2-s,offsetY:l/2-s,visible:o&&a.indexOf("bottom-right")>=0}),this._batchChangeChild(".rotater",{x:r/2,y:-this.rotateAnchorOffset()*t.Util._sign(i)-s,visible:this.rotateEnabled()}),this._batchChangeChild(".back",{width:r,height:i,visible:this.borderEnabled(),stroke:this.borderStroke(),strokeWidth:this.borderStrokeWidth(),dash:this.borderDash(),x:0,y:0});const c=this.anchorStyleFunc();c&&u.forEach((t=>{c(t)})),null===(e=this.getLayer())||void 0===e||e.batchDraw()}isTransforming(){return this._transforming}stopTransform(){if(this._transforming){this._removeEvents();const t=this.findOne("."+this._movingAnchorName);t&&t.stopDrag()}}destroy(){return this.getStage()&&this._cursorChange&&this.getStage().content&&(this.getStage().content.style.cursor=""),a.Group.prototype.destroy.call(this),this.detach(),this._removeEvents(),this}toObject(){return n.Node.prototype.toObject.call(this)}clone(t){return n.Node.prototype.clone.call(this,t)}getClientRect(){return this.nodes().length>0?super.getClientRect():{x:0,y:0,width:0,height:0}}}return Bt.Transformer=w,w.isTransforming=()=>x>0,w.prototype.className="Transformer",(0,l._registerNode)(w),e.Factory.addGetterSetter(w,"enabledAnchors",y,(function(e){return e instanceof Array||t.Util.warn("enabledAnchors value should be an array"),e instanceof Array&&e.forEach((function(e){-1===y.indexOf(e)&&t.Util.warn("Unknown anchor name: "+e+". Available names are: "+y.join(", "))})),e||[]})),e.Factory.addGetterSetter(w,"flipEnabled",!0,(0,s.getBooleanValidator)()),e.Factory.addGetterSetter(w,"resizeEnabled",!0),e.Factory.addGetterSetter(w,"anchorSize",10,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(w,"rotateEnabled",!0),e.Factory.addGetterSetter(w,"rotateLineVisible",!0),e.Factory.addGetterSetter(w,"rotationSnaps",[]),e.Factory.addGetterSetter(w,"rotateAnchorOffset",50,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(w,"rotateAnchorCursor","crosshair"),e.Factory.addGetterSetter(w,"rotationSnapTolerance",5,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(w,"borderEnabled",!0),e.Factory.addGetterSetter(w,"anchorStroke","rgb(0, 161, 255)"),e.Factory.addGetterSetter(w,"anchorStrokeWidth",1,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(w,"anchorFill","white"),e.Factory.addGetterSetter(w,"anchorCornerRadius",0,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(w,"borderStroke","rgb(0, 161, 255)"),e.Factory.addGetterSetter(w,"borderStrokeWidth",1,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(w,"borderDash"),e.Factory.addGetterSetter(w,"keepRatio",!0),e.Factory.addGetterSetter(w,"shiftBehavior","default"),e.Factory.addGetterSetter(w,"centeredScaling",!1),e.Factory.addGetterSetter(w,"ignoreStroke",!1),e.Factory.addGetterSetter(w,"padding",0,(0,s.getNumberValidator)()),e.Factory.addGetterSetter(w,"nodes"),e.Factory.addGetterSetter(w,"node"),e.Factory.addGetterSetter(w,"boundBoxFunc"),e.Factory.addGetterSetter(w,"anchorDragBoundFunc"),e.Factory.addGetterSetter(w,"anchorStyleFunc"),e.Factory.addGetterSetter(w,"shouldOverdrawWholeArea",!1),e.Factory.addGetterSetter(w,"useSingleNodeRotation",!0),e.Factory.backCompat(w,{lineEnabled:"borderEnabled",rotateHandlerOffset:"rotateAnchorOffset",enabledHandlers:"enabledAnchors"}),Bt}var Vt,jt={};var Wt,Kt={};function qt(){if(Wt)return Kt;Wt=1,Object.defineProperty(Kt,"__esModule",{value:!0}),Kt.Blur=void 0;const t=_(),e=M(),n=b();function r(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}const i=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],a=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];return Kt.Blur=function(t){const e=Math.round(this.blurRadius());e>0&&function(t,e){const n=t.data,o=t.width,s=t.height;let l,u,c,h,d,f,g,p,m,y,v,b,_,S,x,w,C,k,P,T,E,F,N,M;const A=e+e+1,R=o-1,L=s-1,D=e+1,z=D*(D+1)/2,I=new r,G=i[e],O=a[e];let U=null,B=I,H=null,V=null;for(c=1;c<A;c++)B=B.next=new r,c===D&&(U=B);for(B.next=I,g=f=0,u=0;u<s;u++){for(w=C=k=P=p=m=y=v=0,b=D*(T=n[f]),_=D*(E=n[f+1]),S=D*(F=n[f+2]),x=D*(N=n[f+3]),p+=z*T,m+=z*E,y+=z*F,v+=z*N,B=I,c=0;c<D;c++)B.r=T,B.g=E,B.b=F,B.a=N,B=B.next;for(c=1;c<D;c++)h=f+((R<c?R:c)<<2),p+=(B.r=T=n[h])*(M=D-c),m+=(B.g=E=n[h+1])*M,y+=(B.b=F=n[h+2])*M,v+=(B.a=N=n[h+3])*M,w+=T,C+=E,k+=F,P+=N,B=B.next;for(H=I,V=U,l=0;l<o;l++)n[f+3]=N=v*G>>O,0!==N?(N=255/N,n[f]=(p*G>>O)*N,n[f+1]=(m*G>>O)*N,n[f+2]=(y*G>>O)*N):n[f]=n[f+1]=n[f+2]=0,p-=b,m-=_,y-=S,v-=x,b-=H.r,_-=H.g,S-=H.b,x-=H.a,h=g+((h=l+e+1)<R?h:R)<<2,w+=H.r=n[h],C+=H.g=n[h+1],k+=H.b=n[h+2],P+=H.a=n[h+3],p+=w,m+=C,y+=k,v+=P,H=H.next,b+=T=V.r,_+=E=V.g,S+=F=V.b,x+=N=V.a,w-=T,C-=E,k-=F,P-=N,V=V.next,f+=4;g+=o}for(l=0;l<o;l++){for(C=k=P=w=m=y=v=p=0,f=l<<2,b=D*(T=n[f]),_=D*(E=n[f+1]),S=D*(F=n[f+2]),x=D*(N=n[f+3]),p+=z*T,m+=z*E,y+=z*F,v+=z*N,B=I,c=0;c<D;c++)B.r=T,B.g=E,B.b=F,B.a=N,B=B.next;for(d=o,c=1;c<=e;c++)f=d+l<<2,p+=(B.r=T=n[f])*(M=D-c),m+=(B.g=E=n[f+1])*M,y+=(B.b=F=n[f+2])*M,v+=(B.a=N=n[f+3])*M,w+=T,C+=E,k+=F,P+=N,B=B.next,c<L&&(d+=o);for(f=l,H=I,V=U,u=0;u<s;u++)h=f<<2,n[h+3]=N=v*G>>O,N>0?(N=255/N,n[h]=(p*G>>O)*N,n[h+1]=(m*G>>O)*N,n[h+2]=(y*G>>O)*N):n[h]=n[h+1]=n[h+2]=0,p-=b,m-=_,y-=S,v-=x,b-=H.r,_-=H.g,S-=H.b,x-=H.a,h=l+((h=u+D)<L?h:L)*o<<2,p+=w+=H.r=n[h],m+=C+=H.g=n[h+1],y+=k+=H.b=n[h+2],v+=P+=H.a=n[h+3],H=H.next,b+=T=V.r,_+=E=V.g,S+=F=V.b,x+=N=V.a,w-=T,C-=E,k-=F,P-=N,V=V.next,f+=o}}(t,e)},t.Factory.addGetterSetter(e.Node,"blurRadius",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Kt}var Yt,Xt={};var Qt,$t={};var Jt,Zt={};var te,ee={};var ne,re={};var ie,ae={};var oe,se={};var le,ue={};var ce,he={};function de(){if(ce)return he;ce=1,Object.defineProperty(he,"__esModule",{value:!0}),he.Kaleidoscope=void 0;const t=_(),e=M(),n=f(),r=b();return he.Kaleidoscope=function(t){const e=t.width,r=t.height;let i,a,o,s,l,u,c,h,d,f,g=Math.round(this.kaleidoscopePower());const p=Math.round(this.kaleidoscopeAngle()),m=Math.floor(e*(p%360)/360);if(g<1)return;const y=n.Util.createCanvasElement();y.width=e,y.height=r;const v=y.getContext("2d").getImageData(0,0,e,r);n.Util.releaseCanvas(y),function(t,e,n){const r=t.data,i=e.data,a=t.width,o=t.height,s=n.polarCenterX||a/2,l=n.polarCenterY||o/2;let u=Math.sqrt(s*s+l*l),c=a-s,h=o-l;const d=Math.sqrt(c*c+h*h);u=d>u?d:u;const f=o,g=a,p=360/g*Math.PI/180;for(let m=0;m<g;m+=1){const t=Math.sin(m*p),e=Math.cos(m*p);for(let n=0;n<f;n+=1){c=Math.floor(s+u*n/f*e),h=Math.floor(l+u*n/f*t);let o=4*(h*a+c);const d=r[o+0],g=r[o+1],p=r[o+2],y=r[o+3];o=4*(m+n*a),i[o+0]=d,i[o+1]=g,i[o+2]=p,i[o+3]=y}}}(t,v,{polarCenterX:e/2,polarCenterY:r/2});let b=e/Math.pow(2,g);for(;b<=8;)b*=2,g-=1;b=Math.ceil(b);let _=b,S=0,x=_,w=1;for(m+b>e&&(S=_,x=0,w=-1),a=0;a<r;a+=1)for(i=S;i!==x;i+=w)o=Math.round(i+m)%e,d=4*(e*a+o),l=v.data[d+0],u=v.data[d+1],c=v.data[d+2],h=v.data[d+3],f=4*(e*a+i),v.data[f+0]=l,v.data[f+1]=u,v.data[f+2]=c,v.data[f+3]=h;for(a=0;a<r;a+=1)for(_=Math.floor(b),s=0;s<g;s+=1){for(i=0;i<_+1;i+=1)d=4*(e*a+i),l=v.data[d+0],u=v.data[d+1],c=v.data[d+2],h=v.data[d+3],f=4*(e*a+2*_-i-1),v.data[f+0]=l,v.data[f+1]=u,v.data[f+2]=c,v.data[f+3]=h;_*=2}!function(t,e,n){const r=t.data,i=e.data,a=t.width,o=t.height,s=n.polarCenterX||a/2,l=n.polarCenterY||o/2;let u=Math.sqrt(s*s+l*l),c=a-s,h=o-l;const d=Math.sqrt(c*c+h*h);u=d>u?d:u;const f=o,g=a;let p,m;for(c=0;c<a;c+=1)for(h=0;h<o;h+=1){const t=c-s,e=h-l,n=Math.sqrt(t*t+e*e)*f/u;let o=(180*Math.atan2(e,t)/Math.PI+360+0)%360;o=o*g/360,p=Math.floor(o),m=Math.floor(n);let d=4*(m*a+p);const y=r[d+0],v=r[d+1],b=r[d+2],_=r[d+3];d=4*(h*a+c),i[d+0]=y,i[d+1]=v,i[d+2]=b,i[d+3]=_}}(v,t,{})},t.Factory.addGetterSetter(e.Node,"kaleidoscopePower",2,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"kaleidoscopeAngle",0,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),he}var fe,ge={};function pe(){if(fe)return ge;fe=1,Object.defineProperty(ge,"__esModule",{value:!0}),ge.Mask=void 0;const t=_(),e=M(),n=b();function r(t,e,n){let r=4*(n*t.width+e);const i=[];return i.push(t.data[r++],t.data[r++],t.data[r++],t.data[r++]),i}function i(t,e){return Math.sqrt(Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)+Math.pow(t[2]-e[2],2))}return ge.Mask=function(t){let e=function(t,e){const n=r(t,0,0),a=r(t,t.width-1,0),o=r(t,0,t.height-1),s=r(t,t.width-1,t.height-1),l=e||10;if(i(n,a)<l&&i(a,s)<l&&i(s,o)<l&&i(o,n)<l){const e=function(t){const e=[0,0,0];for(let n=0;n<t.length;n++)e[0]+=t[n][0],e[1]+=t[n][1],e[2]+=t[n][2];return e[0]/=t.length,e[1]/=t.length,e[2]/=t.length,e}([a,n,s,o]),r=[];for(let n=0;n<t.width*t.height;n++){const a=i(e,[t.data[4*n],t.data[4*n+1],t.data[4*n+2]]);r[n]=a<l?0:255}return r}}(t,this.threshold());return e&&(e=function(t,e,n){const r=[1,1,1,1,0,1,1,1,1],i=Math.round(Math.sqrt(r.length)),a=Math.floor(i/2),o=[];for(let s=0;s<n;s++)for(let l=0;l<e;l++){const u=s*e+l;let c=0;for(let o=0;o<i;o++)for(let u=0;u<i;u++){const h=s+o-a,d=l+u-a;if(h>=0&&h<n&&d>=0&&d<e){const n=r[o*i+u];c+=t[h*e+d]*n}}o[u]=2040===c?255:0}return o}(e,t.width,t.height),e=function(t,e,n){const r=[1,1,1,1,1,1,1,1,1],i=Math.round(Math.sqrt(r.length)),a=Math.floor(i/2),o=[];for(let s=0;s<n;s++)for(let l=0;l<e;l++){const u=s*e+l;let c=0;for(let o=0;o<i;o++)for(let u=0;u<i;u++){const h=s+o-a,d=l+u-a;if(h>=0&&h<n&&d>=0&&d<e){const n=r[o*i+u];c+=t[h*e+d]*n}}o[u]=c>=1020?255:0}return o}(e,t.width,t.height),e=function(t,e,n){const r=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],i=Math.round(Math.sqrt(r.length)),a=Math.floor(i/2),o=[];for(let s=0;s<n;s++)for(let l=0;l<e;l++){const u=s*e+l;let c=0;for(let o=0;o<i;o++)for(let u=0;u<i;u++){const h=s+o-a,d=l+u-a;if(h>=0&&h<n&&d>=0&&d<e){const n=r[o*i+u];c+=t[h*e+d]*n}}o[u]=c}return o}(e,t.width,t.height),function(t,e){for(let n=0;n<t.width*t.height;n++)t.data[4*n+3]=e[n]}(t,e)),t},t.Factory.addGetterSetter(e.Node,"threshold",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),ge}var me,ye={};var ve,be={};var _e,Se={};var xe,we={};var Ce,ke={};var Pe,Te={};var Ee,Fe={};var Ne,Me,Ae={};function Re(){if(Me)return s;Me=1,Object.defineProperty(s,"__esModule",{value:!0}),s.Konva=void 0;const t=rt(),e=function(){if(it)return at;it=1,Object.defineProperty(at,"__esModule",{value:!0}),at.Arc=void 0;const t=_(),e=j(),n=c(),r=b(),i=c();let a=class extends e.Shape{_sceneFunc(t){const e=n.Konva.getAngle(this.angle()),r=this.clockwise();t.beginPath(),t.arc(0,0,this.outerRadius(),0,e,r),t.arc(0,0,this.innerRadius(),e,0,!r),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}getSelfRect(){const t=this.innerRadius(),e=this.outerRadius(),r=this.clockwise(),i=n.Konva.getAngle(r?360-this.angle():this.angle()),a=Math.cos(Math.min(i,Math.PI)),o=Math.sin(Math.min(Math.max(Math.PI,i),3*Math.PI/2)),s=Math.sin(Math.min(i,Math.PI/2)),l=a*(a>0?t:e),u=o*(o>0?t:e),c=s*(s>0?e:t);return{x:l,y:r?-1*c:u,width:1*e-l,height:c-u}}};return at.Arc=a,a.prototype._centroid=!0,a.prototype.className="Arc",a.prototype._attrsAffectingSize=["innerRadius","outerRadius","angle","clockwise"],(0,i._registerNode)(a),t.Factory.addGetterSetter(a,"innerRadius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(a,"outerRadius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(a,"angle",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(a,"clockwise",!1,(0,r.getBooleanValidator)()),at}(),n=function(){if(dt)return st;dt=1,Object.defineProperty(st,"__esModule",{value:!0}),st.Arrow=void 0;const t=_(),e=ut(),n=b(),r=c(),i=pt();let a=class extends e.Line{_sceneFunc(t){super._sceneFunc(t);const e=2*Math.PI,n=this.points();let r=n;const a=0!==this.tension()&&n.length>4;a&&(r=this.getTensionPoints());const o=this.pointerLength(),s=n.length;let l,u;if(a){const t=[r[r.length-4],r[r.length-3],r[r.length-2],r[r.length-1],n[s-2],n[s-1]],e=i.Path.calcLength(r[r.length-4],r[r.length-3],"C",t),a=i.Path.getPointOnQuadraticBezier(Math.min(1,1-o/e),t[0],t[1],t[2],t[3],t[4],t[5]);l=n[s-2]-a.x,u=n[s-1]-a.y}else l=n[s-2]-n[s-4],u=n[s-1]-n[s-3];const c=(Math.atan2(u,l)+e)%e,h=this.pointerWidth();this.pointerAtEnding()&&(t.save(),t.beginPath(),t.translate(n[s-2],n[s-1]),t.rotate(c),t.moveTo(0,0),t.lineTo(-o,h/2),t.lineTo(-o,-h/2),t.closePath(),t.restore(),this.__fillStroke(t)),this.pointerAtBeginning()&&(t.save(),t.beginPath(),t.translate(n[0],n[1]),a?(l=(r[0]+r[2])/2-n[0],u=(r[1]+r[3])/2-n[1]):(l=n[2]-n[0],u=n[3]-n[1]),t.rotate((Math.atan2(-u,-l)+e)%e),t.moveTo(0,0),t.lineTo(-o,h/2),t.lineTo(-o,-h/2),t.closePath(),t.restore(),this.__fillStroke(t))}__fillStroke(t){const e=this.dashEnabled();e&&(this.attrs.dashEnabled=!1,t.setLineDash([])),t.fillStrokeShape(this),e&&(this.attrs.dashEnabled=!0)}getSelfRect(){const t=super.getSelfRect(),e=this.pointerWidth()/2;return{x:t.x,y:t.y-e,width:t.width,height:t.height+2*e}}};return st.Arrow=a,a.prototype.className="Arrow",(0,r._registerNode)(a),t.Factory.addGetterSetter(a,"pointerLength",10,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(a,"pointerWidth",10,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(a,"pointerAtBeginning",!1),t.Factory.addGetterSetter(a,"pointerAtEnding",!0),st}(),r=function(){if(mt)return yt;mt=1,Object.defineProperty(yt,"__esModule",{value:!0}),yt.Circle=void 0;const t=_(),e=j(),n=b(),r=c();let i=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.attrs.radius||0,0,2*Math.PI,!1),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius()!==t/2&&this.radius(t/2)}setHeight(t){this.radius()!==t/2&&this.radius(t/2)}};return yt.Circle=i,i.prototype._centroid=!0,i.prototype.className="Circle",i.prototype._attrsAffectingSize=["radius"],(0,r._registerNode)(i),t.Factory.addGetterSetter(i,"radius",0,(0,n.getNumberValidator)()),yt}(),i=function(){if(vt)return bt;vt=1,Object.defineProperty(bt,"__esModule",{value:!0}),bt.Ellipse=void 0;const t=_(),e=j(),n=b(),r=c();let i=class extends e.Shape{_sceneFunc(t){const e=this.radiusX(),n=this.radiusY();t.beginPath(),t.save(),e!==n&&t.scale(1,n/e),t.arc(0,0,e,0,2*Math.PI,!1),t.restore(),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radiusX()}getHeight(){return 2*this.radiusY()}setWidth(t){this.radiusX(t/2)}setHeight(t){this.radiusY(t/2)}};return bt.Ellipse=i,i.prototype.className="Ellipse",i.prototype._centroid=!0,i.prototype._attrsAffectingSize=["radiusX","radiusY"],(0,r._registerNode)(i),t.Factory.addComponentsGetterSetter(i,"radius",["x","y"]),t.Factory.addGetterSetter(i,"radiusX",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"radiusY",0,(0,n.getNumberValidator)()),bt}(),a=function(){if(_t)return St;_t=1,Object.defineProperty(St,"__esModule",{value:!0}),St.Image=void 0;const t=f(),e=_(),n=j(),r=c(),i=b();class a extends n.Shape{constructor(t){super(t),this._loadListener=()=>{this._requestDraw()},this.on("imageChange.konva",(t=>{this._removeImageLoad(t.oldVal),this._setImageLoad()})),this._setImageLoad()}_setImageLoad(){const t=this.image();t&&t.complete||t&&4===t.readyState||t&&t.addEventListener&&t.addEventListener("load",this._loadListener)}_removeImageLoad(t){t&&t.removeEventListener&&t.removeEventListener("load",this._loadListener)}destroy(){return this._removeImageLoad(this.image()),super.destroy(),this}_useBufferCanvas(){const t=!!this.cornerRadius(),e=this.hasShadow();return!(!t||!e)||super._useBufferCanvas(!0)}_sceneFunc(e){const n=this.getWidth(),r=this.getHeight(),i=this.cornerRadius(),a=this.attrs.image;let o;if(a){const t=this.attrs.cropWidth,e=this.attrs.cropHeight;o=t&&e?[a,this.cropX(),this.cropY(),t,e,0,0,n,r]:[a,0,0,n,r]}(this.hasFill()||this.hasStroke()||i)&&(e.beginPath(),i?t.Util.drawRoundedRectPath(e,n,r,i):e.rect(0,0,n,r),e.closePath(),e.fillStrokeShape(this)),a&&(i&&e.clip(),e.drawImage.apply(e,o))}_hitFunc(e){const n=this.width(),r=this.height(),i=this.cornerRadius();e.beginPath(),i?t.Util.drawRoundedRectPath(e,n,r,i):e.rect(0,0,n,r),e.closePath(),e.fillStrokeShape(this)}getWidth(){var t,e;return null!==(t=this.attrs.width)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.width}getHeight(){var t,e;return null!==(t=this.attrs.height)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.height}static fromURL(e,n,r=null){const i=t.Util.createImageElement();i.onload=function(){const t=new a({image:i});n(t)},i.onerror=r,i.crossOrigin="Anonymous",i.src=e}}return St.Image=a,a.prototype.className="Image",(0,r._registerNode)(a),e.Factory.addGetterSetter(a,"cornerRadius",0,(0,i.getNumberOrArrayOfNumbersValidator)(4)),e.Factory.addGetterSetter(a,"image"),e.Factory.addComponentsGetterSetter(a,"crop",["x","y","width","height"]),e.Factory.addGetterSetter(a,"cropX",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(a,"cropY",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(a,"cropWidth",0,(0,i.getNumberValidator)()),e.Factory.addGetterSetter(a,"cropHeight",0,(0,i.getNumberValidator)()),St}(),o=function(){if(xt)return wt;xt=1,Object.defineProperty(wt,"__esModule",{value:!0}),wt.Tag=wt.Label=void 0;const t=_(),e=j(),n=Q(),r=b(),i=c(),a=["fontFamily","fontSize","fontStyle","padding","lineHeight","text","width","height","pointerDirection","pointerWidth","pointerHeight"],o="up",s="right",l="down",u="left",h=a.length;let d=class extends n.Group{constructor(t){super(t),this.on("add.konva",(function(t){this._addListeners(t.child),this._sync()}))}getText(){return this.find("Text")[0]}getTag(){return this.find("Tag")[0]}_addListeners(t){let e,n=this;const r=function(){n._sync()};for(e=0;e<h;e++)t.on(a[e]+"Change.konva",r)}getWidth(){return this.getText().width()}getHeight(){return this.getText().height()}_sync(){let t,e,n,r,i,a,c,h=this.getText(),d=this.getTag();if(h&&d){switch(t=h.width(),e=h.height(),n=d.pointerDirection(),r=d.pointerWidth(),c=d.pointerHeight(),i=0,a=0,n){case o:i=t/2,a=-1*c;break;case s:i=t+r,a=e/2;break;case l:i=t/2,a=e+c;break;case u:i=-1*r,a=e/2}d.setAttrs({x:-1*i,y:-1*a,width:t,height:e}),h.setAttrs({x:-1*i,y:-1*a})}}};wt.Label=d,d.prototype.className="Label",(0,i._registerNode)(d);class f extends e.Shape{_sceneFunc(t){const e=this.width(),n=this.height(),r=this.pointerDirection(),i=this.pointerWidth(),a=this.pointerHeight(),c=this.cornerRadius();let h=0,d=0,f=0,g=0;"number"==typeof c?h=d=f=g=Math.min(c,e/2,n/2):(h=Math.min(c[0]||0,e/2,n/2),d=Math.min(c[1]||0,e/2,n/2),g=Math.min(c[2]||0,e/2,n/2),f=Math.min(c[3]||0,e/2,n/2)),t.beginPath(),t.moveTo(h,0),r===o&&(t.lineTo((e-i)/2,0),t.lineTo(e/2,-1*a),t.lineTo((e+i)/2,0)),t.lineTo(e-d,0),t.arc(e-d,d,d,3*Math.PI/2,0,!1),r===s&&(t.lineTo(e,(n-a)/2),t.lineTo(e+i,n/2),t.lineTo(e,(n+a)/2)),t.lineTo(e,n-g),t.arc(e-g,n-g,g,0,Math.PI/2,!1),r===l&&(t.lineTo((e+i)/2,n),t.lineTo(e/2,n+a),t.lineTo((e-i)/2,n)),t.lineTo(f,n),t.arc(f,n-f,f,Math.PI/2,Math.PI,!1),r===u&&(t.lineTo(0,(n+a)/2),t.lineTo(-1*i,n/2),t.lineTo(0,(n-a)/2)),t.lineTo(0,h),t.arc(h,h,h,Math.PI,3*Math.PI/2,!1),t.closePath(),t.fillStrokeShape(this)}getSelfRect(){let t=0,e=0,n=this.pointerWidth(),r=this.pointerHeight(),i=this.pointerDirection(),a=this.width(),c=this.height();return i===o?(e-=r,c+=r):i===l?c+=r:i===u?(t-=1.5*n,a+=n):i===s&&(a+=1.5*n),{x:t,y:e,width:a,height:c}}}return wt.Tag=f,f.prototype.className="Tag",(0,i._registerNode)(f),t.Factory.addGetterSetter(f,"pointerDirection","none"),t.Factory.addGetterSetter(f,"pointerWidth",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(f,"pointerHeight",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(f,"cornerRadius",0,(0,r.getNumberOrArrayOfNumbersValidator)(4)),wt}(),l=ut(),u=pt(),h=Pt(),d=function(){if(Tt)return Et;Tt=1,Object.defineProperty(Et,"__esModule",{value:!0}),Et.RegularPolygon=void 0;const t=_(),e=j(),n=b(),r=c();let i=class extends e.Shape{_sceneFunc(t){const e=this._getPoints();t.beginPath(),t.moveTo(e[0].x,e[0].y);for(let n=1;n<e.length;n++)t.lineTo(e[n].x,e[n].y);t.closePath(),t.fillStrokeShape(this)}_getPoints(){const t=this.attrs.sides,e=this.attrs.radius||0,n=[];for(let r=0;r<t;r++)n.push({x:e*Math.sin(2*r*Math.PI/t),y:-1*e*Math.cos(2*r*Math.PI/t)});return n}getSelfRect(){const t=this._getPoints();let e=t[0].x,n=t[0].y,r=t[0].x,i=t[0].y;return t.forEach((t=>{e=Math.min(e,t.x),n=Math.max(n,t.x),r=Math.min(r,t.y),i=Math.max(i,t.y)})),{x:e,y:r,width:n-e,height:i-r}}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}};return Et.RegularPolygon=i,i.prototype.className="RegularPolygon",i.prototype._centroid=!0,i.prototype._attrsAffectingSize=["radius"],(0,r._registerNode)(i),t.Factory.addGetterSetter(i,"radius",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"sides",0,(0,n.getNumberValidator)()),Et}(),g=function(){if(Ft)return Nt;Ft=1,Object.defineProperty(Nt,"__esModule",{value:!0}),Nt.Ring=void 0;const t=_(),e=j(),n=b(),r=c(),i=2*Math.PI;let a=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.innerRadius(),0,i,!1),t.moveTo(this.outerRadius(),0),t.arc(0,0,this.outerRadius(),i,0,!0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}};return Nt.Ring=a,a.prototype.className="Ring",a.prototype._centroid=!0,a.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,r._registerNode)(a),t.Factory.addGetterSetter(a,"innerRadius",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(a,"outerRadius",0,(0,n.getNumberValidator)()),Nt}(),p=function(){if(Mt)return At;Mt=1,Object.defineProperty(At,"__esModule",{value:!0}),At.Sprite=void 0;const t=_(),e=j(),n=Z(),r=b(),i=c();let a=class extends e.Shape{constructor(t){super(t),this._updated=!0,this.anim=new n.Animation((()=>{const t=this._updated;return this._updated=!1,t})),this.on("animationChange.konva",(function(){this.frameIndex(0)})),this.on("frameIndexChange.konva",(function(){this._updated=!0})),this.on("frameRateChange.konva",(function(){this.anim.isRunning()&&(clearInterval(this.interval),this._setInterval())}))}_sceneFunc(t){const e=this.animation(),n=this.frameIndex(),r=4*n,i=this.animations()[e],a=this.frameOffsets(),o=i[r+0],s=i[r+1],l=i[r+2],u=i[r+3],c=this.image();if((this.hasFill()||this.hasStroke())&&(t.beginPath(),t.rect(0,0,l,u),t.closePath(),t.fillStrokeShape(this)),c)if(a){const r=a[e],i=2*n;t.drawImage(c,o,s,l,u,r[i+0],r[i+1],l,u)}else t.drawImage(c,o,s,l,u,0,0,l,u)}_hitFunc(t){const e=this.animation(),n=this.frameIndex(),r=4*n,i=this.animations()[e],a=this.frameOffsets(),o=i[r+2],s=i[r+3];if(t.beginPath(),a){const r=a[e],i=2*n;t.rect(r[i+0],r[i+1],o,s)}else t.rect(0,0,o,s);t.closePath(),t.fillShape(this)}_useBufferCanvas(){return super._useBufferCanvas(!0)}_setInterval(){const t=this;this.interval=setInterval((function(){t._updateIndex()}),1e3/this.frameRate())}start(){if(this.isRunning())return;const t=this.getLayer();this.anim.setLayers(t),this._setInterval(),this.anim.start()}stop(){this.anim.stop(),clearInterval(this.interval)}isRunning(){return this.anim.isRunning()}_updateIndex(){const t=this.frameIndex(),e=this.animation();t<this.animations()[e].length/4-1?this.frameIndex(t+1):this.frameIndex(0)}};return At.Sprite=a,a.prototype.className="Sprite",(0,i._registerNode)(a),t.Factory.addGetterSetter(a,"animation"),t.Factory.addGetterSetter(a,"animations"),t.Factory.addGetterSetter(a,"frameOffsets"),t.Factory.addGetterSetter(a,"image"),t.Factory.addGetterSetter(a,"frameIndex",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(a,"frameRate",17,(0,r.getNumberValidator)()),t.Factory.backCompat(a,{index:"frameIndex",getIndex:"getFrameIndex",setIndex:"setFrameIndex"}),At}(),m=function(){if(Rt)return Lt;Rt=1,Object.defineProperty(Lt,"__esModule",{value:!0}),Lt.Star=void 0;const t=_(),e=j(),n=b(),r=c();let i=class extends e.Shape{_sceneFunc(t){const e=this.innerRadius(),n=this.outerRadius(),r=this.numPoints();t.beginPath(),t.moveTo(0,0-n);for(let i=1;i<2*r;i++){const a=i%2==0?n:e,o=a*Math.sin(i*Math.PI/r),s=-1*a*Math.cos(i*Math.PI/r);t.lineTo(o,s)}t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}};return Lt.Star=i,i.prototype.className="Star",i.prototype._centroid=!0,i.prototype._attrsAffectingSize=["innerRadius","outerRadius"],(0,r._registerNode)(i),t.Factory.addGetterSetter(i,"numPoints",5,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"innerRadius",0,(0,n.getNumberValidator)()),t.Factory.addGetterSetter(i,"outerRadius",0,(0,n.getNumberValidator)()),Lt}(),y=It(),v=function(){if(Gt)return Ot;Gt=1,Object.defineProperty(Ot,"__esModule",{value:!0}),Ot.TextPath=void 0;const t=f(),e=_(),n=j(),r=pt(),i=It(),a=b(),o=c(),s="normal";function l(t){t.fillText(this.partialText,0,0)}function u(t){t.strokeText(this.partialText,0,0)}let h=class extends n.Shape{constructor(e){super(e),this.dummyCanvas=t.Util.createCanvasElement(),this.dataArray=[],this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute(),this._setTextData()})),this.on("textChange.konva alignChange.konva letterSpacingChange.konva kerningFuncChange.konva fontSizeChange.konva fontFamilyChange.konva",this._setTextData),this._setTextData()}_getTextPathLength(){return r.Path.getPathLength(this.dataArray)}_getPointAtLength(t){return this.attrs.data?t-1>this.pathLength?null:r.Path.getPointAtLengthOfDataArray(t,this.dataArray):null}_readDataAttribute(){this.dataArray=r.Path.parsePathData(this.attrs.data),this.pathLength=this._getTextPathLength()}_sceneFunc(t){t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",this.textBaseline()),t.setAttr("textAlign","left"),t.save();const e=this.textDecoration(),n=this.fill(),r=this.fontSize(),i=this.glyphInfo;"underline"===e&&t.beginPath();for(let a=0;a<i.length;a++){t.save();const n=i[a].p0;t.translate(n.x,n.y),t.rotate(i[a].rotation),this.partialText=i[a].text,t.fillStrokeShape(this),"underline"===e&&(0===a&&t.moveTo(0,r/2+1),t.lineTo(r,r/2+1)),t.restore()}"underline"===e&&(t.strokeStyle=n,t.lineWidth=r/20,t.stroke()),t.restore()}_hitFunc(t){t.beginPath();const e=this.glyphInfo;if(e.length>=1){const n=e[0].p0;t.moveTo(n.x,n.y)}for(let n=0;n<e.length;n++){const r=e[n].p1;t.lineTo(r.x,r.y)}t.setAttr("lineWidth",this.fontSize()),t.setAttr("strokeStyle",this.colorKey),t.stroke()}getTextWidth(){return this.textWidth}getTextHeight(){return t.Util.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}setText(t){return i.Text.prototype.setText.call(this,t)}_getContextFont(){return i.Text.prototype._getContextFont.call(this)}_getTextSize(t){const e=this.dummyCanvas.getContext("2d");e.save(),e.font=this._getContextFont();const n=e.measureText(t);return e.restore(),{width:n.width,height:parseInt(`${this.fontSize()}`,10)}}_setTextData(){const{width:t,height:e}=this._getTextSize(this.attrs.text);if(this.textWidth=t,this.textHeight=e,this.glyphInfo=[],!this.attrs.data)return null;const n=this.letterSpacing(),a=this.align(),o=this.kerningFunc(),s=Math.max(this.textWidth+((this.attrs.text||"").length-1)*n,0);let l=0;"center"===a&&(l=Math.max(0,this.pathLength/2-s/2)),"right"===a&&(l=Math.max(0,this.pathLength-s));const u=(0,i.stringToArray)(this.text());let c=l;for(let i=0;i<u.length;i++){const t=this._getPointAtLength(c);if(!t)return;let e=this._getTextSize(u[i]).width+n;if(" "===u[i]&&"justify"===a){const t=this.text().split(" ").length-1;e+=(this.pathLength-s)/t}const l=this._getPointAtLength(c+e);if(!l)return;const d=r.Path.getLineLength(t.x,t.y,l.x,l.y);let f=0;if(o)try{f=o(u[i-1],u[i])*this.fontSize()}catch(h){f=0}t.x+=f,l.x+=f,this.textWidth+=f;const g=r.Path.getPointOnLine(f+d/2,t.x,t.y,l.x,l.y),p=Math.atan2(l.y-t.y,l.x-t.x);this.glyphInfo.push({transposeX:g.x,transposeY:g.y,text:u[i],rotation:p,p0:t,p1:l}),c+=e}}getSelfRect(){if(!this.glyphInfo.length)return{x:0,y:0,width:0,height:0};const t=[];this.glyphInfo.forEach((function(e){t.push(e.p0.x),t.push(e.p0.y),t.push(e.p1.x),t.push(e.p1.y)}));let e,n,r=t[0]||0,i=t[0]||0,a=t[1]||0,o=t[1]||0;for(let l=0;l<t.length/2;l++)e=t[2*l],n=t[2*l+1],r=Math.min(r,e),i=Math.max(i,e),a=Math.min(a,n),o=Math.max(o,n);const s=this.fontSize();return{x:r-s/2,y:a-s/2,width:i-r+s,height:o-a+s}}destroy(){return t.Util.releaseCanvas(this.dummyCanvas),super.destroy()}};return Ot.TextPath=h,h.prototype._fillFunc=l,h.prototype._strokeFunc=u,h.prototype._fillFuncHit=l,h.prototype._strokeFuncHit=u,h.prototype.className="TextPath",h.prototype._attrsAffectingSize=["text","fontSize","data"],(0,o._registerNode)(h),e.Factory.addGetterSetter(h,"data"),e.Factory.addGetterSetter(h,"fontFamily","Arial"),e.Factory.addGetterSetter(h,"fontSize",12,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(h,"fontStyle",s),e.Factory.addGetterSetter(h,"align","left"),e.Factory.addGetterSetter(h,"letterSpacing",0,(0,a.getNumberValidator)()),e.Factory.addGetterSetter(h,"textBaseline","middle"),e.Factory.addGetterSetter(h,"fontVariant",s),e.Factory.addGetterSetter(h,"text",""),e.Factory.addGetterSetter(h,"textDecoration",""),e.Factory.addGetterSetter(h,"kerningFunc",void 0),Ot}(),S=Ht(),x=function(){if(Vt)return jt;Vt=1,Object.defineProperty(jt,"__esModule",{value:!0}),jt.Wedge=void 0;const t=_(),e=j(),n=c(),r=b(),i=c();let a=class extends e.Shape{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.radius(),0,n.Konva.getAngle(this.angle()),this.clockwise()),t.lineTo(0,0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}};return jt.Wedge=a,a.prototype.className="Wedge",a.prototype._centroid=!0,a.prototype._attrsAffectingSize=["radius"],(0,i._registerNode)(a),t.Factory.addGetterSetter(a,"radius",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(a,"angle",0,(0,r.getNumberValidator)()),t.Factory.addGetterSetter(a,"clockwise",!1),t.Factory.backCompat(a,{angleDeg:"angle",getAngleDeg:"getAngle",setAngleDeg:"setAngle"}),jt}(),w=qt(),C=function(){if(Yt)return Xt;Yt=1,Object.defineProperty(Xt,"__esModule",{value:!0}),Xt.Brighten=void 0;const t=_(),e=M(),n=b();return Xt.Brighten=function(t){const e=255*this.brightness(),n=t.data,r=n.length;for(let i=0;i<r;i+=4)n[i]+=e,n[i+1]+=e,n[i+2]+=e},t.Factory.addGetterSetter(e.Node,"brightness",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Xt}(),k=function(){if(Qt)return $t;Qt=1,Object.defineProperty($t,"__esModule",{value:!0}),$t.Contrast=void 0;const t=_(),e=M(),n=b();return $t.Contrast=function(t){const e=Math.pow((this.contrast()+100)/100,2),n=t.data,r=n.length;let i=150,a=150,o=150;for(let s=0;s<r;s+=4)i=n[s],a=n[s+1],o=n[s+2],i/=255,i-=.5,i*=e,i+=.5,i*=255,a/=255,a-=.5,a*=e,a+=.5,a*=255,o/=255,o-=.5,o*=e,o+=.5,o*=255,i=i<0?0:i>255?255:i,a=a<0?0:a>255?255:a,o=o<0?0:o>255?255:o,n[s]=i,n[s+1]=a,n[s+2]=o},t.Factory.addGetterSetter(e.Node,"contrast",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),$t}(),P=function(){if(Jt)return Zt;Jt=1,Object.defineProperty(Zt,"__esModule",{value:!0}),Zt.Emboss=void 0;const t=_(),e=M(),n=f(),r=b();return Zt.Emboss=function(t){const e=10*this.embossStrength(),r=255*this.embossWhiteLevel(),i=this.embossDirection(),a=this.embossBlend(),o=t.data,s=t.width,l=t.height,u=4*s;let c=0,h=0,d=l;switch(i){case"top-left":c=-1,h=-1;break;case"top":c=-1,h=0;break;case"top-right":c=-1,h=1;break;case"right":c=0,h=1;break;case"bottom-right":c=1,h=1;break;case"bottom":c=1,h=0;break;case"bottom-left":c=1,h=-1;break;case"left":c=0,h=-1;break;default:n.Util.error("Unknown emboss direction: "+i)}do{const t=(d-1)*u;let n=c;d+n<1&&(n=0),d+n>l&&(n=0);const i=(d-1+n)*s*4;let f=s;do{const n=t+4*(f-1);let l=h;f+l<1&&(l=0),f+l>s&&(l=0);const u=i+4*(f-1+l),c=o[n]-o[u],d=o[n+1]-o[u+1],g=o[n+2]-o[u+2];let p=c;const m=p>0?p:-p;if((d>0?d:-d)>m&&(p=d),(g>0?g:-g)>m&&(p=g),p*=e,a){const t=o[n]+p,e=o[n+1]+p,r=o[n+2]+p;o[n]=t>255?255:t<0?0:t,o[n+1]=e>255?255:e<0?0:e,o[n+2]=r>255?255:r<0?0:r}else{let t=r-p;t<0?t=0:t>255&&(t=255),o[n]=o[n+1]=o[n+2]=t}}while(--f)}while(--d)},t.Factory.addGetterSetter(e.Node,"embossStrength",.5,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossWhiteLevel",.5,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossDirection","top-left",void 0,t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"embossBlend",!1,void 0,t.Factory.afterSetFilter),Zt}(),T=function(){if(te)return ee;te=1,Object.defineProperty(ee,"__esModule",{value:!0}),ee.Enhance=void 0;const t=_(),e=M(),n=b();function r(t,e,n,r,i){const a=n-e,o=i-r;if(0===a)return r+o/2;if(0===o)return r;let s=(t-e)/a;return s=o*s+r,s}return ee.Enhance=function(t){const e=t.data,n=e.length;let i,a,o,s=e[0],l=s,u=e[1],c=u,h=e[2],d=h;const f=this.enhance();if(0===f)return;for(let r=0;r<n;r+=4)i=e[r+0],i<s?s=i:i>l&&(l=i),a=e[r+1],a<u?u=a:a>c&&(c=a),o=e[r+2],o<h?h=o:o>d&&(d=o);let g,p,m,y,v,b,_,S,x;l===s&&(l=255,s=0),c===u&&(c=255,u=0),d===h&&(d=255,h=0),f>0?(p=l+f*(255-l),m=s-f*(s-0),v=c+f*(255-c),b=u-f*(u-0),S=d+f*(255-d),x=h-f*(h-0)):(g=.5*(l+s),p=l+f*(l-g),m=s+f*(s-g),y=.5*(c+u),v=c+f*(c-y),b=u+f*(u-y),_=.5*(d+h),S=d+f*(d-_),x=h+f*(h-_));for(let w=0;w<n;w+=4)e[w+0]=r(e[w+0],s,l,m,p),e[w+1]=r(e[w+1],u,c,b,v),e[w+2]=r(e[w+2],h,d,x,S)},t.Factory.addGetterSetter(e.Node,"enhance",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),ee}(),E=(ne||(ne=1,Object.defineProperty(re,"__esModule",{value:!0}),re.Grayscale=void 0,re.Grayscale=function(t){const e=t.data,n=e.length;for(let r=0;r<n;r+=4){const t=.34*e[r]+.5*e[r+1]+.16*e[r+2];e[r]=t,e[r+1]=t,e[r+2]=t}}),re),F=function(){if(ie)return ae;ie=1,Object.defineProperty(ae,"__esModule",{value:!0}),ae.HSL=void 0;const t=_(),e=M(),n=b();return t.Factory.addGetterSetter(e.Node,"hue",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"saturation",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"luminance",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),ae.HSL=function(t){const e=t.data,n=e.length,r=Math.pow(2,this.saturation()),i=Math.abs(this.hue()+360)%360,a=127*this.luminance(),o=1*r*Math.cos(i*Math.PI/180),s=1*r*Math.sin(i*Math.PI/180),l=.299+.701*o+.167*s,u=.587-.587*o+.33*s,c=.114-.114*o-.497*s,h=.299-.299*o-.328*s,d=.587+.413*o+.035*s,f=.114-.114*o+.293*s,g=.299-.3*o+1.25*s,p=.587-.586*o-1.05*s,m=.114+.886*o-.2*s;let y,v,b,_;for(let S=0;S<n;S+=4)y=e[S+0],v=e[S+1],b=e[S+2],_=e[S+3],e[S+0]=l*y+u*v+c*b+a,e[S+1]=h*y+d*v+f*b+a,e[S+2]=g*y+p*v+m*b+a,e[S+3]=_},ae}(),N=function(){if(oe)return se;oe=1,Object.defineProperty(se,"__esModule",{value:!0}),se.HSV=void 0;const t=_(),e=M(),n=b();return se.HSV=function(t){const e=t.data,n=e.length,r=Math.pow(2,this.value()),i=Math.pow(2,this.saturation()),a=Math.abs(this.hue()+360)%360,o=r*i*Math.cos(a*Math.PI/180),s=r*i*Math.sin(a*Math.PI/180),l=.299*r+.701*o+.167*s,u=.587*r-.587*o+.33*s,c=.114*r-.114*o-.497*s,h=.299*r-.299*o-.328*s,d=.587*r+.413*o+.035*s,f=.114*r-.114*o+.293*s,g=.299*r-.3*o+1.25*s,p=.587*r-.586*o-1.05*s,m=.114*r+.886*o-.2*s;let y,v,b,_;for(let S=0;S<n;S+=4)y=e[S+0],v=e[S+1],b=e[S+2],_=e[S+3],e[S+0]=l*y+u*v+c*b,e[S+1]=h*y+d*v+f*b,e[S+2]=g*y+p*v+m*b,e[S+3]=_},t.Factory.addGetterSetter(e.Node,"hue",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"saturation",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"value",0,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),se}(),A=(le||(le=1,Object.defineProperty(ue,"__esModule",{value:!0}),ue.Invert=void 0,ue.Invert=function(t){const e=t.data,n=e.length;for(let r=0;r<n;r+=4)e[r]=255-e[r],e[r+1]=255-e[r+1],e[r+2]=255-e[r+2]}),ue),R=de(),L=pe(),D=function(){if(me)return ye;me=1,Object.defineProperty(ye,"__esModule",{value:!0}),ye.Noise=void 0;const t=_(),e=M(),n=b();return ye.Noise=function(t){const e=255*this.noise(),n=t.data,r=n.length,i=e/2;for(let a=0;a<r;a+=4)n[a+0]+=i-2*i*Math.random(),n[a+1]+=i-2*i*Math.random(),n[a+2]+=i-2*i*Math.random()},t.Factory.addGetterSetter(e.Node,"noise",.2,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),ye}(),z=function(){if(ve)return be;ve=1,Object.defineProperty(be,"__esModule",{value:!0}),be.Pixelate=void 0;const t=_(),e=f(),n=M(),r=b();return be.Pixelate=function(t){let n,r,i,a,o,s,l,u,c,h,d,f,g,p,m=Math.ceil(this.pixelSize()),y=t.width,v=t.height,b=Math.ceil(y/m),_=Math.ceil(v/m),S=t.data;if(m<=0)e.Util.error("pixelSize value can not be <= 0");else for(f=0;f<b;f+=1)for(g=0;g<_;g+=1){for(a=0,o=0,s=0,l=0,u=f*m,c=u+m,h=g*m,d=h+m,p=0,n=u;n<c;n+=1)if(!(n>=y))for(r=h;r<d;r+=1)r>=v||(i=4*(y*r+n),a+=S[i+0],o+=S[i+1],s+=S[i+2],l+=S[i+3],p+=1);for(a/=p,o/=p,s/=p,l/=p,n=u;n<c;n+=1)if(!(n>=y))for(r=h;r<d;r+=1)r>=v||(i=4*(y*r+n),S[i+0]=a,S[i+1]=o,S[i+2]=s,S[i+3]=l)}},t.Factory.addGetterSetter(n.Node,"pixelSize",8,(0,r.getNumberValidator)(),t.Factory.afterSetFilter),be}(),I=function(){if(_e)return Se;_e=1,Object.defineProperty(Se,"__esModule",{value:!0}),Se.Posterize=void 0;const t=_(),e=M(),n=b();return Se.Posterize=function(t){const e=Math.round(254*this.levels())+1,n=t.data,r=n.length,i=255/e;for(let a=0;a<r;a+=1)n[a]=Math.floor(n[a]/i)*i},t.Factory.addGetterSetter(e.Node,"levels",.5,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Se}(),G=function(){if(xe)return we;xe=1,Object.defineProperty(we,"__esModule",{value:!0}),we.RGB=void 0;const t=_(),e=M(),n=b();return we.RGB=function(t){const e=t.data,n=e.length,r=this.red(),i=this.green(),a=this.blue();for(let o=0;o<n;o+=4){const t=(.34*e[o]+.5*e[o+1]+.16*e[o+2])/255;e[o]=t*r,e[o+1]=t*i,e[o+2]=t*a,e[o+3]=e[o+3]}},t.Factory.addGetterSetter(e.Node,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"blue",0,n.RGBComponent,t.Factory.afterSetFilter),we}(),O=function(){if(Ce)return ke;Ce=1,Object.defineProperty(ke,"__esModule",{value:!0}),ke.RGBA=void 0;const t=_(),e=M(),n=b();return ke.RGBA=function(t){const e=t.data,n=e.length,r=this.red(),i=this.green(),a=this.blue(),o=this.alpha();for(let s=0;s<n;s+=4){const t=1-o;e[s]=r*o+e[s]*t,e[s+1]=i*o+e[s+1]*t,e[s+2]=a*o+e[s+2]*t}},t.Factory.addGetterSetter(e.Node,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),t.Factory.addGetterSetter(e.Node,"blue",0,n.RGBComponent,t.Factory.afterSetFilter),t.Factory.addGetterSetter(e.Node,"alpha",1,(function(t){return this._filterUpToDate=!1,t>1?1:t<0?0:t})),ke}(),U=(Pe||(Pe=1,Object.defineProperty(Te,"__esModule",{value:!0}),Te.Sepia=void 0,Te.Sepia=function(t){const e=t.data,n=e.length;for(let r=0;r<n;r+=4){const t=e[r+0],n=e[r+1],i=e[r+2];e[r+0]=Math.min(255,.393*t+.769*n+.189*i),e[r+1]=Math.min(255,.349*t+.686*n+.168*i),e[r+2]=Math.min(255,.272*t+.534*n+.131*i)}}),Te),B=(Ee||(Ee=1,Object.defineProperty(Fe,"__esModule",{value:!0}),Fe.Solarize=void 0,Fe.Solarize=function(t){const e=t.data,n=t.width,r=4*n;let i=t.height;do{const t=(i-1)*r;let a=n;do{const n=t+4*(a-1);let r=e[n],i=e[n+1],o=e[n+2];r>127&&(r=255-r),i>127&&(i=255-i),o>127&&(o=255-o),e[n]=r,e[n+1]=i,e[n+2]=o}while(--a)}while(--i)}),Fe),H=function(){if(Ne)return Ae;Ne=1,Object.defineProperty(Ae,"__esModule",{value:!0}),Ae.Threshold=void 0;const t=_(),e=M(),n=b();return Ae.Threshold=function(t){const e=255*this.threshold(),n=t.data,r=n.length;for(let i=0;i<r;i+=1)n[i]=n[i]<e?0:255},t.Factory.addGetterSetter(e.Node,"threshold",.5,(0,n.getNumberValidator)(),t.Factory.afterSetFilter),Ae}();return s.Konva=t.Konva.Util._assign(t.Konva,{Arc:e.Arc,Arrow:n.Arrow,Circle:r.Circle,Ellipse:i.Ellipse,Image:a.Image,Label:o.Label,Tag:o.Tag,Line:l.Line,Path:u.Path,Rect:h.Rect,RegularPolygon:d.RegularPolygon,Ring:g.Ring,Sprite:p.Sprite,Star:m.Star,Text:y.Text,TextPath:v.TextPath,Transformer:S.Transformer,Wedge:x.Wedge,Filters:{Blur:w.Blur,Brighten:C.Brighten,Contrast:k.Contrast,Emboss:P.Emboss,Enhance:T.Enhance,Grayscale:E.Grayscale,HSL:F.HSL,HSV:N.HSV,Invert:A.Invert,Kaleidoscope:R.Kaleidoscope,Mask:L.Mask,Noise:D.Noise,Pixelate:z.Pixelate,Posterize:I.Posterize,RGB:G.RGB,RGBA:O.RGBA,Sepia:U.Sepia,Solarize:B.Solarize,Threshold:H.Threshold}}),s}var Le,De=o.exports;const ze=e(function(){if(Le)return o.exports;Le=1,Object.defineProperty(De,"__esModule",{value:!0});const t=Re();return o.exports=t.Konva,o.exports}());var Ie,Ge={exports:{}};const Oe=e((Ie||(Ie=1,function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.Konva=void 0;var n=rt();Object.defineProperty(e,"Konva",{enumerable:!0,get:function(){return n.Konva}});const r=rt();t.exports=r.Konva}(Ge,Ge.exports)),Ge.exports));var Ue,Be,He,Ve,je={exports:{}},We={exports:{}},Ke={exports:{}},qe={};function Ye(){return Be||(Be=1,Ke.exports=(Ue||(Ue=1,function(t){function e(t,e){var n=t.length;t.push(e);t:for(;0<n;){var r=n-1>>>1,a=t[r];if(!(0<i(a,e)))break t;t[r]=e,t[n]=a,n=r}}function n(t){return 0===t.length?null:t[0]}function r(t){if(0===t.length)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var r=0,a=t.length,o=a>>>1;r<o;){var s=2*(r+1)-1,l=t[s],u=s+1,c=t[u];if(0>i(l,n))u<a&&0>i(c,l)?(t[r]=c,t[u]=n,r=u):(t[r]=l,t[s]=n,r=s);else{if(!(u<a&&0>i(c,n)))break t;t[r]=c,t[u]=n,r=u}}}return e}function i(t,e){var n=t.sortIndex-e.sortIndex;return 0!==n?n:t.id-e.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var l=[],u=[],c=1,h=null,d=3,f=!1,g=!1,p=!1,m="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function b(t){for(var i=n(u);null!==i;){if(null===i.callback)r(u);else{if(!(i.startTime<=t))break;r(u),i.sortIndex=i.expirationTime,e(l,i)}i=n(u)}}function _(t){if(p=!1,b(t),!g)if(null!==n(l))g=!0,N();else{var e=n(u);null!==e&&M(_,e.startTime-t)}}var S,x=!1,w=-1,C=5,k=-1;function P(){return!(t.unstable_now()-k<C)}function T(){if(x){var e=t.unstable_now();k=e;var i=!0;try{t:{g=!1,p&&(p=!1,y(w),w=-1),f=!0;var a=d;try{e:{for(b(e),h=n(l);null!==h&&!(h.expirationTime>e&&P());){var o=h.callback;if("function"==typeof o){h.callback=null,d=h.priorityLevel;var s=o(h.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){h.callback=s,b(e),i=!0;break e}h===n(l)&&r(l),b(e)}else r(l);h=n(l)}if(null!==h)i=!0;else{var c=n(u);null!==c&&M(_,c.startTime-e),i=!1}}break t}finally{h=null,d=a,f=!1}i=void 0}}finally{i?S():x=!1}}}if("function"==typeof v)S=function(){v(T)};else if("undefined"!=typeof MessageChannel){var E=new MessageChannel,F=E.port2;E.port1.onmessage=T,S=function(){F.postMessage(null)}}else S=function(){m(T,0)};function N(){x||(x=!0,S())}function M(e,n){w=m((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(t){t.callback=null},t.unstable_continueExecution=function(){g||f||(g=!0,N())},t.unstable_forceFrameRate=function(t){0>t||125<t||(C=0<t?Math.floor(1e3/t):5)},t.unstable_getCurrentPriorityLevel=function(){return d},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(t){switch(d){case 1:case 2:case 3:var e=3;break;default:e=d}var n=d;d=e;try{return t()}finally{d=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=d;d=t;try{return e()}finally{d=n}},t.unstable_scheduleCallback=function(r,i,a){var o=t.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?o+a:o,r){case 1:var s=-1;break;case 2:s=250;break;case 5:s=**********;break;case 4:s=1e4;break;default:s=5e3}return r={id:c++,callback:i,priorityLevel:r,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>o?(r.sortIndex=a,e(u,r),null===n(l)&&r===n(u)&&(p?(y(w),w=-1):p=!0,M(_,a-o))):(r.sortIndex=s,e(l,r),g||f||(g=!0,N())),r},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(t){var e=d;return function(){var n=d;d=e;try{return t.apply(this,arguments)}finally{d=n}}}}(qe)),qe)),Ke.exports}
/**
 * @license React
 * react-reconciler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function Xe(){return He||(He=1,(t=We).exports=function(t){function e(t,e,n,r){return new ki(t,e,n,r)}function r(){}function i(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(t){return null===t||"object"!=typeof t?null:"function"==typeof(t=ua&&t[ua]||t["@@iterator"])?t:null}function o(t){if(null==t)return null;if("function"==typeof t)return t.$$typeof===ca?null:t.displayName||t.name||null;if("string"==typeof t)return t;switch(t){case Qi:return"Fragment";case Xi:return"Portal";case Ji:return"Profiler";case $i:return"StrictMode";case ra:return"Suspense";case ia:return"SuspenseList"}if("object"==typeof t)switch(t.$$typeof){case ea:return(t.displayName||"Context")+".Provider";case ta:return(t._context.displayName||"Context")+".Consumer";case na:var e=t.render;return(t=t.displayName)||(t=""!==(t=e.displayName||e.name||"")?"ForwardRef("+t+")":"ForwardRef"),t;case aa:return null!==(e=t.displayName||null)?e:o(t.type)||"Memo";case oa:e=t._payload,t=t._init;try{return o(t(e))}catch(n){}}return null}function s(t){if(void 0===Bi)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Bi=e&&e[1]||"",Hi=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Bi+t+Hi}function l(t,e){if(!t||da)return"";da=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(e){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(i){var r=i}Reflect.construct(t,[],n)}else{try{n.call()}catch(a){r=a}t.call(n.prototype)}}else{try{throw Error()}catch(o){r=o}(n=t())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(s){if(s&&r&&"string"==typeof s.stack)return[s.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),o=a[0],l=a[1];if(o&&l){var u=o.split("\n"),c=l.split("\n");for(i=r=0;r<u.length&&!u[r].includes("DetermineComponentFrameRoot");)r++;for(;i<c.length&&!c[i].includes("DetermineComponentFrameRoot");)i++;if(r===u.length||i===c.length)for(r=u.length-1,i=c.length-1;1<=r&&0<=i&&u[r]!==c[i];)i--;for(;1<=r&&0<=i;r--,i--)if(u[r]!==c[i]){if(1!==r||1!==i)do{if(r--,0>--i||u[r]!==c[i]){var h="\n"+u[r].replace(" at new "," at ");return t.displayName&&h.includes("<anonymous>")&&(h=h.replace("<anonymous>",t.displayName)),h}}while(1<=r&&0<=i);break}}}finally{da=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?s(n):""}function u(t){switch(t.tag){case 26:case 27:case 5:return s(t.type);case 16:return s("Lazy");case 13:return s("Suspense");case 19:return s("SuspenseList");case 0:case 15:return t=l(t.type,!1);case 11:return t=l(t.type.render,!1);case 1:return t=l(t.type,!0);default:return""}}function c(t){try{var e="";do{e+=u(t),t=t.return}while(t);return e}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function h(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do{!!(4098&(e=t).flags)&&(n=e.return),t=e.return}while(t)}return 3===e.tag?n:null}function d(t){if(h(t)!==t)throw Error(i(188))}function f(t){var e=t.alternate;if(!e){if(null===(e=h(t)))throw Error(i(188));return e!==t?null:t}for(var n=t,r=e;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return d(a),t;if(o===r)return d(a),e;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var s=!1,l=a.child;l;){if(l===n){s=!0,n=a,r=o;break}if(l===r){s=!0,r=a,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=a;break}if(l===r){s=!0,r=o,n=a;break}l=l.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?t:e}function g(t){var e=t.tag;if(5===e||26===e||27===e||6===e)return t;for(t=t.child;null!==t;){if(null!==(e=g(t)))return e;t=t.sibling}return null}function p(t){var e=t.tag;if(5===e||26===e||27===e||6===e)return t;for(t=t.child;null!==t;){if(4!==t.tag&&null!==(e=p(t)))return e;t=t.sibling}return null}function m(t){return{current:t}}function y(t){0>bs||(t.current=vs[bs],vs[bs]=null,bs--)}function v(t,e){bs++,vs[bs]=t.current,t.current=e}function b(t){var e=42&t;if(0!==e)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194176&t;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&t;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function _(t,e){var n=t.pendingLanes;if(0===n)return 0;var r=0,i=t.suspendedLanes,a=t.pingedLanes,o=t.warmLanes;t=0!==t.finishedLanes;var s=134217727&n;return 0!==s?0!==(n=s&~i)?r=b(n):0!==(a&=s)?r=b(a):t||0!==(o=s&~o)&&(r=b(o)):0!==(s=n&~i)?r=b(s):0!==a?r=b(a):t||0!==(o=n&~o)&&(r=b(o)),0===r?0:0!==e&&e!==r&&0===(e&i)&&((i=r&-r)>=(o=e&-e)||32===i&&4194176&o)?e:r}function S(t,e){return 0===(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)}function x(t,e){switch(t){case 1:case 2:case 4:case 8:return e+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;default:return-1}}function w(){var t=Cs;return!(4194176&(Cs<<=1))&&(Cs=128),t}function C(){var t=ks;return!(62914560&(ks<<=1))&&(ks=4194304),t}function k(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function P(t,e){t.pendingLanes|=e,268435456!==e&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function T(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var r=31-Ss(e);t.entangledLanes|=e,t.entanglements[r]=1073741824|t.entanglements[r]|4194218&n}function E(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-Ss(n),i=1<<r;i&e|t[r]&e&&(t[r]|=e),n&=~i}}function F(t){return 2<(t&=-t)?8<t?134217727&t?32:268435456:8:2}function N(t){if("function"==typeof Ds&&zs(t),Gs&&"function"==typeof Gs.setStrictMode)try{Gs.setStrictMode(Is,t)}catch(e){}}function M(t,e){if("object"==typeof t&&null!==t){var n=Us.get(t);return void 0!==n?n:(e={value:t,source:e,stack:c(e)},Us.set(t,e),e)}return{value:t,source:e,stack:c(e)}}function A(t,e){Bs[Hs++]=js,Bs[Hs++]=Vs,Vs=t,js=e}function R(t,e,n){Ws[Ks++]=Ys,Ws[Ks++]=Xs,Ws[Ks++]=qs,qs=t;var r=Ys;t=Xs;var i=32-Ss(r)-1;r&=~(1<<i),n+=1;var a=32-Ss(e)+i;if(30<a){var o=i-i%5;a=(r&(1<<o)-1).toString(32),r>>=o,i-=o,Ys=1<<32-Ss(e)+i|n<<i|r,Xs=a+t}else Ys=1<<a|n<<i|r,Xs=t}function L(t){null!==t.return&&(A(t,1),R(t,1,0))}function D(t){for(;t===Vs;)Vs=Bs[--Hs],Bs[Hs]=null,js=Bs[--Hs],Bs[Hs]=null;for(;t===qs;)qs=Ws[--Ks],Ws[Ks]=null,Xs=Ws[--Ks],Ws[Ks]=null,Ys=Ws[--Ks],Ws[Ks]=null}function z(t,e){v(Js,e),v($s,t),v(Qs,null),t=va(e),y(Qs),v(Qs,t)}function I(){y(Qs),y($s),y(Js)}function G(t){null!==t.memoizedState&&v(Zs,t);var e=Qs.current,n=ba(e,t.type);e!==n&&(v($s,t),v(Qs,n))}function O(t){$s.current===t&&(y(Qs),y($s)),Zs.current===t&&(y(Zs),Na?qa._currentValue=Ka:qa._currentValue2=Ka)}function U(t){throw W(M(Error(i(418,"")),t)),al}function B(t,e){if(!Ra)throw Error(i(175));Bo(t.stateNode,t.type,t.memoizedProps,e,t)||U(t)}function H(t){for(tl=t.return;tl;)switch(tl.tag){case 3:case 27:return void(il=!0);case 5:case 13:return void(il=!1);default:tl=tl.return}}function V(t){if(!Ra||t!==tl)return!1;if(!nl)return H(t),nl=!0,!1;var e=!1;if(ds?3!==t.tag&&27!==t.tag&&(5!==t.tag||Xo(t.type)&&!ka(t.type,t.memoizedProps))&&(e=!0):3!==t.tag&&(5!==t.tag||Xo(t.type)&&!ka(t.type,t.memoizedProps))&&(e=!0),e&&el&&U(t),H(t),13===t.tag){if(!Ra)throw Error(i(316));if(!(t=null!==(t=t.memoizedState)?t.dehydrated:null))throw Error(i(317));el=jo(t)}else el=tl?Lo(t.stateNode):null;return!0}function j(){Ra&&(el=tl=null,nl=!1)}function W(t){null===rl?rl=[t]:rl.push(t)}function K(){for(var t=sl,e=ll=sl=0;e<t;){var n=ol[e];ol[e++]=null;var r=ol[e];ol[e++]=null;var i=ol[e];ol[e++]=null;var a=ol[e];if(ol[e++]=null,null!==r&&null!==i){var o=r.pending;null===o?i.next=i:(i.next=o.next,o.next=i),r.pending=i}0!==a&&Q(n,i,a)}}function q(t,e,n,r){ol[sl++]=t,ol[sl++]=e,ol[sl++]=n,ol[sl++]=r,ll|=r,t.lanes|=r,null!==(t=t.alternate)&&(t.lanes|=r)}function Y(t,e,n,r){return q(t,e,n,r),$(t)}function X(t,e){return q(t,null,null,e),$(t)}function Q(t,e,n){t.lanes|=n;var r=t.alternate;null!==r&&(r.lanes|=n);for(var i=!1,a=t.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(t=a.stateNode)||1&t._visibility||(i=!0)),t=a,a=a.return;i&&null!==e&&3===t.tag&&(a=t.stateNode,i=31-Ss(n),null===(t=(a=a.hiddenUpdates)[i])?a[i]=[e]:t.push(e),e.lane=536870912|n)}function $(t){if(50<rc)throw rc=0,ic=null,Error(i(185));for(var e=t.return;null!==e;)e=(t=e).return;return 3===t.tag?t.stateNode:null}function J(t){var e;t!==cl&&null===t.next&&(null===cl?ul=cl=t:cl=cl.next=t),dl=!0,hl||(hl=!0,e=tt,Xa?Qa((function(){6&Fu?Ps(Ms,e):e()})):Ps(Ms,e))}function Z(t,e){if(!fl&&dl){fl=!0;do{for(var n=!1,r=ul;null!==r;){if(0!==t){var i=r.pendingLanes;if(0===i)var a=0;else{var o=r.suspendedLanes,s=r.pingedLanes;a=(1<<31-Ss(42|t)+1)-1,a=201326677&(a&=i&~(o&~s))?201326677&a|1:a?2|a:0}0!==a&&(n=!0,rt(r,a))}else a=Au,!(3&(a=_(r,r===Nu?a:0)))||S(r,a)||(n=!0,rt(r,a));r=r.next}}while(n);fl=!1}}function tt(){dl=hl=!1;var t=0;0!==gl&&(Oa()&&(t=gl),gl=0);for(var e=Ns(),n=null,r=ul;null!==r;){var i=r.next,a=et(r,e);0===a?(r.next=null,null===n?ul=i:n.next=i,null===i&&(cl=n)):(n=r,(0!==t||3&a)&&(dl=!0)),r=i}Z(t)}function et(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,i=t.expirationTimes,a=-62914561&t.pendingLanes;0<a;){var o=31-Ss(a),s=1<<o,l=i[o];-1===l?0!==(s&n)&&0===(s&r)||(i[o]=x(s,e)):l<=e&&(t.expiredLanes|=s),a&=~s}if(n=Au,n=_(t,t===(e=Nu)?n:0),r=t.callbackNode,0===n||t===e&&2===Ru||null!==t.cancelPendingCommit)return null!==r&&null!==r&&Ts(r),t.callbackNode=null,t.callbackPriority=0;if(!(3&n)||S(t,n)){if((e=n&-n)===t.callbackPriority)return e;switch(null!==r&&Ts(r),F(n)){case 2:case 8:n=As;break;case 32:default:n=Rs;break;case 268435456:n=Ls}return r=nt.bind(null,t),n=Ps(n,r),t.callbackPriority=e,t.callbackNode=n,e}return null!==r&&null!==r&&Ts(r),t.callbackPriority=2,t.callbackNode=null,2}function nt(t,e){var n=t.callbackNode;if(yi()&&t.callbackNode!==n)return null;var r=Au;return 0===(r=_(t,t===Nu?r:0))?null:(Yr(t,r,e),et(t,Ns()),null!=t.callbackNode&&t.callbackNode===n?nt.bind(null,t):null)}function rt(t,e){if(yi())return null;Yr(t,e,!0)}function it(){return 0===gl&&(gl=w()),gl}function at(){if(0===--ml&&null!==pl){null!==vl&&(vl.status="fulfilled");var t=pl;pl=null,yl=0,vl=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function ot(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function st(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function lt(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ut(t,e,n){var r=t.updateQueue;if(null===r)return null;if(r=r.shared,2&Fu){var i=r.pending;return null===i?e.next=e:(e.next=i.next,i.next=e),r.pending=e,e=$(t),Q(t,null,n),e}return q(t,r,e,n),$(t)}function ct(t,e,n){if(null!==(e=e.updateQueue)&&(e=e.shared,4194176&n)){var r=e.lanes;n|=r&=t.pendingLanes,e.lanes=n,E(t,n)}}function ht(t,e){var n=t.updateQueue,r=t.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?i=a=o:a=a.next=o,n=n.next}while(null!==n);null===a?i=a=e:a=a.next=e}else i=a=e;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(t.updateQueue=n)}null===(t=n.lastBaseUpdate)?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function dt(){if(_l&&null!==vl)throw vl}function ft(t,e,n,r){_l=!1;var i=t.updateQueue;bl=!1;var a=i.firstBaseUpdate,o=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,u=l.next;l.next=null,null===o?a=u:o.next=u,o=l;var c=t.alternate;null!==c&&(s=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l)}if(null!==a){var h=i.baseState;for(o=0,c=u=l=null,s=a;;){var d=-536870913&s.lane,f=d!==s.lane;if(f?(Au&d)===d:(r&d)===d){0!==d&&d===yl&&(_l=!0),null!==c&&(c=c.next={lane:0,tag:s.tag,payload:s.payload,callback:null,next:null});t:{var g=t,p=s;d=e;var m=n;switch(p.tag){case 1:if("function"==typeof(g=p.payload)){h=g.call(m,h,d);break t}h=g;break t;case 3:g.flags=-65537&g.flags|128;case 0:if(null==(d="function"==typeof(g=p.payload)?g.call(m,h,d):g))break t;h=Ki({},h,d);break t;case 2:bl=!0}}null!==(d=s.callback)&&(t.flags|=64,f&&(t.flags|=8192),null===(f=i.callbacks)?i.callbacks=[d]:f.push(d))}else f={lane:d,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=f,l=h):c=c.next=f,o|=d;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(f=s).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}null===c&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null===a&&(i.shared.lanes=0),Uu|=o,t.lanes=o,t.memoizedState=h}}function gt(t,e){if("function"!=typeof t)throw Error(i(191,t));t.call(e)}function pt(t,e){var n=t.callbacks;if(null!==n)for(t.callbacks=null,t=0;t<n.length;t++)gt(n[t],e)}function mt(t,e){if(Os(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Sl.call(e,i)||!Os(t[i],e[i]))return!1}return!0}function yt(t){return"fulfilled"===(t=t.status)||"rejected"===t}function vt(){}function bt(t,e,n){switch(void 0===(n=t[n])?t.push(e):n!==e&&(e.then(vt,vt),e=n),e.status){case"fulfilled":return e.value;case"rejected":if((t=e.reason)===xl)throw Error(i(483));throw t;default:if("string"==typeof e.status)e.then(vt,vt);else{if(null!==(t=Nu)&&100<t.shellSuspendCounter)throw Error(i(482));(t=e).status="pending",t.then((function(t){if("pending"===e.status){var n=e;n.status="fulfilled",n.value=t}}),(function(t){if("pending"===e.status){var n=e;n.status="rejected",n.reason=t}}))}switch(e.status){case"fulfilled":return e.value;case"rejected":if((t=e.reason)===xl)throw Error(i(483));throw t}throw kl=e,xl}}function _t(){if(null===kl)throw Error(i(459));var t=kl;return kl=null,t}function St(t){var e=Tl;return Tl+=1,null===Pl&&(Pl=[]),bt(Pl,t,e)}function xt(t,e){e=e.props.ref,t.ref=void 0!==e?e:null}function wt(t,e){if(e.$$typeof===qi)throw Error(i(525));throw t=Object.prototype.toString.call(e),Error(i(31,"[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function Ct(t){return(0,t._init)(t._payload)}function kt(t){function n(e,n){if(t){var r=e.deletions;null===r?(e.deletions=[n],e.flags|=16):r.push(n)}}function r(e,r){if(!t)return null;for(;null!==r;)n(e,r),r=r.sibling;return null}function o(t){for(var e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function s(t,e){return(t=Ti(t,e)).index=0,t.sibling=null,t}function l(e,n,r){return e.index=r,t?null!==(r=e.alternate)?(r=r.index)<n?(e.flags|=33554434,n):r:(e.flags|=33554434,n):(e.flags|=1048576,n)}function u(e){return t&&null===e.alternate&&(e.flags|=33554434),e}function c(t,e,n,r){return null===e||6!==e.tag?((e=Ai(n,t.mode,r)).return=t,e):((e=s(e,n)).return=t,e)}function h(t,e,n,r){var i=n.type;return i===Qi?f(t,e,n.props.children,r,n.key):null!==e&&(e.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===oa&&Ct(i)===e.type)?(xt(e=s(e,n.props),n),e.return=t,e):(xt(e=Fi(n.type,n.key,n.props,null,t.mode,r),n),e.return=t,e)}function d(t,e,n,r){return null===e||4!==e.tag||e.stateNode.containerInfo!==n.containerInfo||e.stateNode.implementation!==n.implementation?((e=Ri(n,t.mode,r)).return=t,e):((e=s(e,n.children||[])).return=t,e)}function f(t,e,n,r,i){return null===e||7!==e.tag?((e=Ni(n,t.mode,r,i)).return=t,e):((e=s(e,n)).return=t,e)}function g(t,e,n){if("string"==typeof e&&""!==e||"number"==typeof e||"bigint"==typeof e)return(e=Ai(""+e,t.mode,n)).return=t,e;if("object"==typeof e&&null!==e){switch(e.$$typeof){case Yi:return xt(n=Fi(e.type,e.key,e.props,null,t.mode,n),e),n.return=t,n;case Xi:return(e=Ri(e,t.mode,n)).return=t,e;case oa:return g(t,e=(0,e._init)(e._payload),n)}if(fa(e)||a(e))return(e=Ni(e,t.mode,n,null)).return=t,e;if("function"==typeof e.then)return g(t,St(e),n);if(e.$$typeof===ea)return g(t,Ln(t,e),n);wt(t,e)}return null}function p(t,e,n,r){var i=null!==e?e.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==i?null:c(t,e,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case Yi:return n.key===i?h(t,e,n,r):null;case Xi:return n.key===i?d(t,e,n,r):null;case oa:return p(t,e,n=(i=n._init)(n._payload),r)}if(fa(n)||a(n))return null!==i?null:f(t,e,n,r,null);if("function"==typeof n.then)return p(t,e,St(n),r);if(n.$$typeof===ea)return p(t,e,Ln(t,n),r);wt(t,n)}return null}function m(t,e,n,r,i){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return c(e,t=t.get(n)||null,""+r,i);if("object"==typeof r&&null!==r){switch(r.$$typeof){case Yi:return h(e,t=t.get(null===r.key?n:r.key)||null,r,i);case Xi:return d(e,t=t.get(null===r.key?n:r.key)||null,r,i);case oa:return m(t,e,n,r=(0,r._init)(r._payload),i)}if(fa(r)||a(r))return f(e,t=t.get(n)||null,r,i,null);if("function"==typeof r.then)return m(t,e,n,St(r),i);if(r.$$typeof===ea)return m(t,e,n,Ln(e,r),i);wt(e,r)}return null}function y(e,c,h,d){if("object"==typeof h&&null!==h&&h.type===Qi&&null===h.key&&(h=h.props.children),"object"==typeof h&&null!==h){switch(h.$$typeof){case Yi:t:{for(var f=h.key;null!==c;){if(c.key===f){if((f=h.type)===Qi){if(7===c.tag){r(e,c.sibling),(d=s(c,h.props.children)).return=e,e=d;break t}}else if(c.elementType===f||"object"==typeof f&&null!==f&&f.$$typeof===oa&&Ct(f)===c.type){r(e,c.sibling),xt(d=s(c,h.props),h),d.return=e,e=d;break t}r(e,c);break}n(e,c),c=c.sibling}h.type===Qi?((d=Ni(h.props.children,e.mode,d,h.key)).return=e,e=d):(xt(d=Fi(h.type,h.key,h.props,null,e.mode,d),h),d.return=e,e=d)}return u(e);case Xi:t:{for(f=h.key;null!==c;){if(c.key===f){if(4===c.tag&&c.stateNode.containerInfo===h.containerInfo&&c.stateNode.implementation===h.implementation){r(e,c.sibling),(d=s(c,h.children||[])).return=e,e=d;break t}r(e,c);break}n(e,c),c=c.sibling}(d=Ri(h,e.mode,d)).return=e,e=d}return u(e);case oa:return y(e,c,h=(f=h._init)(h._payload),d)}if(fa(h))return function(e,i,a,s){for(var u=null,c=null,h=i,d=i=0,f=null;null!==h&&d<a.length;d++){h.index>d?(f=h,h=null):f=h.sibling;var y=p(e,h,a[d],s);if(null===y){null===h&&(h=f);break}t&&h&&null===y.alternate&&n(e,h),i=l(y,i,d),null===c?u=y:c.sibling=y,c=y,h=f}if(d===a.length)return r(e,h),nl&&A(e,d),u;if(null===h){for(;d<a.length;d++)null!==(h=g(e,a[d],s))&&(i=l(h,i,d),null===c?u=h:c.sibling=h,c=h);return nl&&A(e,d),u}for(h=o(h);d<a.length;d++)null!==(f=m(h,e,d,a[d],s))&&(t&&null!==f.alternate&&h.delete(null===f.key?d:f.key),i=l(f,i,d),null===c?u=f:c.sibling=f,c=f);return t&&h.forEach((function(t){return n(e,t)})),nl&&A(e,d),u}(e,c,h,d);if(a(h)){if("function"!=typeof(f=a(h)))throw Error(i(150));return function(e,a,s,u){if(null==s)throw Error(i(151));for(var c=null,h=null,d=a,f=a=0,y=null,v=s.next();null!==d&&!v.done;f++,v=s.next()){d.index>f?(y=d,d=null):y=d.sibling;var b=p(e,d,v.value,u);if(null===b){null===d&&(d=y);break}t&&d&&null===b.alternate&&n(e,d),a=l(b,a,f),null===h?c=b:h.sibling=b,h=b,d=y}if(v.done)return r(e,d),nl&&A(e,f),c;if(null===d){for(;!v.done;f++,v=s.next())null!==(v=g(e,v.value,u))&&(a=l(v,a,f),null===h?c=v:h.sibling=v,h=v);return nl&&A(e,f),c}for(d=o(d);!v.done;f++,v=s.next())null!==(v=m(d,e,f,v.value,u))&&(t&&null!==v.alternate&&d.delete(null===v.key?f:v.key),a=l(v,a,f),null===h?c=v:h.sibling=v,h=v);return t&&d.forEach((function(t){return n(e,t)})),nl&&A(e,f),c}(e,c,h=f.call(h),d)}if("function"==typeof h.then)return y(e,c,St(h),d);if(h.$$typeof===ea)return y(e,c,Ln(e,h),d);wt(e,h)}return"string"==typeof h&&""!==h||"number"==typeof h||"bigint"==typeof h?(h=""+h,null!==c&&6===c.tag?(r(e,c.sibling),(d=s(c,h)).return=e,e=d):(r(e,c),(d=Ai(h,e.mode,d)).return=e,e=d),u(e)):r(e,c)}return function(t,n,r,i){try{Tl=0;var a=y(t,n,r,i);return Pl=null,a}catch(s){if(s===xl)throw s;var o=e(29,s,null,t.mode);return o.lanes=i,o.return=t,o}}}function Pt(t,e){v(Ml,t=Gu),v(Nl,e),Gu=t|e.baseLanes}function Tt(){v(Ml,Gu),v(Nl,Nl.current)}function Et(){Gu=Ml.current,y(Nl),y(Ml)}function Ft(t){var e=t.alternate;v(Ll,1&Ll.current),v(Al,t),null===Rl&&(null===e||null!==Nl.current||null!==e.memoizedState)&&(Rl=t)}function Nt(t){if(22===t.tag){if(v(Ll,Ll.current),v(Al,t),null===Rl){var e=t.alternate;null!==e&&null!==e.memoizedState&&(Rl=t)}}else Mt()}function Mt(){v(Ll,Ll.current),v(Al,Al.current)}function At(t){y(Al),Rl===t&&(Rl=null),y(Ll)}function Rt(t){for(var e=t;null!==e;){if(13===e.tag){var n=e.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||Eo(n)||Fo(n)))return e}else if(19===e.tag&&void 0!==e.memoizedProps.revealOrder){if(128&e.flags)return e}else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break;for(;null===e.sibling;){if(null===e.return||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Lt(){throw Error(i(321))}function Dt(t,e){if(null===e)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Os(t[n],e[n]))return!1;return!0}function zt(t,e,n,r,i,a){return Dl=a,zl=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,ha.H=null===t||null===t.memoizedState?Yl:Xl,Bl=!1,a=n(r,i),Bl=!1,Ul&&(a=Gt(e,n,r,i)),It(t),a}function It(t){ha.H=ql;var e=null!==Il&&null!==Il.next;if(Dl=0,Gl=Il=zl=null,Ol=!1,Vl=0,jl=null,e)throw Error(i(300));null===t||tu||null!==(t=t.dependencies)&&Mn(t)&&(tu=!0)}function Gt(t,e,n,r){zl=t;var a=0;do{if(Ul&&(jl=null),Vl=0,Ul=!1,25<=a)throw Error(i(301));if(a+=1,Gl=Il=null,null!=t.updateQueue){var o=t.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}ha.H=Ql,o=e(n,r)}while(Ul);return o}function Ot(){var t=ha.H,e=t.useState()[0];return e="function"==typeof e.then?Wt(e):e,t=t.useState()[0],(null!==Il?Il.memoizedState:null)!==t&&(zl.flags|=1024),e}function Ut(){var t=0!==Hl;return Hl=0,t}function Bt(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Ht(t){if(Ol){for(t=t.memoizedState;null!==t;){var e=t.queue;null!==e&&(e.pending=null),t=t.next}Ol=!1}Dl=0,Gl=Il=zl=null,Ul=!1,Vl=Hl=0,jl=null}function Vt(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Gl?zl.memoizedState=Gl=t:Gl=Gl.next=t,Gl}function jt(){if(null===Il){var t=zl.alternate;t=null!==t?t.memoizedState:null}else t=Il.next;var e=null===Gl?zl.memoizedState:Gl.next;if(null!==e)Gl=e,Il=t;else{if(null===t){if(null===zl.alternate)throw Error(i(467));throw Error(i(310))}t={memoizedState:(Il=t).memoizedState,baseState:Il.baseState,baseQueue:Il.baseQueue,queue:Il.queue,next:null},null===Gl?zl.memoizedState=Gl=t:Gl=Gl.next=t}return Gl}function Wt(t){var e=Vl;return Vl+=1,null===jl&&(jl=[]),t=bt(jl,t,e),e=zl,null===(null===Gl?e.memoizedState:Gl.next)&&(e=e.alternate,ha.H=null===e||null===e.memoizedState?Yl:Xl),t}function Kt(t){if(null!==t&&"object"==typeof t){if("function"==typeof t.then)return Wt(t);if(t.$$typeof===ea)return Rn(t)}throw Error(i(438,String(t)))}function qt(t){var e=null,n=zl.updateQueue;if(null!==n&&(e=n.memoCache),null==e){var r=zl.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(e={data:r.data.map((function(t){return t.slice()})),index:0})}if(null==e&&(e={data:[],index:0}),null===n&&(n=Kl(),zl.updateQueue=n),n.memoCache=e,void 0===(n=e.data[e.index]))for(n=e.data[e.index]=Array(t),r=0;r<t;r++)n[r]=la;return e.index++,n}function Yt(t,e){return"function"==typeof e?e(t):e}function Xt(t){return Qt(jt(),Il,t)}function Qt(t,e,n){var r=t.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var a=t.baseQueue,o=r.pending;if(null!==o){if(null!==a){var s=a.next;a.next=o.next,o.next=s}e.baseQueue=a=o,r.pending=null}if(o=t.baseState,null===a)t.memoizedState=o;else{var l=s=null,u=null,c=e=a.next,h=!1;do{var d=-536870913&c.lane;if(d!==c.lane?(Au&d)===d:(Dl&d)===d){var f=c.revertLane;if(0===f)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),d===yl&&(h=!0);else{if((Dl&f)===f){c=c.next,f===yl&&(h=!0);continue}d={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=d,s=o):u=u.next=d,zl.lanes|=f,Uu|=f}d=c.action,Bl&&n(o,d),o=c.hasEagerState?c.eagerState:n(o,d)}else f={lane:d,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(l=u=f,s=o):u=u.next=f,zl.lanes|=d,Uu|=d;c=c.next}while(null!==c&&c!==e);if(null===u?s=o:u.next=l,!Os(o,t.memoizedState)&&(tu=!0,h&&null!==(n=vl)))throw n;t.memoizedState=o,t.baseState=s,t.baseQueue=u,r.lastRenderedState=o}return null===a&&(r.lanes=0),[t.memoizedState,r.dispatch]}function $t(t){var e=jt(),n=e.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=t;var r=n.dispatch,a=n.pending,o=e.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{o=t(o,s.action),s=s.next}while(s!==a);Os(o,e.memoizedState)||(tu=!0),e.memoizedState=o,null===e.baseQueue&&(e.baseState=o),n.lastRenderedState=o}return[o,r]}function Jt(t,e,n){var r=zl,a=jt(),o=nl;if(o){if(void 0===n)throw Error(i(407));n=n()}else n=e();var s=!Os((Il||a).memoizedState,n);if(s&&(a.memoizedState=n,tu=!0),a=a.queue,we(ee.bind(null,r,a,t),[t]),a.getSnapshot!==e||s||null!==Gl&&1&Gl.memoizedState.tag){if(r.flags|=2048,ve(9,te.bind(null,r,a,n,e),{destroy:void 0},null),null===Nu)throw Error(i(349));o||60&Dl||Zt(r,e,n)}return n}function Zt(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},null===(e=zl.updateQueue)?(e=Kl(),zl.updateQueue=e,e.stores=[t]):null===(n=e.stores)?e.stores=[t]:n.push(t)}function te(t,e,n,r){e.value=n,e.getSnapshot=r,ne(e)&&re(t)}function ee(t,e,n){return n((function(){ne(e)&&re(t)}))}function ne(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Os(t,n)}catch(r){return!0}}function re(t){var e=X(t,2);null!==e&&qr(e,0,2)}function ie(t){var e=Vt();if("function"==typeof t){var n=t;if(t=n(),Bl){N(!0);try{n()}finally{N(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Yt,lastRenderedState:t},e}function ae(t,e,n,r){return t.baseState=n,Qt(t,Il,"function"==typeof r?r:Yt)}function oe(t,e,n,r,a){if(Ve(t))throw Error(i(485));if(null!==(t=e.action)){var o={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(t){o.listeners.push(t)}};null!==ha.T?n(!0):o.isTransition=!1,r(o),null===(n=e.pending)?(o.next=e.pending=o,se(e,o)):(o.next=n.next,e.pending=n.next=o)}}function se(t,e){var n=e.action,r=e.payload,i=t.state;if(e.isTransition){var a=ha.T,o={};ha.T=o;try{var s=n(i,r),l=ha.S;null!==l&&l(o,s),le(t,e,s)}catch(u){ce(t,e,u)}finally{ha.T=a}}else try{le(t,e,a=n(i,r))}catch(c){ce(t,e,c)}}function le(t,e,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){ue(t,e,n)}),(function(n){return ce(t,e,n)})):ue(t,e,n)}function ue(t,e,n){e.status="fulfilled",e.value=n,he(e),t.state=n,null!==(e=t.pending)&&((n=e.next)===e?t.pending=null:(n=n.next,e.next=n,se(t,n)))}function ce(t,e,n){var r=t.pending;if(t.pending=null,null!==r){r=r.next;do{e.status="rejected",e.reason=n,he(e),e=e.next}while(e!==r)}t.action=null}function he(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function de(t,e){return e}function fe(t,e){if(nl){var n=Nu.formState;if(null!==n){t:{var r=zl;if(nl){if(el){var i=Ao(el,il);if(i){el=Lo(i),r=Ro(i);break t}}U(r)}r=!1}r&&(e=n[0])}}(n=Vt()).memoizedState=n.baseState=e,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:de,lastRenderedState:e},n.queue=r,n=Ue.bind(null,zl,r),r.dispatch=n,r=ie(!1);var a=He.bind(null,zl,!1,r.queue);return i={state:e,dispatch:null,action:t,pending:null},(r=Vt()).queue=i,n=oe.bind(null,zl,i,a,n),i.dispatch=n,r.memoizedState=t,[e,n,!1]}function ge(t){return pe(jt(),Il,t)}function pe(t,e,n){e=Qt(t,e,de)[0],t=Xt(Yt)[0],e="object"==typeof e&&null!==e&&"function"==typeof e.then?Wt(e):e;var r=jt(),i=r.queue,a=i.dispatch;return n!==r.memoizedState&&(zl.flags|=2048,ve(9,me.bind(null,i,n),{destroy:void 0},null)),[e,a,t]}function me(t,e){t.action=e}function ye(t){var e=jt(),n=Il;if(null!==n)return pe(e,n,t);jt(),e=e.memoizedState;var r=(n=jt()).queue.dispatch;return n.memoizedState=t,[e,r,!1]}function ve(t,e,n,r){return t={tag:t,create:e,inst:n,deps:r,next:null},null===(e=zl.updateQueue)&&(e=Kl(),zl.updateQueue=e),null===(n=e.lastEffect)?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t),t}function be(){return jt().memoizedState}function _e(t,e,n,r){var i=Vt();zl.flags|=t,i.memoizedState=ve(1|e,n,{destroy:void 0},void 0===r?null:r)}function Se(t,e,n,r){var i=jt();r=void 0===r?null:r;var a=i.memoizedState.inst;null!==Il&&null!==r&&Dt(r,Il.memoizedState.deps)?i.memoizedState=ve(e,n,a,r):(zl.flags|=t,i.memoizedState=ve(1|e,n,a,r))}function xe(t,e){_e(8390656,8,t,e)}function we(t,e){Se(2048,8,t,e)}function Ce(t,e){return Se(4,2,t,e)}function ke(t,e){return Se(4,4,t,e)}function Pe(t,e){if("function"==typeof e){t=t();var n=e(t);return function(){"function"==typeof n?n():e(null)}}if(null!=e)return t=t(),e.current=t,function(){e.current=null}}function Te(t,e,n){n=null!=n?n.concat([t]):null,Se(4,4,Pe.bind(null,e,t),n)}function Ee(){}function Fe(t,e){var n=jt();e=void 0===e?null:e;var r=n.memoizedState;return null!==e&&Dt(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function Ne(t,e){var n=jt();e=void 0===e?null:e;var r=n.memoizedState;if(null!==e&&Dt(e,r[1]))return r[0];if(r=t(),Bl){N(!0);try{t()}finally{N(!1)}}return n.memoizedState=[r,e],r}function Me(t,e,n){return void 0===n||1073741824&Dl?t.memoizedState=e:(t.memoizedState=n,t=Kr(),zl.lanes|=t,Uu|=t,n)}function Ae(t,e,n,r){return Os(n,e)?n:null!==Nl.current?(t=Me(t,n,r),Os(t,e)||(tu=!0),t):42&Dl?(t=Kr(),zl.lanes|=t,Uu|=t,e):(tu=!0,t.memoizedState=n)}function Re(t,e,n,r,i){var a=Ia();za(0!==a&&8>a?a:8);var o,s,l,u=ha.T,c={};ha.T=c,He(t,!1,e,n);try{var h=i(),d=ha.S;null!==d&&d(c,h),null!==h&&"object"==typeof h&&"function"==typeof h.then?Be(t,e,(o=r,s=[],l={status:"pending",value:null,reason:null,then:function(t){s.push(t)}},h.then((function(){l.status="fulfilled",l.value=o;for(var t=0;t<s.length;t++)(0,s[t])(o)}),(function(t){for(l.status="rejected",l.reason=t,t=0;t<s.length;t++)(0,s[t])(void 0)})),l),Wr()):Be(t,e,r,Wr())}catch(f){Be(t,e,{then:function(){},status:"rejected",reason:f},Wr())}finally{za(a),ha.T=u}}function Le(t){var e=t.memoizedState;if(null!==e)return e;var n={};return(e={memoizedState:Ka,baseState:Ka,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Yt,lastRenderedState:Ka},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Yt,lastRenderedState:n},next:null},t.memoizedState=e,null!==(t=t.alternate)&&(t.memoizedState=e),e}function De(){return Rn(qa)}function ze(){return jt().memoizedState}function Ie(){return jt().memoizedState}function Ge(t){for(var e=t.return;null!==e;){switch(e.tag){case 24:case 3:var n=Wr(),r=ut(e,t=lt(n),n);return null!==r&&(qr(r,0,n),ct(r,e,n)),e={cache:zn()},void(t.payload=e)}e=e.return}}function Oe(t,e,n){var r=Wr();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ve(t)?je(e,n):null!==(n=Y(t,e,n,r))&&(qr(n,0,r),We(n,e,r))}function Ue(t,e,n){Be(t,e,n,Wr())}function Be(t,e,n,r){var i={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ve(t))je(e,i);else{var a=t.alternate;if(0===t.lanes&&(null===a||0===a.lanes)&&null!==(a=e.lastRenderedReducer))try{var o=e.lastRenderedState,s=a(o,n);if(i.hasEagerState=!0,i.eagerState=s,Os(s,o))return q(t,e,i,0),null===Nu&&K(),!1}catch(l){}if(null!==(n=Y(t,e,i,r)))return qr(n,0,r),We(n,e,r),!0}return!1}function He(t,e,n,r){if(r={lane:2,revertLane:it(),action:r,hasEagerState:!1,eagerState:null,next:null},Ve(t)){if(e)throw Error(i(479))}else null!==(e=Y(t,n,r,2))&&qr(e,0,2)}function Ve(t){var e=t.alternate;return t===zl||null!==e&&e===zl}function je(t,e){Ul=Ol=!0;var n=t.pending;null===n?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function We(t,e,n){if(4194176&n){var r=e.lanes;n|=r&=t.pendingLanes,e.lanes=n,E(t,n)}}function Ke(t,e,n,r){n=null==(n=n(r,e=t.memoizedState))?e:Ki({},e,n),t.memoizedState=n,0===t.lanes&&(t.updateQueue.baseState=n)}function qe(t,e,n,r,i,a,o){return"function"==typeof(t=t.stateNode).shouldComponentUpdate?t.shouldComponentUpdate(r,a,o):!(e.prototype&&e.prototype.isPureReactComponent&&mt(n,r)&&mt(i,a))}function Xe(t,e,n,r){t=e.state,"function"==typeof e.componentWillReceiveProps&&e.componentWillReceiveProps(n,r),"function"==typeof e.UNSAFE_componentWillReceiveProps&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&$l.enqueueReplaceState(e,e.state,null)}function Qe(t,e){var n=e;if("ref"in e)for(var r in n={},e)"ref"!==r&&(n[r]=e[r]);if(t=t.defaultProps)for(var i in n===e&&(n=Ki({},n)),t)void 0===n[i]&&(n[i]=t[i]);return n}function $e(t,e){try{(0,t.onUncaughtError)(e.value,{componentStack:e.stack})}catch(n){setTimeout((function(){throw n}))}}function Je(t,e,n){try{(0,t.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===e.tag?e.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function Ze(t,e,n){return(n=lt(n)).tag=3,n.payload={element:null},n.callback=function(){$e(t,e)},n}function tn(t){return(t=lt(t)).tag=3,t}function en(t,e,n,r){var i=n.type.getDerivedStateFromError;if("function"==typeof i){var a=r.value;t.payload=function(){return i(a)},t.callback=function(){Je(e,n,r)}}var o=n.stateNode;null!==o&&"function"==typeof o.componentDidCatch&&(t.callback=function(){Je(e,n,r),"function"!=typeof i&&(null===$u?$u=new Set([this]):$u.add(this));var t=r.stack;this.componentDidCatch(r.value,{componentStack:null!==t?t:""})})}function nn(t,e,n,r){e.child=null===t?Fl(e,null,n,r):El(e,t.child,n,r)}function rn(t,e,n,r,i){n=n.render;var a=e.ref;if("ref"in r){var o={};for(var s in r)"ref"!==s&&(o[s]=r[s])}else o=r;return An(e),r=zt(t,e,n,o,a,i),s=Ut(),null===t||tu?(nl&&s&&L(e),e.flags|=1,nn(t,e,r,i),e.child):(Bt(t,e,i),wn(t,e,i))}function an(t,e,n,r,i){if(null===t){var a=n.type;return"function"!=typeof a||Pi(a)||void 0!==a.defaultProps||null!==n.compare?((t=Fi(n.type,null,r,e,e.mode,i)).ref=e.ref,t.return=e,e.child=t):(e.tag=15,e.type=a,on(t,e,a,r,i))}if(a=t.child,!Cn(t,i)){var o=a.memoizedProps;if((n=null!==(n=n.compare)?n:mt)(o,r)&&t.ref===e.ref)return wn(t,e,i)}return e.flags|=1,(t=Ti(a,r)).ref=e.ref,t.return=e,e.child=t}function on(t,e,n,r,i){if(null!==t){var a=t.memoizedProps;if(mt(a,r)&&t.ref===e.ref){if(tu=!1,e.pendingProps=r=a,!Cn(t,i))return e.lanes=t.lanes,wn(t,e,i);131072&t.flags&&(tu=!0)}}return cn(t,e,n,r,i)}function sn(t,e,n){var r=e.pendingProps,i=r.children,a=!!(2&e.stateNode._pendingVisibility),o=null!==t?t.memoizedState:null;if(un(t,e),"hidden"===r.mode||a){if(128&e.flags){if(r=null!==o?o.baseLanes|n:n,null!==t){for(i=e.child=t.child,a=0;null!==i;)a=a|i.lanes|i.childLanes,i=i.sibling;e.childLanes=a&~r}else e.childLanes=0,e.child=null;return ln(t,e,r,n)}if(!(536870912&n))return e.lanes=e.childLanes=536870912,ln(t,e,null!==o?o.baseLanes|n:n,n);e.memoizedState={baseLanes:0,cachePool:null},null!==t&&On(0,null!==o?o.cachePool:null),null!==o?Pt(e,o):Tt(),Nt(e)}else null!==o?(On(0,o.cachePool),Pt(e,o),Mt(),e.memoizedState=null):(null!==t&&On(0,null),Tt(),Mt());return nn(t,e,i,n),e.child}function ln(t,e,n,r){var i=Gn();return i=null===i?null:{parent:Na?lu._currentValue:lu._currentValue2,pool:i},e.memoizedState={baseLanes:n,cachePool:i},null!==t&&On(0,null),Tt(),Nt(e),null!==t&&Nn(t,e,r,!0),null}function un(t,e){var n=e.ref;if(null===n)null!==t&&null!==t.ref&&(e.flags|=2097664);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(i(284));null!==t&&t.ref===n||(e.flags|=2097664)}}function cn(t,e,n,r,i){return An(e),n=zt(t,e,n,r,void 0,i),r=Ut(),null===t||tu?(nl&&r&&L(e),e.flags|=1,nn(t,e,n,i),e.child):(Bt(t,e,i),wn(t,e,i))}function hn(t,e,n,r,i,a){return An(e),e.updateQueue=null,n=Gt(e,r,n,i),It(t),r=Ut(),null===t||tu?(nl&&r&&L(e),e.flags|=1,nn(t,e,n,a),e.child):(Bt(t,e,a),wn(t,e,a))}function dn(t,e,n,r,i){if(An(e),null===e.stateNode){var a=_s,o=n.contextType;"object"==typeof o&&null!==o&&(a=Rn(o)),a=new n(r,a),e.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=$l,e.stateNode=a,a._reactInternals=e,(a=e.stateNode).props=r,a.state=e.memoizedState,a.refs={},ot(e),o=n.contextType,a.context="object"==typeof o&&null!==o?Rn(o):_s,a.state=e.memoizedState,"function"==typeof(o=n.getDerivedStateFromProps)&&(Ke(e,n,o,r),a.state=e.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(o=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),o!==a.state&&$l.enqueueReplaceState(a,a.state,null),ft(e,r,a,i),dt(),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308),r=!0}else if(null===t){a=e.stateNode;var s=e.memoizedProps,l=Qe(n,s);a.props=l;var u=a.context,c=n.contextType;o=_s,"object"==typeof c&&null!==c&&(o=Rn(c));var h=n.getDerivedStateFromProps;c="function"==typeof h||"function"==typeof a.getSnapshotBeforeUpdate,s=e.pendingProps!==s,c||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(s||u!==o)&&Xe(e,a,r,o),bl=!1;var d=e.memoizedState;a.state=d,ft(e,r,a,i),dt(),u=e.memoizedState,s||d!==u||bl?("function"==typeof h&&(Ke(e,n,h,r),u=e.memoizedState),(l=bl||qe(e,n,l,r,d,u,o))?(c||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(e.flags|=4194308)):("function"==typeof a.componentDidMount&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=u),a.props=r,a.state=u,a.context=o,r=l):("function"==typeof a.componentDidMount&&(e.flags|=4194308),r=!1)}else{a=e.stateNode,st(t,e),c=Qe(n,o=e.memoizedProps),a.props=c,h=e.pendingProps,d=a.context,u=n.contextType,l=_s,"object"==typeof u&&null!==u&&(l=Rn(u)),(u="function"==typeof(s=n.getDerivedStateFromProps)||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(o!==h||d!==l)&&Xe(e,a,r,l),bl=!1,d=e.memoizedState,a.state=d,ft(e,r,a,i),dt();var f=e.memoizedState;o!==h||d!==f||bl||null!==t&&null!==t.dependencies&&Mn(t.dependencies)?("function"==typeof s&&(Ke(e,n,s,r),f=e.memoizedState),(c=bl||qe(e,n,c,r,d,f,l)||null!==t&&null!==t.dependencies&&Mn(t.dependencies))?(u||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,f,l),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,f,l)),"function"==typeof a.componentDidUpdate&&(e.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(e.flags|=1024)):("function"!=typeof a.componentDidUpdate||o===t.memoizedProps&&d===t.memoizedState||(e.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===t.memoizedProps&&d===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=f),a.props=r,a.state=f,a.context=l,r=c):("function"!=typeof a.componentDidUpdate||o===t.memoizedProps&&d===t.memoizedState||(e.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||o===t.memoizedProps&&d===t.memoizedState||(e.flags|=1024),r=!1)}return a=r,un(t,e),r=!!(128&e.flags),a||r?(a=e.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:a.render(),e.flags|=1,null!==t&&r?(e.child=El(e,t.child,null,i),e.child=El(e,null,n,i)):nn(t,e,n,i),e.memoizedState=a.state,t=e.child):t=wn(t,e,i),t}function fn(t,e,n,r){return j(),e.flags|=256,nn(t,e,n,r),e.child}function gn(t){return{baseLanes:t,cachePool:Un()}}function pn(t,e,n){return t=null!==t?t.childLanes&~n:0,e&&(t|=Vu),t}function mn(t,n,r){var a,o=n.pendingProps,s=!1,l=!!(128&n.flags);if((a=l)||(a=(null===t||null!==t.memoizedState)&&!!(2&Ll.current)),a&&(s=!0,n.flags&=-129),a=!!(32&n.flags),n.flags&=-33,null===t){if(nl){if(s?Ft(n):Mt(),nl){var u,c=el;(u=c)&&(null!==(c=Uo(c,il))?(n.memoizedState={dehydrated:c,treeContext:null!==qs?{id:Ys,overflow:Xs}:null,retryLane:536870912},(u=e(18,null,null,0)).stateNode=c,u.return=n,n.child=u,tl=n,el=null,u=!0):u=!1),u||U(n)}if(null!==(c=n.memoizedState)&&null!==(c=c.dehydrated))return Fo(c)?n.lanes=16:n.lanes=536870912,null;At(n)}return c=o.children,o=o.fallback,s?(Mt(),c=vn({mode:"hidden",children:c},s=n.mode),o=Ni(o,s,r,null),c.return=n,o.return=n,c.sibling=o,n.child=c,(s=n.child).memoizedState=gn(r),s.childLanes=pn(t,a,r),n.memoizedState=eu,o):(Ft(n),yn(n,c))}if(null!==(u=t.memoizedState)&&null!==(c=u.dehydrated)){if(l)256&n.flags?(Ft(n),n.flags&=-257,n=bn(t,n,r)):null!==n.memoizedState?(Mt(),n.child=t.child,n.flags|=128,n=null):(Mt(),s=o.fallback,c=n.mode,o=vn({mode:"visible",children:o.children},c),(s=Ni(s,c,r,null)).flags|=2,o.return=n,s.return=n,o.sibling=s,n.child=o,El(n,t.child,null,r),(o=n.child).memoizedState=gn(r),o.childLanes=pn(t,a,r),n.memoizedState=eu,n=s);else if(Ft(n),Fo(c))a=No(c).digest,(o=Error(i(419))).stack="",o.digest=a,W({value:o,source:null,stack:null}),n=bn(t,n,r);else if(tu||Nn(t,n,r,!1),a=0!==(r&t.childLanes),tu||a){if(null!==(a=Nu)){if(42&(o=r&-r))o=1;else switch(o){case 2:o=1;break;case 8:o=4;break;case 32:o=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:o=64;break;case 268435456:o=134217728;break;default:o=0}if(0!==(o=0!==(o&(a.suspendedLanes|r))?0:o)&&o!==u.retryLane)throw u.retryLane=o,X(t,o),qr(a,0,o),Zl}Eo(c)||oi(),n=bn(t,n,r)}else Eo(c)?(n.flags|=128,n.child=t.child,n=wi.bind(null,t),Mo(c,n),n=null):(t=u.treeContext,Ra&&(el=Io(c),tl=n,nl=!0,rl=null,il=!1,null!==t&&(Ws[Ks++]=Ys,Ws[Ks++]=Xs,Ws[Ks++]=qs,Ys=t.id,Xs=t.overflow,qs=n)),(n=yn(n,o.children)).flags|=4096);return n}return s?(Mt(),s=o.fallback,c=n.mode,l=(u=t.child).sibling,(o=Ti(u,{mode:"hidden",children:o.children})).subtreeFlags=31457280&u.subtreeFlags,null!==l?s=Ti(l,s):(s=Ni(s,c,r,null)).flags|=2,s.return=n,o.return=n,o.sibling=s,n.child=o,o=s,s=n.child,null===(c=t.child.memoizedState)?c=gn(r):(null!==(u=c.cachePool)?(l=Na?lu._currentValue:lu._currentValue2,u=u.parent!==l?{parent:l,pool:l}:u):u=Un(),c={baseLanes:c.baseLanes|r,cachePool:u}),s.memoizedState=c,s.childLanes=pn(t,a,r),n.memoizedState=eu,o):(Ft(n),t=(r=t.child).sibling,(r=Ti(r,{mode:"visible",children:o.children})).return=n,r.sibling=null,null!==t&&(null===(a=n.deletions)?(n.deletions=[t],n.flags|=16):a.push(t)),n.child=r,n.memoizedState=null,r)}function yn(t,e){return(e=vn({mode:"visible",children:e},t.mode)).return=t,t.child=e}function vn(t,e){return Mi(t,e,0,null)}function bn(t,e,n){return El(e,t.child,null,n),(t=yn(e,e.pendingProps.children)).flags|=2,e.memoizedState=null,t}function _n(t,e,n){t.lanes|=e;var r=t.alternate;null!==r&&(r.lanes|=e),En(t.return,e,n)}function Sn(t,e,n,r,i){var a=t.memoizedState;null===a?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(a.isBackwards=e,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=i)}function xn(t,e,n){var r=e.pendingProps,i=r.revealOrder,a=r.tail;if(nn(t,e,r.children,n),2&(r=Ll.current))r=1&r|2,e.flags|=128;else{if(null!==t&&128&t.flags)t:for(t=e.child;null!==t;){if(13===t.tag)null!==t.memoizedState&&_n(t,n,e);else if(19===t.tag)_n(t,n,e);else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;null===t.sibling;){if(null===t.return||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}switch(v(Ll,r),i){case"forwards":for(n=e.child,i=null;null!==n;)null!==(t=n.alternate)&&null===Rt(t)&&(i=n),n=n.sibling;null===(n=i)?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),Sn(e,!1,i,n,a);break;case"backwards":for(n=null,i=e.child,e.child=null;null!==i;){if(null!==(t=i.alternate)&&null===Rt(t)){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}Sn(e,!0,n,null,a);break;case"together":Sn(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function wn(t,e,n){if(null!==t&&(e.dependencies=t.dependencies),Uu|=e.lanes,0===(n&e.childLanes)){if(null===t)return null;if(Nn(t,e,n,!1),0===(n&e.childLanes))return null}if(null!==t&&e.child!==t.child)throw Error(i(153));if(null!==e.child){for(n=Ti(t=e.child,t.pendingProps),e.child=n,n.return=e;null!==t.sibling;)t=t.sibling,(n=n.sibling=Ti(t,t.pendingProps)).return=e;n.sibling=null}return e.child}function Cn(t,e){return 0!==(t.lanes&e)||!(null===(t=t.dependencies)||!Mn(t))}function kn(t,e,n){if(null!==t)if(t.memoizedProps!==e.pendingProps)tu=!0;else{if(!(Cn(t,n)||128&e.flags))return tu=!1,function(t,e,n){switch(e.tag){case 3:z(e,e.stateNode.containerInfo),Pn(0,lu,t.memoizedState.cache),j();break;case 27:case 5:G(e);break;case 4:z(e,e.stateNode.containerInfo);break;case 10:Pn(0,e.type,e.memoizedProps.value);break;case 13:var r=e.memoizedState;if(null!==r)return null!==r.dehydrated?(Ft(e),e.flags|=128,null):0!==(n&e.child.childLanes)?mn(t,e,n):(Ft(e),null!==(t=wn(t,e,n))?t.sibling:null);Ft(e);break;case 19:var i=!!(128&t.flags);if((r=0!==(n&e.childLanes))||(Nn(t,e,n,!1),r=0!==(n&e.childLanes)),i){if(r)return xn(t,e,n);e.flags|=128}if(null!==(i=e.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),v(Ll,Ll.current),r)break;return null;case 22:case 23:return e.lanes=0,sn(t,e,n);case 24:Pn(0,lu,t.memoizedState.cache)}return wn(t,e,n)}(t,e,n);tu=!!(131072&t.flags)}else tu=!1,nl&&1048576&e.flags&&R(e,js,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var r=e.elementType,a=r._init;if(r=a(r._payload),e.type=r,"function"!=typeof r){if(null!=r){if((a=r.$$typeof)===na){e.tag=11,e=rn(null,e,r,t,n);break t}if(a===aa){e.tag=14,e=an(null,e,r,t,n);break t}}throw e=o(r)||r,Error(i(306,e,""))}Pi(r)?(t=Qe(r,t),e.tag=1,e=dn(null,e,r,t,n)):(e.tag=0,e=cn(null,e,r,t,n))}return e;case 0:return cn(t,e,e.type,e.pendingProps,n);case 1:return dn(t,e,r=e.type,a=Qe(r,e.pendingProps),n);case 3:t:{if(z(e,e.stateNode.containerInfo),null===t)throw Error(i(387));var s=e.pendingProps;r=(a=e.memoizedState).element,st(t,e),ft(e,s,null,n);var l=e.memoizedState;if(s=l.cache,Pn(0,lu,s),s!==a.cache&&Fn(e,[lu],n,!0),dt(),s=l.element,Ra&&a.isDehydrated){if(a={element:s,isDehydrated:!1,cache:l.cache},e.updateQueue.baseState=a,e.memoizedState=a,256&e.flags){e=fn(t,e,s,n);break t}if(s!==r){W(r=M(Error(i(424)),e)),e=fn(t,e,s,n);break t}for(Ra&&(el=zo(e.stateNode.containerInfo),tl=e,nl=!0,rl=null,il=!0),n=Fl(e,null,s,n),e.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(j(),s===r){e=wn(t,e,n);break t}nn(t,e,s,n)}e=e.child}return e;case 26:if(Jo)return un(t,e),null===t?(n=es(e.type,null,e.pendingProps,null))?e.memoizedState=n:nl||(e.stateNode=ss(e.type,e.pendingProps,Js.current,e)):e.memoizedState=es(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:if(ds)return G(e),null===t&&ds&&nl&&(r=e.stateNode=fs(e.type,e.pendingProps,Js.current,Qs.current,!1),tl=e,il=!0,el=Do(r)),r=e.pendingProps.children,null!==t||nl?nn(t,e,r,n):e.child=El(e,null,r,n),un(t,e),e.child;case 5:return null===t&&nl&&(Qo(e.type,e.pendingProps,Qs.current),(a=r=el)&&(null!==(r=Go(r,e.type,e.pendingProps,il))?(e.stateNode=r,tl=e,el=Do(r),il=!1,a=!0):a=!1),a||U(e)),G(e),a=e.type,s=e.pendingProps,l=null!==t?t.memoizedProps:null,r=s.children,ka(a,s)?r=null:null!==l&&ka(a,l)&&(e.flags|=32),null!==e.memoizedState&&(a=zt(t,e,Ot,null,null,n),Na?qa._currentValue=a:qa._currentValue2=a),un(t,e),nn(t,e,r,n),e.child;case 6:return null===t&&nl&&($o(e.pendingProps,Qs.current),(t=n=el)&&(null!==(n=Oo(n,e.pendingProps,il))?(e.stateNode=n,tl=e,el=null,t=!0):t=!1),t||U(e)),null;case 13:return mn(t,e,n);case 4:return z(e,e.stateNode.containerInfo),r=e.pendingProps,null===t?e.child=El(e,null,r,n):nn(t,e,r,n),e.child;case 11:return rn(t,e,e.type,e.pendingProps,n);case 7:return nn(t,e,e.pendingProps,n),e.child;case 8:case 12:return nn(t,e,e.pendingProps.children,n),e.child;case 10:return r=e.pendingProps,Pn(0,e.type,r.value),nn(t,e,r.children,n),e.child;case 9:return a=e.type._context,r=e.pendingProps.children,An(e),r=r(a=Rn(a)),e.flags|=1,nn(t,e,r,n),e.child;case 14:return an(t,e,e.type,e.pendingProps,n);case 15:return on(t,e,e.type,e.pendingProps,n);case 19:return xn(t,e,n);case 22:return sn(t,e,n);case 24:return An(e),r=Rn(lu),null===t?(null===(a=Gn())&&(a=Nu,s=zn(),a.pooledCache=s,s.refCount++,null!==s&&(a.pooledCacheLanes|=n),a=s),e.memoizedState={parent:r,cache:a},ot(e),Pn(0,lu,a)):(0!==(t.lanes&n)&&(st(t,e),ft(e,null,null,n),dt()),a=t.memoizedState,s=e.memoizedState,a.parent!==r?(a={parent:r,cache:r},e.memoizedState=a,0===e.lanes&&(e.memoizedState=e.updateQueue.baseState=a),Pn(0,lu,r)):(r=s.cache,Pn(0,lu,r),r!==a.cache&&Fn(e,[lu],n,!0))),nn(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(i(156,e.tag))}function Pn(t,e,n){Na?(v(nu,e._currentValue),e._currentValue=n):(v(nu,e._currentValue2),e._currentValue2=n)}function Tn(t){var e=nu.current;Na?t._currentValue=e:t._currentValue2=e,y(nu)}function En(t,e,n){for(;null!==t;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,null!==r&&(r.childLanes|=e)):null!==r&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function Fn(t,e,n,r){var a=t.child;for(null!==a&&(a.return=t);null!==a;){var o=a.dependencies;if(null!==o){var s=a.child;o=o.firstContext;t:for(;null!==o;){var l=o;o=a;for(var u=0;u<e.length;u++)if(l.context===e[u]){o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),En(o.return,n,t),r||(s=null);break t}o=l.next}}else if(18===a.tag){if(null===(s=a.return))throw Error(i(341));s.lanes|=n,null!==(o=s.alternate)&&(o.lanes|=n),En(s,n,t),s=null}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}}function Nn(t,e,n,r){t=null;for(var a=e,o=!1;null!==a;){if(!o)if(524288&a.flags)o=!0;else if(262144&a.flags)break;if(10===a.tag){var s=a.alternate;if(null===s)throw Error(i(387));if(null!==(s=s.memoizedProps)){var l=a.type;Os(a.pendingProps.value,s.value)||(null!==t?t.push(l):t=[l])}}else if(a===Zs.current){if(null===(s=a.alternate))throw Error(i(387));s.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==t?t.push(qa):t=[qa])}a=a.return}null!==t&&Fn(e,t,n,r),e.flags|=262144}function Mn(t){for(t=t.firstContext;null!==t;){var e=t.context;if(!Os(Na?e._currentValue:e._currentValue2,t.memoizedValue))return!0;t=t.next}return!1}function An(t){ru=t,iu=null,null!==(t=t.dependencies)&&(t.firstContext=null)}function Rn(t){return Dn(ru,t)}function Ln(t,e){return null===ru&&An(t),Dn(t,e)}function Dn(t,e){var n=Na?e._currentValue:e._currentValue2;if(e={context:e,memoizedValue:n,next:null},null===iu){if(null===t)throw Error(i(308));iu=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else iu=iu.next=e;return n}function zn(){return{controller:new au,data:new Map,refCount:0}}function In(t){t.refCount--,0===t.refCount&&ou(su,(function(){t.controller.abort()}))}function Gn(){var t=cu.current;return null!==t?t:Nu.pooledCache}function On(t,e){v(cu,null===e?cu.current:e.pool)}function Un(){var t=Gn();return null===t?null:{parent:Na?lu._currentValue:lu._currentValue2,pool:t}}function Bn(t){t.flags|=4}function Hn(t,e){if(null!==t&&t.child===e.child)return!1;if(16&e.flags)return!0;for(t=e.child;null!==t;){if(13878&t.flags||13878&t.subtreeFlags)return!0;t=t.sibling}return!1}function Vn(t,e,n,r){if(Ma)for(n=e.child;null!==n;){if(5===n.tag||6===n.tag)wa(t,n.stateNode);else if(!(4===n.tag||ds&&27===n.tag)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}else if(Aa)for(var i=e.child;null!==i;){if(5===i.tag){var a=i.stateNode;n&&r&&(a=Po(a,i.type,i.memoizedProps)),wa(t,a)}else if(6===i.tag)a=i.stateNode,n&&r&&(a=To(a,i.memoizedProps)),wa(t,a);else if(4!==i.tag)if(22===i.tag&&null!==i.memoizedState)null!==(a=i.child)&&(a.return=i),Vn(t,i,!0,!0);else if(null!==i.child){i.child.return=i,i=i.child;continue}if(i===e)break;for(;null===i.sibling;){if(null===i.return||i.return===e)return;i=i.return}i.sibling.return=i.return,i=i.sibling}}function jn(t,e,n,r){if(Aa)for(var i=e.child;null!==i;){if(5===i.tag){var a=i.stateNode;n&&r&&(a=Po(a,i.type,i.memoizedProps)),wo(t,a)}else if(6===i.tag)a=i.stateNode,n&&r&&(a=To(a,i.memoizedProps)),wo(t,a);else if(4!==i.tag)if(22===i.tag&&null!==i.memoizedState)null!==(a=i.child)&&(a.return=i),jn(t,i,!(null!==i.memoizedProps&&"manual"===i.memoizedProps.mode),!0);else if(null!==i.child){i.child.return=i,i=i.child;continue}if(i===e)break;for(;null===i.sibling;){if(null===i.return||i.return===e)return;i=i.return}i.sibling.return=i.return,i=i.sibling}}function Wn(t,e){if(Aa&&Hn(t,e)){var n=(t=e.stateNode).containerInfo,r=xo();jn(r,e,!1,!1),t.pendingChildren=r,Bn(e),Co(n,r)}}function Kn(t,e,n,r){if(Ma)t.memoizedProps!==r&&Bn(e);else if(Aa){var i=t.stateNode,a=t.memoizedProps;if((t=Hn(t,e))||a!==r){var o=Qs.current;(a=So(i,n,a,r,!t,null))===i?e.stateNode=i:(Ca(a,n,r,o)&&Bn(e),e.stateNode=a,t?Vn(a,e,!1,!1):Bn(e))}else e.stateNode=i}}function qn(t,e,n){if(Ba(e,n)){if(t.flags|=16777216,!Ha(e,n)){if(!ri())throw kl=Cl,wl;t.flags|=8192}}else t.flags&=-16777217}function Yn(t,e){if(us(e)){if(t.flags|=16777216,!cs(e)){if(!ri())throw kl=Cl,wl;t.flags|=8192}}else t.flags&=-16777217}function Xn(t,e){null!==e&&(t.flags|=4),16384&t.flags&&(e=22!==t.tag?C():536870912,t.lanes|=e,ju|=e)}function Qn(t,e){if(!nl)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;null!==e;)null!==e.alternate&&(n=e),e=e.sibling;null===n?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?e||null===t.tail?t.tail=null:t.tail.sibling=null:r.sibling=null}}function $n(t){var e=null!==t.alternate&&t.alternate.child===t.child,n=0,r=0;if(e)for(var i=t.child;null!==i;)n|=i.lanes|i.childLanes,r|=31457280&i.subtreeFlags,r|=31457280&i.flags,i.return=t,i=i.sibling;else for(i=t.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function Jn(t,e,n){var r=e.pendingProps;switch(D(e),e.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return $n(e),null;case 3:return n=e.stateNode,r=null,null!==t&&(r=t.memoizedState.cache),e.memoizedState.cache!==r&&(e.flags|=2048),Tn(lu),I(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==t&&null!==t.child||(V(e)?Bn(e):null===t||t.memoizedState.isDehydrated&&!(256&e.flags)||(e.flags|=1024,null!==rl&&(Xr(rl),rl=null))),Wn(t,e),$n(e),null;case 26:if(Jo){n=e.type;var a=e.memoizedState;return null===t?(Bn(e),null!==a?($n(e),Yn(e,a)):($n(e),qn(e,n,r))):a?a!==t.memoizedState?(Bn(e),$n(e),Yn(e,a)):($n(e),e.flags&=-16777217):(Ma?t.memoizedProps!==r&&Bn(e):Kn(t,e,n,r),$n(e),qn(e,n,r)),null}case 27:if(ds){if(O(e),n=Js.current,a=e.type,null!==t&&null!=e.stateNode)Ma?t.memoizedProps!==r&&Bn(e):Kn(t,e,a,r);else{if(!r){if(null===e.stateNode)throw Error(i(166));return $n(e),null}t=Qs.current,V(e)?B(e,t):(t=fs(a,r,n,t,!0),e.stateNode=t,Bn(e))}return $n(e),null}case 5:if(O(e),n=e.type,null!==t&&null!=e.stateNode)Kn(t,e,n,r);else{if(!r){if(null===e.stateNode)throw Error(i(166));return $n(e),null}t=Qs.current,V(e)?B(e,t):(Vn(a=xa(n,r,Js.current,t,e),e,!1,!1),e.stateNode=a,Ca(a,n,r,t)&&Bn(e))}return $n(e),qn(e,e.type,e.pendingProps),null;case 6:if(t&&null!=e.stateNode)n=t.memoizedProps,Ma?n!==r&&Bn(e):Aa&&(n!==r?(e.stateNode=Pa(r,Js.current,Qs.current,e),Bn(e)):e.stateNode=t.stateNode);else{if("string"!=typeof r&&null===e.stateNode)throw Error(i(166));if(t=Js.current,n=Qs.current,V(e)){if(!Ra)throw Error(i(176));if(t=e.stateNode,n=e.memoizedProps,r=null,null!==(a=tl))switch(a.tag){case 27:case 5:r=a.memoizedProps}Ho(t,n,e,r)||U(e)}else e.stateNode=Pa(r,t,n,e)}return $n(e),null;case 13:if(r=e.memoizedState,null===t||null!==t.memoizedState&&null!==t.memoizedState.dehydrated){if(a=V(e),null!==r&&null!==r.dehydrated){if(null===t){if(!a)throw Error(i(318));if(!Ra)throw Error(i(344));if(!(a=null!==(a=e.memoizedState)?a.dehydrated:null))throw Error(i(317));Vo(a,e)}else j(),!(128&e.flags)&&(e.memoizedState=null),e.flags|=4;$n(e),a=!1}else null!==rl&&(Xr(rl),rl=null),a=!0;if(!a)return 256&e.flags?(At(e),e):(At(e),null)}if(At(e),128&e.flags)return e.lanes=n,e;if(n=null!==r,t=null!==t&&null!==t.memoizedState,n){a=null,null!==(r=e.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var o=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(o=r.memoizedState.cachePool.pool),o!==a&&(r.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Xn(e,e.updateQueue),$n(e),null;case 4:return I(),Wn(t,e),null===t&&Da(e.stateNode.containerInfo),$n(e),null;case 10:return Tn(e.type),$n(e),null;case 19:if(y(Ll),null===(a=e.memoizedState))return $n(e),null;if(r=!!(128&e.flags),null===(o=a.rendering))if(r)Qn(a,!1);else{if(0!==Ou||null!==t&&128&t.flags)for(t=e.child;null!==t;){if(null!==(o=Rt(t))){for(e.flags|=128,Qn(a,!1),t=o.updateQueue,e.updateQueue=t,Xn(e,t),e.subtreeFlags=0,t=n,n=e.child;null!==n;)Ei(n,t),n=n.sibling;return v(Ll,1&Ll.current|2),e.child}t=t.sibling}null!==a.tail&&Ns()>Xu&&(e.flags|=128,r=!0,Qn(a,!1),e.lanes=4194304)}else{if(!r)if(null!==(t=Rt(o))){if(e.flags|=128,r=!0,t=t.updateQueue,e.updateQueue=t,Xn(e,t),Qn(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate&&!nl)return $n(e),null}else 2*Ns()-a.renderingStartTime>Xu&&536870912!==n&&(e.flags|=128,r=!0,Qn(a,!1),e.lanes=4194304);a.isBackwards?(o.sibling=e.child,e.child=o):(null!==(t=a.last)?t.sibling=o:e.child=o,a.last=o)}return null!==a.tail?(e=a.tail,a.rendering=e,a.tail=e.sibling,a.renderingStartTime=Ns(),e.sibling=null,t=Ll.current,v(Ll,r?1&t|2:1&t),e):($n(e),null);case 22:case 23:return At(e),Et(),r=null!==e.memoizedState,null!==t?null!==t.memoizedState!==r&&(e.flags|=8192):r&&(e.flags|=8192),r?!!(536870912&n)&&!(128&e.flags)&&($n(e),6&e.subtreeFlags&&(e.flags|=8192)):$n(e),null!==(n=e.updateQueue)&&Xn(e,n.retryQueue),n=null,null!==t&&null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(n=t.memoizedState.cachePool.pool),r=null,null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(r=e.memoizedState.cachePool.pool),r!==n&&(e.flags|=2048),null!==t&&y(cu),null;case 24:return n=null,null!==t&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Tn(lu),$n(e),null;case 25:return null}throw Error(i(156,e.tag))}function Zn(t,e){switch(D(e),e.tag){case 1:return 65536&(t=e.flags)?(e.flags=-65537&t|128,e):null;case 3:return Tn(lu),I(),65536&(t=e.flags)&&!(128&t)?(e.flags=-65537&t|128,e):null;case 26:case 27:case 5:return O(e),null;case 13:if(At(e),null!==(t=e.memoizedState)&&null!==t.dehydrated){if(null===e.alternate)throw Error(i(340));j()}return 65536&(t=e.flags)?(e.flags=-65537&t|128,e):null;case 19:return y(Ll),null;case 4:return I(),null;case 10:return Tn(e.type),null;case 22:case 23:return At(e),Et(),null!==t&&y(cu),65536&(t=e.flags)?(e.flags=-65537&t|128,e):null;case 24:return Tn(lu),null;default:return null}}function tr(t,e){switch(D(e),e.tag){case 3:Tn(lu),I();break;case 26:case 27:case 5:O(e);break;case 4:I();break;case 13:At(e);break;case 19:y(Ll);break;case 10:Tn(e.type);break;case 22:case 23:At(e),Et(),null!==t&&y(cu);break;case 24:Tn(lu)}}function er(t,e){try{var n=e.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var i=r.next;n=i;do{if((n.tag&t)===t){r=void 0;var a=n.create,o=n.inst;r=a(),o.destroy=r}n=n.next}while(n!==i)}}catch(s){bi(e,e.return,s)}}function nr(t,e,n){try{var r=e.updateQueue,i=null!==r?r.lastEffect:null;if(null!==i){var a=i.next;r=a;do{if((r.tag&t)===t){var o=r.inst,s=o.destroy;if(void 0!==s){o.destroy=void 0,i=e;var l=n;try{s()}catch(u){bi(i,l,u)}}}r=r.next}while(r!==a)}}catch(u){bi(e,e.return,u)}}function rr(t){var e=t.updateQueue;if(null!==e){var n=t.stateNode;try{pt(e,n)}catch(r){bi(t,t.return,r)}}}function ir(t,e,n){n.props=Qe(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(r){bi(t,e,r)}}function ar(t,e){try{var n=t.ref;if(null!==n){var r=t.stateNode;switch(t.tag){case 26:case 27:case 5:var i=ya(r);break;default:i=r}"function"==typeof n?t.refCleanup=n(i):n.current=i}}catch(a){bi(t,e,a)}}function or(t,e){var n=t.ref,r=t.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(i){bi(t,e,i)}finally{t.refCleanup=null,null!=(t=t.alternate)&&(t.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(a){bi(t,e,a)}else n.current=null}function sr(t){var e=t.type,n=t.memoizedProps,r=t.stateNode;try{lo(r,e,n,t)}catch(i){bi(t,t.return,i)}}function lr(t,e,n){try{uo(t.stateNode,t.type,n,e,t)}catch(r){bi(t,t.return,r)}}function ur(t){return 5===t.tag||3===t.tag||!!Jo&&26===t.tag||!!ds&&27===t.tag||4===t.tag}function cr(t){t:for(;;){for(;null===t.sibling;){if(null===t.return||ur(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;5!==t.tag&&6!==t.tag&&(!ds||27!==t.tag)&&18!==t.tag;){if(2&t.flags)continue t;if(null===t.child||4===t.tag)continue t;t.child.return=t,t=t.child}if(!(2&t.flags))return t.stateNode}}function hr(t,e,n){var r=t.tag;if(5===r||6===r)t=t.stateNode,e?ho(n,t,e):oo(n,t);else if(!(4===r||ds&&27===r)&&null!==(t=t.child))for(hr(t,e,n),t=t.sibling;null!==t;)hr(t,e,n),t=t.sibling}function dr(t,e,n){var r=t.tag;if(5===r||6===r)t=t.stateNode,e?co(n,t,e):ao(n,t);else if(!(4===r||ds&&27===r)&&null!==(t=t.child))for(dr(t,e,n),t=t.sibling;null!==t;)dr(t,e,n),t=t.sibling}function fr(t,e,n){t=t.containerInfo;try{ko(t,n)}catch(r){bi(e,e.return,r)}}function gr(t,e,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Cr(t,n),4&r&&er(5,n);break;case 1:if(Cr(t,n),4&r)if(t=n.stateNode,null===e)try{t.componentDidMount()}catch(s){bi(n,n.return,s)}else{var i=Qe(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(l){bi(n,n.return,l)}}64&r&&rr(n),512&r&&ar(n,n.return);break;case 3:if(Cr(t,n),64&r&&null!==(r=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:t=ya(n.child.stateNode);break;case 1:t=n.child.stateNode}try{pt(r,t)}catch(s){bi(n,n.return,s)}}break;case 26:if(Jo){Cr(t,n),512&r&&ar(n,n.return);break}case 27:case 5:Cr(t,n),null===e&&4&r&&sr(n),512&r&&ar(n,n.return);break;case 12:Cr(t,n);break;case 13:Cr(t,n),4&r&&vr(t,n);break;case 22:if(!(i=null!==n.memoizedState||hu)){e=null!==e&&null!==e.memoizedState||du;var a=hu,o=du;hu=i,(du=e)&&!o?Pr(t,n,!!(8772&n.subtreeFlags)):Cr(t,n),hu=a,du=o}512&r&&("manual"===n.memoizedProps.mode?ar(n,n.return):or(n,n.return));break;default:Cr(t,n)}}function pr(t){var e=t.alternate;null!==e&&(t.alternate=null,pr(e)),t.child=null,t.deletions=null,t.sibling=null,5===t.tag&&null!==(e=t.stateNode)&&Ua(e),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function mr(t,e,n){for(n=n.child;null!==n;)yr(t,e,n),n=n.sibling}function yr(t,e,n){if(Gs&&"function"==typeof Gs.onCommitFiberUnmount)try{Gs.onCommitFiberUnmount(Is,n)}catch(a){}switch(n.tag){case 26:if(Jo){du||or(n,e),mr(t,e,n),n.memoizedState?rs(n.memoizedState):n.stateNode&&os(n.stateNode);break}case 27:if(ds){du||or(n,e);var r=yu,i=vu;yu=n.stateNode,mr(t,e,n),ms(n.stateNode),yu=r,vu=i;break}case 5:du||or(n,e);case 6:if(Ma){if(r=yu,i=vu,yu=null,mr(t,e,n),vu=i,null!==(yu=r))if(vu)try{go(yu,n.stateNode)}catch(o){bi(n,e,o)}else try{fo(yu,n.stateNode)}catch(o){bi(n,e,o)}}else mr(t,e,n);break;case 18:Ma&&null!==yu&&(vu?Yo(yu,n.stateNode):qo(yu,n.stateNode));break;case 4:Ma?(r=yu,i=vu,yu=n.stateNode.containerInfo,vu=!0,mr(t,e,n),yu=r,vu=i):(Aa&&fr(n.stateNode,n,xo()),mr(t,e,n));break;case 0:case 11:case 14:case 15:du||nr(2,n,e),du||nr(4,n,e),mr(t,e,n);break;case 1:du||(or(n,e),"function"==typeof(r=n.stateNode).componentWillUnmount&&ir(n,e,r)),mr(t,e,n);break;case 21:mr(t,e,n);break;case 22:du||or(n,e),du=(r=du)||null!==n.memoizedState,mr(t,e,n),du=r;break;default:mr(t,e,n)}}function vr(t,e){if(Ra&&null===e.memoizedState&&null!==(t=e.alternate)&&null!==(t=t.memoizedState)&&null!==(t=t.dehydrated))try{Ko(t)}catch(n){bi(e,e.return,n)}}function br(t,e){var n=function(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return null===e&&(e=t.stateNode=new gu),e;case 22:return null===(e=(t=t.stateNode)._retryCache)&&(e=t._retryCache=new gu),e;default:throw Error(i(435,t.tag))}}(t);e.forEach((function(e){var r=Ci.bind(null,t,e);n.has(e)||(n.add(e),e.then(r,r))}))}function _r(t,e){var n=e.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],o=t,s=e;if(Ma){var l=s;t:for(;null!==l;){switch(l.tag){case 27:case 5:yu=l.stateNode,vu=!1;break t;case 3:case 4:yu=l.stateNode.containerInfo,vu=!0;break t}l=l.return}if(null===yu)throw Error(i(160));yr(o,s,a),yu=null,vu=!1}else yr(o,s,a);null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&e.subtreeFlags)for(e=e.child;null!==e;)Sr(e,t),e=e.sibling}function Sr(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:_r(e,t),xr(t),4&r&&(nr(3,t,t.return),er(3,t),nr(5,t,t.return));break;case 1:_r(e,t),xr(t),512&r&&(du||null===n||or(n,n.return)),64&r&&hu&&null!==(t=t.updateQueue)&&null!==(r=t.callbacks)&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=null===n?r:n.concat(r));break;case 26:if(Jo){var a=bu;_r(e,t),xr(t),512&r&&(du||null===n||or(n,n.return)),4&r&&(r=null!==n?n.memoizedState:null,e=t.memoizedState,null===n?null===e?null===t.stateNode?t.stateNode=is(a,t.type,t.memoizedProps,t):as(a,t.type,t.stateNode):t.stateNode=ns(a,e,t.memoizedProps):r!==e?(null===r?null!==n.stateNode&&os(n.stateNode):rs(r),null===e?as(a,t.type,t.stateNode):ns(a,e,t.memoizedProps)):null===e&&null!==t.stateNode&&lr(t,t.memoizedProps,n.memoizedProps));break}case 27:if(ds&&4&r&&null===t.alternate){a=t.stateNode;var o=t.memoizedProps;try{gs(a),ps(t.type,o,a,t)}catch(h){bi(t,t.return,h)}}case 5:if(_r(e,t),xr(t),512&r&&(du||null===n||or(n,n.return)),Ma){if(32&t.flags){e=t.stateNode;try{po(e)}catch(h){bi(t,t.return,h)}}4&r&&null!=t.stateNode&&lr(t,e=t.memoizedProps,null!==n?n.memoizedProps:e),1024&r&&(fu=!0)}break;case 6:if(_r(e,t),xr(t),4&r&&Ma){if(null===t.stateNode)throw Error(i(162));r=t.memoizedProps,n=null!==n?n.memoizedProps:r,e=t.stateNode;try{so(e,n,r)}catch(h){bi(t,t.return,h)}}break;case 3:if(Jo?(ls(),a=bu,bu=ts(e.containerInfo),_r(e,t),bu=a):_r(e,t),xr(t),4&r){if(Ma&&Ra&&null!==n&&n.memoizedState.isDehydrated)try{Wo(e.containerInfo)}catch(h){bi(t,t.return,h)}if(Aa){r=e.containerInfo,n=e.pendingChildren;try{ko(r,n)}catch(h){bi(t,t.return,h)}}}fu&&(fu=!1,wr(t));break;case 4:Jo?(n=bu,bu=ts(t.stateNode.containerInfo),_r(e,t),xr(t),bu=n):(_r(e,t),xr(t)),4&r&&Aa&&fr(t.stateNode,t,t.stateNode.pendingChildren);break;case 12:_r(e,t),xr(t);break;case 13:_r(e,t),xr(t),8192&t.child.flags&&null!==t.memoizedState!=(null!==n&&null!==n.memoizedState)&&(Yu=Ns()),4&r&&null!==(r=t.updateQueue)&&(t.updateQueue=null,br(t,r));break;case 22:512&r&&(du||null===n||or(n,n.return)),a=null!==t.memoizedState;var s=null!==n&&null!==n.memoizedState,l=hu,u=du;if(hu=l||a,du=u||s,_r(e,t),du=u,hu=l,xr(t),(e=t.stateNode)._current=t,e._visibility&=-3,e._visibility|=2&e._pendingVisibility,8192&r&&(e._visibility=a?-2&e._visibility:1|e._visibility,a&&(e=hu||du,null===n||s||e||kr(t)),Ma&&(null===t.memoizedProps||"manual"!==t.memoizedProps.mode)))t:if(n=null,Ma)for(e=t;;){if(5===e.tag||Jo&&26===e.tag||ds&&27===e.tag){if(null===n){s=n=e;try{o=s.stateNode,a?mo(o):vo(s.stateNode,s.memoizedProps)}catch(h){bi(s,s.return,h)}}}else if(6===e.tag){if(null===n){s=e;try{var c=s.stateNode;a?yo(c):bo(c,s.memoizedProps)}catch(h){bi(s,s.return,h)}}}else if((22!==e.tag&&23!==e.tag||null===e.memoizedState||e===t)&&null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;null===e.sibling;){if(null===e.return||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}4&r&&null!==(r=t.updateQueue)&&null!==(n=r.retryQueue)&&(r.retryQueue=null,br(t,n));break;case 19:_r(e,t),xr(t),4&r&&null!==(r=t.updateQueue)&&(t.updateQueue=null,br(t,r));break;case 21:break;default:_r(e,t),xr(t)}}function xr(t){var e=t.flags;if(2&e){try{if(Ma&&(!ds||27!==t.tag)){t:{for(var n=t.return;null!==n;){if(ur(n)){var r=n;break t}n=n.return}throw Error(i(160))}switch(r.tag){case 27:if(ds){var a=r.stateNode;dr(t,cr(t),a);break}case 5:var o=r.stateNode;32&r.flags&&(po(o),r.flags&=-33),dr(t,cr(t),o);break;case 3:case 4:var s=r.stateNode.containerInfo;hr(t,cr(t),s);break;default:throw Error(i(161))}}}catch(l){bi(t,t.return,l)}t.flags&=-3}4096&e&&(t.flags&=-4097)}function wr(t){if(1024&t.subtreeFlags)for(t=t.child;null!==t;){var e=t;wr(e),5===e.tag&&1024&e.flags&&Ya(e.stateNode),t=t.sibling}}function Cr(t,e){if(8772&e.subtreeFlags)for(e=e.child;null!==e;)gr(t,e.alternate,e),e=e.sibling}function kr(t){for(t=t.child;null!==t;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:nr(4,e,e.return),kr(e);break;case 1:or(e,e.return);var n=e.stateNode;"function"==typeof n.componentWillUnmount&&ir(e,e.return,n),kr(e);break;case 26:case 27:case 5:or(e,e.return),kr(e);break;case 22:or(e,e.return),null===e.memoizedState&&kr(e);break;default:kr(e)}t=t.sibling}}function Pr(t,e,n){for(n=n&&!!(8772&e.subtreeFlags),e=e.child;null!==e;){var r=e.alternate,i=t,a=e,o=a.flags;switch(a.tag){case 0:case 11:case 15:Pr(i,a,n),er(4,a);break;case 1:if(Pr(i,a,n),"function"==typeof(i=(r=a).stateNode).componentDidMount)try{i.componentDidMount()}catch(u){bi(r,r.return,u)}if(null!==(i=(r=a).updateQueue)){var s=r.stateNode;try{var l=i.shared.hiddenCallbacks;if(null!==l)for(i.shared.hiddenCallbacks=null,i=0;i<l.length;i++)gt(l[i],s)}catch(u){bi(r,r.return,u)}}n&&64&o&&rr(a),ar(a,a.return);break;case 26:case 27:case 5:Pr(i,a,n),n&&null===r&&4&o&&sr(a),ar(a,a.return);break;case 12:default:Pr(i,a,n);break;case 13:Pr(i,a,n),n&&4&o&&vr(i,a);break;case 22:null===a.memoizedState&&Pr(i,a,n),ar(a,a.return)}e=e.sibling}}function Tr(t,e){var n=null;null!==t&&null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(n=t.memoizedState.cachePool.pool),t=null,null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),t!==n&&(null!=t&&t.refCount++,null!=n&&In(n))}function Er(t,e){t=null,null!==e.alternate&&(t=e.alternate.memoizedState.cache),(e=e.memoizedState.cache)!==t&&(e.refCount++,null!=t&&In(t))}function Fr(t,e,n,r){if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Nr(t,e,n,r),e=e.sibling}function Nr(t,e,n,r){var i=e.flags;switch(e.tag){case 0:case 11:case 15:Fr(t,e,n,r),2048&i&&er(9,e);break;case 3:Fr(t,e,n,r),2048&i&&(t=null,null!==e.alternate&&(t=e.alternate.memoizedState.cache),(e=e.memoizedState.cache)!==t&&(e.refCount++,null!=t&&In(t)));break;case 12:if(2048&i){Fr(t,e,n,r),t=e.stateNode;try{var a=e.memoizedProps,o=a.id,s=a.onPostCommit;"function"==typeof s&&s(o,null===e.alternate?"mount":"update",t.passiveEffectDuration,-0)}catch(l){bi(e,e.return,l)}}else Fr(t,e,n,r);break;case 23:break;case 22:a=e.stateNode,null!==e.memoizedState?4&a._visibility?Fr(t,e,n,r):Ar(t,e):4&a._visibility?Fr(t,e,n,r):(a._visibility|=4,Mr(t,e,n,r,!!(10256&e.subtreeFlags))),2048&i&&Tr(e.alternate,e);break;case 24:Fr(t,e,n,r),2048&i&&Er(e.alternate,e);break;default:Fr(t,e,n,r)}}function Mr(t,e,n,r,i){for(i=i&&!!(10256&e.subtreeFlags),e=e.child;null!==e;){var a=t,o=e,s=n,l=r,u=o.flags;switch(o.tag){case 0:case 11:case 15:Mr(a,o,s,l,i),er(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?4&c._visibility?Mr(a,o,s,l,i):Ar(a,o):(c._visibility|=4,Mr(a,o,s,l,i)),i&&2048&u&&Tr(o.alternate,o);break;case 24:Mr(a,o,s,l,i),i&&2048&u&&Er(o.alternate,o);break;default:Mr(a,o,s,l,i)}e=e.sibling}}function Ar(t,e){if(10256&e.subtreeFlags)for(e=e.child;null!==e;){var n=t,r=e,i=r.flags;switch(r.tag){case 22:Ar(n,r),2048&i&&Tr(r.alternate,r);break;case 24:Ar(n,r),2048&i&&Er(r.alternate,r);break;default:Ar(n,r)}e=e.sibling}}function Rr(t){if(t.subtreeFlags&_u)for(t=t.child;null!==t;)Lr(t),t=t.sibling}function Lr(t){switch(t.tag){case 26:Rr(t),t.flags&_u&&(null!==t.memoizedState?hs(bu,t.memoizedState,t.memoizedProps):ja(t.type,t.memoizedProps));break;case 5:Rr(t),t.flags&_u&&ja(t.type,t.memoizedProps);break;case 3:case 4:if(Jo){var e=bu;bu=ts(t.stateNode.containerInfo),Rr(t),bu=e}else Rr(t);break;case 22:null===t.memoizedState&&(null!==(e=t.alternate)&&null!==e.memoizedState?(e=_u,_u=16777216,Rr(t),_u=e):Rr(t));break;default:Rr(t)}}function Dr(t){var e=t.alternate;if(null!==e&&null!==(t=e.child)){e.child=null;do{e=t.sibling,t.sibling=null,t=e}while(null!==t)}}function zr(t){var e=t.deletions;if(16&t.flags){if(null!==e)for(var n=0;n<e.length;n++){var r=e[n];pu=r,Or(r,t)}Dr(t)}if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Ir(t),t=t.sibling}function Ir(t){switch(t.tag){case 0:case 11:case 15:zr(t),2048&t.flags&&nr(9,t,t.return);break;case 3:case 12:default:zr(t);break;case 22:var e=t.stateNode;null!==t.memoizedState&&4&e._visibility&&(null===t.return||13!==t.return.tag)?(e._visibility&=-5,Gr(t)):zr(t)}}function Gr(t){var e=t.deletions;if(16&t.flags){if(null!==e)for(var n=0;n<e.length;n++){var r=e[n];pu=r,Or(r,t)}Dr(t)}for(t=t.child;null!==t;){switch((e=t).tag){case 0:case 11:case 15:nr(8,e,e.return),Gr(e);break;case 22:4&(n=e.stateNode)._visibility&&(n._visibility&=-5,Gr(e));break;default:Gr(e)}t=t.sibling}}function Or(t,e){for(;null!==pu;){var n=pu;switch(n.tag){case 0:case 11:case 15:nr(8,n,e);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:In(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,pu=r;else t:for(n=t;null!==pu;){var i=(r=pu).sibling,a=r.return;if(pr(r),r===n){pu=null;break t}if(null!==i){i.return=a,pu=i;break t}pu=a}}}function Ur(t){var e=La(t);if(null!=e){if("string"!=typeof e.memoizedProps["data-testname"])throw Error(i(364));return e}if(null===(t=Ja(t)))throw Error(i(362));return t.stateNode.current}function Br(t,e){var n=t.tag;switch(e.$$typeof){case xu:if(t.type===e.value)return!0;break;case wu:t:{for(e=e.value,t=[t,0],n=0;n<t.length;){var r=t[n++],a=r.tag,o=t[n++],s=e[o];if(5!==a&&26!==a&&27!==a||!eo(r)){for(;null!=s&&Br(r,s);)s=e[++o];if(o===e.length){e=!0;break t}for(r=r.child;null!==r;)t.push(r,o),r=r.sibling}}e=!1}return e;case Cu:if((5===n||26===n||27===n)&&no(t.stateNode,e.value))return!0;break;case Pu:if((5===n||6===n||26===n||27===n)&&null!==(t=to(t))&&0<=t.indexOf(e.value))return!0;break;case ku:if((5===n||26===n||27===n)&&"string"==typeof(t=t.memoizedProps["data-testname"])&&t.toLowerCase()===e.value.toLowerCase())return!0;break;default:throw Error(i(365))}return!1}function Hr(t){switch(t.$$typeof){case xu:return"<"+(o(t.value)||"Unknown")+">";case wu:return":has("+(Hr(t)||"")+")";case Cu:return'[role="'+t.value+'"]';case Pu:return'"'+t.value+'"';case ku:return'[data-testname="'+t.value+'"]';default:throw Error(i(365))}}function Vr(t,e){var n=[];t=[t,0];for(var r=0;r<t.length;){var i=t[r++],a=i.tag,o=t[r++],s=e[o];if(5!==a&&26!==a&&27!==a||!eo(i)){for(;null!=s&&Br(i,s);)s=e[++o];if(o===e.length)n.push(i);else for(i=i.child;null!==i;)t.push(i,o),i=i.sibling}}return n}function jr(t,e){if(!$a)throw Error(i(363));t=Vr(t=Ur(t),e),e=[],t=Array.from(t);for(var n=0;n<t.length;){var r=t[n++],a=r.tag;if(5===a||26===a||27===a)eo(r)||e.push(r.stateNode);else for(r=r.child;null!==r;)t.push(r),r=r.sibling}return e}function Wr(){return 2&Fu&&0!==Au?Au&-Au:null!==ha.T?0!==yl?yl:it():Ga()}function Kr(){0===Vu&&(Vu=536870912&Au&&!nl?536870912:w());var t=Al.current;return null!==t&&(t.flags|=32),Vu}function qr(t,e,n){(t===Nu&&2===Ru||null!==t.cancelPendingCommit)&&(ei(t,0),Jr(t,Au,Vu,!1)),P(t,n),2&Fu&&t===Nu||(t===Nu&&(!(2&Fu)&&(Bu|=n),4===Ou&&Jr(t,Au,Vu,!1)),J(t))}function Yr(t,e,n){if(6&Fu)throw Error(i(327));for(var r=!n&&!(60&e)&&0===(e&t.expiredLanes)||S(t,e),a=r?function(t,e){var n=Fu;Fu|=2;var r=ii(),a=ai();Nu!==t||Au!==e?(Qu=null,Xu=Ns()+500,ei(t,e)):zu=S(t,e);t:for(;;)try{if(0!==Ru&&null!==Mu){e=Mu;var o=Lu;e:switch(Ru){case 1:Ru=0,Lu=null,di(t,e,o,1);break;case 2:if(yt(o)){Ru=0,Lu=null,hi(e);break}e=function(){2===Ru&&Nu===t&&(Ru=7),J(t)},o.then(e,e);break t;case 3:Ru=7;break t;case 4:Ru=5;break t;case 7:yt(o)?(Ru=0,Lu=null,hi(e)):(Ru=0,Lu=null,di(t,e,o,7));break;case 5:var s=null;switch(Mu.tag){case 26:s=Mu.memoizedState;case 5:case 27:var l=Mu,u=l.type,c=l.pendingProps;if(s?cs(s):Ha(u,c)){Ru=0,Lu=null;var h=l.sibling;if(null!==h)Mu=h;else{var d=l.return;null!==d?(Mu=d,fi(d)):Mu=null}break e}}Ru=0,Lu=null,di(t,e,o,5);break;case 6:Ru=0,Lu=null,di(t,e,o,6);break;case 8:ti(),Ou=6;break t;default:throw Error(i(462))}}ui();break}catch(f){ni(t,f)}return iu=ru=null,ha.H=r,ha.A=a,Fu=n,null!==Mu?0:(Nu=null,Au=0,K(),Ou)}(t,e):si(t,e,!0),o=r;;){if(0===a){zu&&!r&&Jr(t,e,0,!1);break}if(6===a)Jr(t,e,0,!Du);else{if(n=t.current.alternate,o&&!$r(n)){a=si(t,e,!1),o=!1;continue}if(2===a){if(o=e,t.errorRecoveryDisabledLanes&o)var s=0;else s=0!=(s=-536870913&t.pendingLanes)?s:536870912&s?536870912:0;if(0!==s){e=s;t:{var l=t;a=Wu;var u=Ra&&l.current.memoizedState.isDehydrated;if(u&&(ei(l,s).flags|=256),2!==(s=si(l,s,!1))){if(Iu&&!u){l.errorRecoveryDisabledLanes|=o,Bu|=o,a=4;break t}o=Ku,Ku=a,null!==o&&Xr(o)}a=s}if(o=!1,2!==a)continue}}if(1===a){ei(t,0),Jr(t,e,0,!0);break}t:{switch(r=t,a){case 0:case 1:throw Error(i(345));case 4:if((4194176&e)===e){Jr(r,e,Vu,!Du);break t}break;case 2:Ku=null;break;case 3:case 5:break;default:throw Error(i(329))}if(r.finishedWork=n,r.finishedLanes=e,(62914560&e)===e&&10<(o=Yu+300-Ns())){if(Jr(r,e,Vu,!Du),0!==_(r,0))break t;r.timeoutHandle=Ta(Qr.bind(null,r,n,Ku,Qu,qu,e,Vu,Bu,ju,Du,2,-0,0),o)}else Qr(r,n,Ku,Qu,qu,e,Vu,Bu,ju,Du,0,-0,0)}}break}J(t)}function Xr(t){null===Ku?Ku=t:Ku.push.apply(Ku,t)}function Qr(t,e,n,r,i,a,o,s,l,u,c,h,d){var f=e.subtreeFlags;if((8192&f||!(16785408&~f))&&(Va(),Lr(e),null!==(e=Wa())))return t.cancelPendingCommit=e(pi.bind(null,t,n,r,i,o,s,l,1,h,d)),void Jr(t,a,o,!u);pi(t,n,r,i,o,s,l)}function $r(t){for(var e=t;;){var n=e.tag;if((0===n||11===n||15===n)&&16384&e.flags&&null!==(n=e.updateQueue)&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],a=i.getSnapshot;i=i.value;try{if(!Os(a(),i))return!1}catch(o){return!1}}if(n=e.child,16384&e.subtreeFlags&&null!==n)n.return=e,e=n;else{if(e===t)break;for(;null===e.sibling;){if(null===e.return||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Jr(t,e,n,r){e&=~Hu,e&=~Bu,t.suspendedLanes|=e,t.pingedLanes&=~e,r&&(t.warmLanes|=e),r=t.expirationTimes;for(var i=e;0<i;){var a=31-Ss(i),o=1<<a;r[a]=-1,i&=~o}0!==n&&T(t,n,e)}function Zr(){return!!(6&Fu)||(Z(0),!1)}function ti(){if(null!==Mu){if(0===Ru)var t=Mu.return;else iu=ru=null,Ht(t=Mu),Pl=null,Tl=0,t=Mu;for(;null!==t;)tr(t.alternate,t),t=t.return;Mu=null}}function ei(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;n!==Fa&&(t.timeoutHandle=Fa,Ea(n)),null!==(n=t.cancelPendingCommit)&&(t.cancelPendingCommit=null,n()),ti(),Nu=t,Mu=n=Ti(t.current,null),Au=e,Ru=0,Lu=null,Du=!1,zu=S(t,e),Iu=!1,ju=Vu=Hu=Bu=Uu=Ou=0,Ku=Wu=null,qu=!1,8&e&&(e|=32&e);var r=t.entangledLanes;if(0!==r)for(t=t.entanglements,r&=e;0<r;){var i=31-Ss(r),a=1<<i;e|=t[i],r&=~a}return Gu=e,K(),n}function ni(t,e){zl=null,ha.H=ql,e===xl?(e=_t(),Ru=3):e===wl?(e=_t(),Ru=4):Ru=e===Zl?8:null!==e&&"object"==typeof e&&"function"==typeof e.then?6:1,Lu=e,null===Mu&&(Ou=1,$e(t,M(e,t.current)))}function ri(){var t=Al.current;return null===t||((4194176&Au)===Au?null===Rl:!!((62914560&Au)===Au||536870912&Au)&&t===Rl)}function ii(){var t=ha.H;return ha.H=ql,null===t?ql:t}function ai(){var t=ha.A;return ha.A=Su,t}function oi(){Ou=4,Du||(4194176&Au)!==Au&&null!==Al.current||(zu=!0),!(134217727&Uu)&&!(134217727&Bu)||null===Nu||Jr(Nu,Au,Vu,!1)}function si(t,e,n){var r=Fu;Fu|=2;var i=ii(),a=ai();Nu===t&&Au===e||(Qu=null,ei(t,e)),e=!1;var o=Ou;t:for(;;)try{if(0!==Ru&&null!==Mu){var s=Mu,l=Lu;switch(Ru){case 8:ti(),o=6;break t;case 3:case 2:case 6:null===Al.current&&(e=!0);var u=Ru;if(Ru=0,Lu=null,di(t,s,l,u),n&&zu){o=0;break t}break;default:u=Ru,Ru=0,Lu=null,di(t,s,l,u)}}li(),o=Ou;break}catch(c){ni(t,c)}return e&&t.shellSuspendCounter++,iu=ru=null,Fu=r,ha.H=i,ha.A=a,null===Mu&&(Nu=null,Au=0,K()),o}function li(){for(;null!==Mu;)ci(Mu)}function ui(){for(;null!==Mu&&!Es();)ci(Mu)}function ci(t){var e=kn(t.alternate,t,Gu);t.memoizedProps=t.pendingProps,null===e?fi(t):Mu=e}function hi(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=hn(n,e,e.pendingProps,e.type,void 0,Au);break;case 11:e=hn(n,e,e.pendingProps,e.type.render,e.ref,Au);break;case 5:Ht(e);default:tr(n,e),e=kn(n,e=Mu=Ei(e,Gu),Gu)}t.memoizedProps=t.pendingProps,null===e?fi(t):Mu=e}function di(t,e,n,r){iu=ru=null,Ht(e),Pl=null,Tl=0;var a=e.return;try{if(function(t,e,n,r,a){if(n.flags|=32768,null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(e=n.alternate)&&Nn(e,n,a,!0),null!==(n=Al.current)){switch(n.tag){case 13:return null===Rl?oi():null===n.alternate&&0===Ou&&(Ou=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Cl?n.flags|=16384:(null===(e=n.updateQueue)?n.updateQueue=new Set([r]):e.add(r),_i(t,r,a)),!1;case 22:return n.flags|=65536,r===Cl?n.flags|=16384:(null===(e=n.updateQueue)?(e={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=e):null===(n=e.retryQueue)?e.retryQueue=new Set([r]):n.add(r),_i(t,r,a)),!1}throw Error(i(435,n.tag))}return _i(t,r,a),oi(),!1}if(nl)return null!==(e=Al.current)?(!(65536&e.flags)&&(e.flags|=256),e.flags|=65536,e.lanes=a,r!==al&&W(M(t=Error(i(422),{cause:r}),n))):(r!==al&&W(M(e=Error(i(423),{cause:r}),n)),(t=t.current.alternate).flags|=65536,a&=-a,t.lanes|=a,r=M(r,n),ht(t,a=Ze(t.stateNode,r,a)),4!==Ou&&(Ou=2)),!1;var o=Error(i(520),{cause:r});if(o=M(o,n),null===Wu?Wu=[o]:Wu.push(o),4!==Ou&&(Ou=2),null===e)return!0;r=M(r,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=a&-a,n.lanes|=t,ht(n,t=Ze(n.stateNode,r,t)),!1;case 1:if(e=n.type,o=n.stateNode,!(128&n.flags||"function"!=typeof e.getDerivedStateFromError&&(null===o||"function"!=typeof o.componentDidCatch||null!==$u&&$u.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,en(a=tn(a),t,n,r),ht(n,a),!1}n=n.return}while(null!==n);return!1}(t,a,e,n,Au))return Ou=1,$e(t,M(n,t.current)),void(Mu=null)}catch(o){if(null!==a)throw Mu=a,o;return Ou=1,$e(t,M(n,t.current)),void(Mu=null)}32768&e.flags?(nl||1===r?t=!0:zu||536870912&Au?t=!1:(Du=t=!0,(2===r||3===r||6===r)&&null!==(r=Al.current)&&13===r.tag&&(r.flags|=16384)),gi(e,t)):fi(e)}function fi(t){var e=t;do{if(32768&e.flags)return void gi(e,Du);t=e.return;var n=Jn(e.alternate,e,Gu);if(null!==n)return void(Mu=n);if(null!==(e=e.sibling))return void(Mu=e);Mu=e=t}while(null!==e);0===Ou&&(Ou=5)}function gi(t,e){do{var n=Zn(t.alternate,t);if(null!==n)return n.flags&=32767,void(Mu=n);if(null!==(n=t.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&null!==(t=t.sibling))return void(Mu=t);Mu=t=n}while(null!==t);Ou=6,Mu=null}function pi(t,e,n,r,a,o,s,l,u,c){var h=ha.T,d=Ia();try{za(2),ha.T=null,function(t,e,n,r,a,o,s,l){do{yi()}while(null!==Zu);if(6&Fu)throw Error(i(327));var u=t.finishedWork;if(r=t.finishedLanes,null===u)return null;if(t.finishedWork=null,t.finishedLanes=0,u===t.current)throw Error(i(177));t.callbackNode=null,t.callbackPriority=0,t.cancelPendingCommit=null;var c=u.lanes|u.childLanes;if(function(t,e,n,r,i,a){var o=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var s=t.entanglements,l=t.expirationTimes,u=t.hiddenUpdates;for(n=o&~n;0<n;){var c=31-Ss(n),h=1<<c;s[c]=0,l[c]=-1;var d=u[c];if(null!==d)for(u[c]=null,c=0;c<d.length;c++){var f=d[c];null!==f&&(f.lane&=-536870913)}n&=~h}0!==r&&T(t,r,0),0!==a&&0===i&&0!==t.tag&&(t.suspendedLanes|=a&~(o&~e))}(t,r,c|=ll,o,s,l),t===Nu&&(Mu=Nu=null,Au=0),!(10256&u.subtreeFlags)&&!(10256&u.flags)||Ju||(Ju=!0,ec=c,nc=n,Ps(Rs,(function(){return yi(),null}))),n=!!(15990&u.flags),15990&u.subtreeFlags||n?(n=ha.T,ha.T=null,o=Ia(),za(2),s=Fu,Fu|=4,function(t,e){for(_a(t.containerInfo),pu=e;null!==pu;)if(e=(t=pu).child,1028&t.subtreeFlags&&null!==e)e.return=t,pu=e;else for(;null!==pu;){var n=(t=pu).alternate;switch(e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==n){e=void 0;var r=t,a=n.memoizedProps;n=n.memoizedState;var o=r.stateNode;try{var s=Qe(r.type,a,(r.elementType,r.type));e=o.getSnapshotBeforeUpdate(s,n),o.__reactInternalSnapshotBeforeUpdate=e}catch(l){bi(r,r.return,l)}}break;case 3:1024&e&&Ma&&_o(t.stateNode.containerInfo);break;default:if(1024&e)throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,pu=e;break}pu=t.return}s=mu,mu=!1}(t,u),Sr(u,t),Sa(t.containerInfo),t.current=u,gr(t,u.alternate,u),Fs(),Fu=s,za(o),ha.T=n):t.current=u,Ju?(Ju=!1,Zu=t,tc=r):mi(t,c),0===(c=t.pendingLanes)&&($u=null),function(t){if(Gs&&"function"==typeof Gs.onCommitFiberRoot)try{Gs.onCommitFiberRoot(Is,t,void 0,!(128&~t.current.flags))}catch(e){}}(u.stateNode),J(t),null!==e)for(a=t.onRecoverableError,u=0;u<e.length;u++)c=e[u],a(c.value,{componentStack:c.stack});!!(3&tc)&&yi(),c=t.pendingLanes,4194218&r&&42&c?t===ic?rc++:(rc=0,ic=t):rc=0,Z(0)}(t,e,n,r,d,a,o,s)}finally{ha.T=h,za(d)}}function mi(t,e){0===(t.pooledCacheLanes&=e)&&null!=(e=t.pooledCache)&&(t.pooledCache=null,In(e))}function yi(){if(null!==Zu){var t=Zu,e=ec;ec=0;var n=F(tc),r=32>n?32:n;n=ha.T;var a=Ia();try{if(za(r),ha.T=null,null===Zu)var o=!1;else{r=nc,nc=null;var s=Zu,l=tc;if(Zu=null,tc=0,6&Fu)throw Error(i(331));var u=Fu;if(Fu|=4,Ir(s.current),Nr(s,s.current,l,r),Fu=u,Z(0),Gs&&"function"==typeof Gs.onPostCommitFiberRoot)try{Gs.onPostCommitFiberRoot(Is,s)}catch(c){}o=!0}return o}finally{za(a),ha.T=n,mi(t,e)}}return!1}function vi(t,e,n){e=M(n,e),null!==(t=ut(t,e=Ze(t.stateNode,e,2),2))&&(P(t,2),J(t))}function bi(t,e,n){if(3===t.tag)vi(t,t,n);else for(;null!==e;){if(3===e.tag){vi(e,t,n);break}if(1===e.tag){var r=e.stateNode;if("function"==typeof e.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===$u||!$u.has(r))){t=M(n,t),null!==(r=ut(e,n=tn(2),2))&&(en(n,r,e,t),P(r,2),J(r));break}}e=e.return}}function _i(t,e,n){var r=t.pingCache;if(null===r){r=t.pingCache=new Eu;var i=new Set;r.set(e,i)}else void 0===(i=r.get(e))&&(i=new Set,r.set(e,i));i.has(n)||(Iu=!0,i.add(n),t=Si.bind(null,t,e,n),e.then(t,t))}function Si(t,e,n){var r=t.pingCache;null!==r&&r.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Nu===t&&(Au&n)===n&&(4===Ou||3===Ou&&(62914560&Au)===Au&&300>Ns()-Yu?!(2&Fu)&&ei(t,0):Hu|=n,ju===Au&&(ju=0)),J(t)}function xi(t,e){0===e&&(e=C()),null!==(t=X(t,e))&&(P(t,e),J(t))}function wi(t){var e=t.memoizedState,n=0;null!==e&&(n=e.retryLane),xi(t,n)}function Ci(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,a=t.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=t.stateNode;break;case 22:r=t.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(e),xi(t,n)}function ki(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pi(t){return!(!(t=t.prototype)||!t.isReactComponent)}function Ti(t,n){var r=t.alternate;return null===r?((r=e(t.tag,n,t.key,t.mode)).elementType=t.elementType,r.type=t.type,r.stateNode=t.stateNode,r.alternate=t,t.alternate=r):(r.pendingProps=n,r.type=t.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=31457280&t.flags,r.childLanes=t.childLanes,r.lanes=t.lanes,r.child=t.child,r.memoizedProps=t.memoizedProps,r.memoizedState=t.memoizedState,r.updateQueue=t.updateQueue,n=t.dependencies,r.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext},r.sibling=t.sibling,r.index=t.index,r.ref=t.ref,r.refCleanup=t.refCleanup,r}function Ei(t,e){t.flags&=31457282;var n=t.alternate;return null===n?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Fi(t,n,r,a,o,s){var l=0;if(a=t,"function"==typeof t)Pi(t)&&(l=1);else if("string"==typeof t)l=Jo&&ds?Zo(t,r,Qs.current)?26:ys(t)?27:5:Jo?Zo(t,r,Qs.current)?26:5:ds&&ys(t)?27:5;else t:switch(t){case Qi:return Ni(r.children,o,s,n);case $i:l=8,o|=24;break;case Ji:return(t=e(12,r,n,2|o)).elementType=Ji,t.lanes=s,t;case ra:return(t=e(13,r,n,o)).elementType=ra,t.lanes=s,t;case ia:return(t=e(19,r,n,o)).elementType=ia,t.lanes=s,t;case sa:return Mi(r,o,s,n);default:if("object"==typeof t&&null!==t)switch(t.$$typeof){case Zi:case ea:l=10;break t;case ta:l=9;break t;case na:l=11;break t;case aa:l=14;break t;case oa:l=16,a=null;break t}l=29,r=Error(i(130,null===t?"null":typeof t,"")),a=null}return(n=e(l,r,n,o)).elementType=t,n.type=a,n.lanes=s,n}function Ni(t,n,r,i){return(t=e(7,t,i,n)).lanes=r,t}function Mi(t,n,r,a){(t=e(22,t,a,n)).elementType=sa,t.lanes=r;var o={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var t=o._current;if(null===t)throw Error(i(456));if(!(2&o._pendingVisibility)){var e=X(t,2);null!==e&&(o._pendingVisibility|=2,qr(e,0,2))}},attach:function(){var t=o._current;if(null===t)throw Error(i(456));if(2&o._pendingVisibility){var e=X(t,2);null!==e&&(o._pendingVisibility&=-3,qr(e,0,2))}}};return t.stateNode=o,t}function Ai(t,n,r){return(t=e(6,t,null,n)).lanes=r,t}function Ri(t,n,r){return(n=e(4,null!==t.children?t.children:[],t.key,n)).lanes=r,n.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},n}function Li(t,e,n,r,i,a,o,s){this.tag=1,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=Fa,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=k(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=k(0),this.hiddenUpdates=k(null),this.identifierPrefix=r,this.onUncaughtError=i,this.onCaughtError=a,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=s,this.incompleteTransitions=new Map}function Di(t,n,r,i,a,o,s,l,u,c,h,d){return t=new Li(t,n,r,s,l,u,c,d),n=1,!0===o&&(n|=24),o=e(3,null,null,n),t.current=o,o.stateNode=t,(n=zn()).refCount++,t.pooledCache=n,n.refCount++,o.memoizedState={element:i,isDehydrated:r,cache:n},ot(o),t}function zi(t){return t?t=_s:_s}function Ii(t){var e=t._reactInternals;if(void 0===e){if("function"==typeof t.render)throw Error(i(188));throw t=Object.keys(t).join(","),Error(i(268,t))}return null===(t=null!==(t=f(e))?g(t):null)?null:ya(t.stateNode)}function Gi(t,e,n,r,i,a){i=zi(i),null===r.context?r.context=i:r.pendingContext=i,(r=lt(e)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=ut(t,r,e))&&(qr(n,0,e),ct(n,t,e))}function Oi(t,e){if(null!==(t=t.memoizedState)&&null!==t.dehydrated){var n=t.retryLane;t.retryLane=0!==n&&n<e?n:e}}function Ui(t,e){Oi(t,e),(t=t.alternate)&&Oi(t,e)}var Bi,Hi,Vi={},ji=n(),Wi=Ye(),Ki=Object.assign,qi=Symbol.for("react.element"),Yi=Symbol.for("react.transitional.element"),Xi=Symbol.for("react.portal"),Qi=Symbol.for("react.fragment"),$i=Symbol.for("react.strict_mode"),Ji=Symbol.for("react.profiler"),Zi=Symbol.for("react.provider"),ta=Symbol.for("react.consumer"),ea=Symbol.for("react.context"),na=Symbol.for("react.forward_ref"),ra=Symbol.for("react.suspense"),ia=Symbol.for("react.suspense_list"),aa=Symbol.for("react.memo"),oa=Symbol.for("react.lazy"),sa=Symbol.for("react.offscreen"),la=Symbol.for("react.memo_cache_sentinel"),ua=Symbol.iterator,ca=Symbol.for("react.client.reference"),ha=ji.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,da=!1,fa=Array.isArray,ga=t.rendererVersion,pa=t.rendererPackageName,ma=t.extraDevToolsConfig,ya=t.getPublicInstance,va=t.getRootHostContext,ba=t.getChildHostContext,_a=t.prepareForCommit,Sa=t.resetAfterCommit,xa=t.createInstance,wa=t.appendInitialChild,Ca=t.finalizeInitialChildren,ka=t.shouldSetTextContent,Pa=t.createTextInstance,Ta=t.scheduleTimeout,Ea=t.cancelTimeout,Fa=t.noTimeout,Na=t.isPrimaryRenderer;t.warnsIfNotActing;var Ma=t.supportsMutation,Aa=t.supportsPersistence,Ra=t.supportsHydration,La=t.getInstanceFromNode;t.beforeActiveInstanceBlur,t.afterActiveInstanceBlur;var Da=t.preparePortalMount;t.prepareScopeUpdate,t.getInstanceFromScope;var za=t.setCurrentUpdatePriority,Ia=t.getCurrentUpdatePriority,Ga=t.resolveUpdatePriority;t.resolveEventType,t.resolveEventTimeStamp;var Oa=t.shouldAttemptEagerTransition,Ua=t.detachDeletedInstance;t.requestPostPaintCallback;var Ba=t.maySuspendCommit,Ha=t.preloadInstance,Va=t.startSuspendingCommit,ja=t.suspendInstance,Wa=t.waitForCommitToBeReady,Ka=t.NotPendingTransition,qa=t.HostTransitionContext,Ya=t.resetFormInstance;t.bindToConsole;var Xa=t.supportsMicrotasks,Qa=t.scheduleMicrotask,$a=t.supportsTestSelectors,Ja=t.findFiberRoot,Za=t.getBoundingRect,to=t.getTextContent,eo=t.isHiddenSubtree,no=t.matchAccessibilityRole,ro=t.setFocusIfFocusable,io=t.setupIntersectionObserver,ao=t.appendChild,oo=t.appendChildToContainer,so=t.commitTextUpdate,lo=t.commitMount,uo=t.commitUpdate,co=t.insertBefore,ho=t.insertInContainerBefore,fo=t.removeChild,go=t.removeChildFromContainer,po=t.resetTextContent,mo=t.hideInstance,yo=t.hideTextInstance,vo=t.unhideInstance,bo=t.unhideTextInstance,_o=t.clearContainer,So=t.cloneInstance,xo=t.createContainerChildSet,wo=t.appendChildToContainerChildSet,Co=t.finalizeContainerChildren,ko=t.replaceContainerChildren,Po=t.cloneHiddenInstance,To=t.cloneHiddenTextInstance,Eo=t.isSuspenseInstancePending,Fo=t.isSuspenseInstanceFallback,No=t.getSuspenseInstanceFallbackErrorDetails,Mo=t.registerSuspenseInstanceRetry,Ao=t.canHydrateFormStateMarker,Ro=t.isFormStateMarkerMatching,Lo=t.getNextHydratableSibling,Do=t.getFirstHydratableChild,zo=t.getFirstHydratableChildWithinContainer,Io=t.getFirstHydratableChildWithinSuspenseInstance,Go=t.canHydrateInstance,Oo=t.canHydrateTextInstance,Uo=t.canHydrateSuspenseInstance,Bo=t.hydrateInstance,Ho=t.hydrateTextInstance,Vo=t.hydrateSuspenseInstance,jo=t.getNextHydratableInstanceAfterSuspenseInstance,Wo=t.commitHydratedContainer,Ko=t.commitHydratedSuspenseInstance,qo=t.clearSuspenseBoundary,Yo=t.clearSuspenseBoundaryFromContainer,Xo=t.shouldDeleteUnhydratedTailInstances;t.diffHydratedPropsForDevWarnings,t.diffHydratedTextForDevWarnings,t.describeHydratableInstanceForDevWarnings;var Qo=t.validateHydratableInstance,$o=t.validateHydratableTextInstance,Jo=t.supportsResources,Zo=t.isHostHoistableType,ts=t.getHoistableRoot,es=t.getResource,ns=t.acquireResource,rs=t.releaseResource,is=t.hydrateHoistable,as=t.mountHoistable,os=t.unmountHoistable,ss=t.createHoistableInstance,ls=t.prepareToCommitHoistables,us=t.mayResourceSuspendCommit,cs=t.preloadResource,hs=t.suspendResource,ds=t.supportsSingletons,fs=t.resolveSingletonInstance,gs=t.clearSingleton,ps=t.acquireSingletonInstance,ms=t.releaseSingletonInstance,ys=t.isHostSingletonType,vs=[],bs=-1,_s={},Ss=Math.clz32?Math.clz32:function(t){return 0==(t>>>=0)?32:31-(xs(t)/ws|0)|0},xs=Math.log,ws=Math.LN2,Cs=128,ks=4194304,Ps=Wi.unstable_scheduleCallback,Ts=Wi.unstable_cancelCallback,Es=Wi.unstable_shouldYield,Fs=Wi.unstable_requestPaint,Ns=Wi.unstable_now,Ms=Wi.unstable_ImmediatePriority,As=Wi.unstable_UserBlockingPriority,Rs=Wi.unstable_NormalPriority,Ls=Wi.unstable_IdlePriority,Ds=Wi.log,zs=Wi.unstable_setDisableYieldValue,Is=null,Gs=null,Os="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},Us=new WeakMap,Bs=[],Hs=0,Vs=null,js=0,Ws=[],Ks=0,qs=null,Ys=1,Xs="",Qs=m(null),$s=m(null),Js=m(null),Zs=m(null),tl=null,el=null,nl=!1,rl=null,il=!1,al=Error(i(519)),ol=[],sl=0,ll=0,ul=null,cl=null,hl=!1,dl=!1,fl=!1,gl=0,pl=null,ml=0,yl=0,vl=null,bl=!1,_l=!1,Sl=Object.prototype.hasOwnProperty,xl=Error(i(460)),wl=Error(i(474)),Cl={then:function(){}},kl=null,Pl=null,Tl=0,El=kt(!0),Fl=kt(!1),Nl=m(null),Ml=m(0),Al=m(null),Rl=null,Ll=m(0),Dl=0,zl=null,Il=null,Gl=null,Ol=!1,Ul=!1,Bl=!1,Hl=0,Vl=0,jl=null,Wl=0,Kl=function(){return{lastEffect:null,events:null,stores:null,memoCache:null}},ql={readContext:Rn,use:Kt,useCallback:Lt,useContext:Lt,useEffect:Lt,useImperativeHandle:Lt,useLayoutEffect:Lt,useInsertionEffect:Lt,useMemo:Lt,useReducer:Lt,useRef:Lt,useState:Lt,useDebugValue:Lt,useDeferredValue:Lt,useTransition:Lt,useSyncExternalStore:Lt,useId:Lt};ql.useCacheRefresh=Lt,ql.useMemoCache=Lt,ql.useHostTransitionStatus=Lt,ql.useFormState=Lt,ql.useActionState=Lt,ql.useOptimistic=Lt;var Yl={readContext:Rn,use:Kt,useCallback:function(t,e){return Vt().memoizedState=[t,void 0===e?null:e],t},useContext:Rn,useEffect:xe,useImperativeHandle:function(t,e,n){n=null!=n?n.concat([t]):null,_e(4194308,4,Pe.bind(null,e,t),n)},useLayoutEffect:function(t,e){return _e(4194308,4,t,e)},useInsertionEffect:function(t,e){_e(4,2,t,e)},useMemo:function(t,e){var n=Vt();e=void 0===e?null:e;var r=t();if(Bl){N(!0);try{t()}finally{N(!1)}}return n.memoizedState=[r,e],r},useReducer:function(t,e,n){var r=Vt();if(void 0!==n){var i=n(e);if(Bl){N(!0);try{n(e)}finally{N(!1)}}}else i=e;return r.memoizedState=r.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},r.queue=t,t=t.dispatch=Oe.bind(null,zl,t),[r.memoizedState,t]},useRef:function(t){return t={current:t},Vt().memoizedState=t},useState:function(t){var e=(t=ie(t)).queue,n=Ue.bind(null,zl,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Ee,useDeferredValue:function(t,e){return Me(Vt(),t,e)},useTransition:function(){var t=ie(!1);return t=Re.bind(null,zl,t.queue,!0,!1),Vt().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var r=zl,a=Vt();if(nl){if(void 0===n)throw Error(i(407));n=n()}else{if(n=e(),null===Nu)throw Error(i(349));60&Au||Zt(r,e,n)}a.memoizedState=n;var o={value:n,getSnapshot:e};return a.queue=o,xe(ee.bind(null,r,o,t),[t]),r.flags|=2048,ve(9,te.bind(null,r,o,n,e),{destroy:void 0},null),n},useId:function(){var t=Vt(),e=Nu.identifierPrefix;if(nl){var n=Xs;e=":"+e+"R"+(n=(Ys&~(1<<32-Ss(Ys)-1)).toString(32)+n),0<(n=Hl++)&&(e+="H"+n.toString(32)),e+=":"}else e=":"+e+"r"+(n=Wl++).toString(32)+":";return t.memoizedState=e},useCacheRefresh:function(){return Vt().memoizedState=Ge.bind(null,zl)}};Yl.useMemoCache=qt,Yl.useHostTransitionStatus=De,Yl.useFormState=fe,Yl.useActionState=fe,Yl.useOptimistic=function(t){var e=Vt();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=He.bind(null,zl,!0,n),n.dispatch=e,[t,e]};var Xl={readContext:Rn,use:Kt,useCallback:Fe,useContext:Rn,useEffect:we,useImperativeHandle:Te,useInsertionEffect:Ce,useLayoutEffect:ke,useMemo:Ne,useReducer:Xt,useRef:be,useState:function(){return Xt(Yt)},useDebugValue:Ee,useDeferredValue:function(t,e){return Ae(jt(),Il.memoizedState,t,e)},useTransition:function(){var t=Xt(Yt)[0],e=jt().memoizedState;return["boolean"==typeof t?t:Wt(t),e]},useSyncExternalStore:Jt,useId:ze};Xl.useCacheRefresh=Ie,Xl.useMemoCache=qt,Xl.useHostTransitionStatus=De,Xl.useFormState=ge,Xl.useActionState=ge,Xl.useOptimistic=function(t,e){return ae(jt(),0,t,e)};var Ql={readContext:Rn,use:Kt,useCallback:Fe,useContext:Rn,useEffect:we,useImperativeHandle:Te,useInsertionEffect:Ce,useLayoutEffect:ke,useMemo:Ne,useReducer:$t,useRef:be,useState:function(){return $t(Yt)},useDebugValue:Ee,useDeferredValue:function(t,e){var n=jt();return null===Il?Me(n,t,e):Ae(n,Il.memoizedState,t,e)},useTransition:function(){var t=$t(Yt)[0],e=jt().memoizedState;return["boolean"==typeof t?t:Wt(t),e]},useSyncExternalStore:Jt,useId:ze};Ql.useCacheRefresh=Ie,Ql.useMemoCache=qt,Ql.useHostTransitionStatus=De,Ql.useFormState=ye,Ql.useActionState=ye,Ql.useOptimistic=function(t,e){var n=jt();return null!==Il?ae(n,0,t,e):(n.baseState=t,[t,n.queue.dispatch])};var $l={isMounted:function(t){return!!(t=t._reactInternals)&&h(t)===t},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=Wr(),i=lt(r);i.payload=e,null!=n&&(i.callback=n),null!==(e=ut(t,i,r))&&(qr(e,0,r),ct(e,t,r))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=Wr(),i=lt(r);i.tag=1,i.payload=e,null!=n&&(i.callback=n),null!==(e=ut(t,i,r))&&(qr(e,0,r),ct(e,t,r))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Wr(),r=lt(n);r.tag=2,null!=e&&(r.callback=e),null!==(e=ut(t,r,n))&&(qr(e,0,n),ct(e,t,n))}},Jl="function"==typeof reportError?reportError:function(t){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof t&&null!==t&&"string"==typeof t.message?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",t)},Zl=Error(i(461)),tu=!1,eu={dehydrated:null,treeContext:null,retryLane:0},nu=m(null),ru=null,iu=null,au="undefined"!=typeof AbortController?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(e,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach((function(t){return t()}))}},ou=Wi.unstable_scheduleCallback,su=Wi.unstable_NormalPriority,lu={$$typeof:ea,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0},uu=ha.S;ha.S=function(t,e){"object"==typeof e&&null!==e&&"function"==typeof e.then&&function(t,e){if(null===pl){var n=pl=[];ml=0,yl=it(),vl={status:"pending",value:void 0,then:function(t){n.push(t)}}}ml++,e.then(at,at)}(0,e),null!==uu&&uu(t,e)};var cu=m(null),hu=!1,du=!1,fu=!1,gu="function"==typeof WeakSet?WeakSet:Set,pu=null,mu=!1,yu=null,vu=!1,bu=null,_u=8192,Su={getCacheForType:function(t){var e=Rn(lu),n=e.data.get(t);return void 0===n&&(n=t(),e.data.set(t,n)),n}},xu=0,wu=1,Cu=2,ku=3,Pu=4;if("function"==typeof Symbol&&Symbol.for){var Tu=Symbol.for;xu=Tu("selector.component"),wu=Tu("selector.has_pseudo_class"),Cu=Tu("selector.role"),ku=Tu("selector.test_id"),Pu=Tu("selector.text")}var Eu="function"==typeof WeakMap?WeakMap:Map,Fu=0,Nu=null,Mu=null,Au=0,Ru=0,Lu=null,Du=!1,zu=!1,Iu=!1,Gu=0,Ou=0,Uu=0,Bu=0,Hu=0,Vu=0,ju=0,Wu=null,Ku=null,qu=!1,Yu=0,Xu=1/0,Qu=null,$u=null,Ju=!1,Zu=null,tc=0,ec=0,nc=null,rc=0,ic=null;return Vi.attemptContinuousHydration=function(t){if(13===t.tag){var e=X(t,67108864);null!==e&&qr(e,0,67108864),Ui(t,67108864)}},Vi.attemptHydrationAtCurrentPriority=function(t){if(13===t.tag){var e=Wr(),n=X(t,e);null!==n&&qr(n,0,e),Ui(t,e)}},Vi.attemptSynchronousHydration=function(t){switch(t.tag){case 3:if((t=t.stateNode).current.memoizedState.isDehydrated){var e=b(t.pendingLanes);if(0!==e){for(t.pendingLanes|=2,t.entangledLanes|=2;e;){var n=1<<31-Ss(e);t.entanglements[1]|=n,e&=~n}J(t),!(6&Fu)&&(Xu=Ns()+500,Z(0))}}break;case 13:null!==(e=X(t,2))&&qr(e,0,2),Zr(),Ui(t,2)}},Vi.batchedUpdates=function(t,e){return t(e)},Vi.createComponentSelector=function(t){return{$$typeof:xu,value:t}},Vi.createContainer=function(t,e,n,r,i,a,o,s,l,u){return Di(t,e,!1,null,0,r,a,o,s,l,0,null)},Vi.createHasPseudoClassSelector=function(t){return{$$typeof:wu,value:t}},Vi.createHydrationContainer=function(t,e,n,r,i,a,o,s,l,u,c,h,d){return(t=Di(n,r,!0,t,0,a,s,l,u,c,0,d)).context=zi(null),n=t.current,(i=lt(r=Wr())).callback=null!=e?e:null,ut(n,i,r),t.current.lanes=r,P(t,r),J(t),t},Vi.createPortal=function(t,e,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:Xi,key:null==r?null:""+r,children:t,containerInfo:e,implementation:n}},Vi.createRoleSelector=function(t){return{$$typeof:Cu,value:t}},Vi.createTestNameSelector=function(t){return{$$typeof:ku,value:t}},Vi.createTextSelector=function(t){return{$$typeof:Pu,value:t}},Vi.defaultOnCaughtError=function(t){},Vi.defaultOnRecoverableError=function(t){Jl(t)},Vi.defaultOnUncaughtError=function(t){Jl(t)},Vi.deferredUpdates=function(t){var e=ha.T,n=Ia();try{return za(32),ha.T=null,t()}finally{za(n),ha.T=e}},Vi.discreteUpdates=function(t,e,n,r,i){var a=ha.T,o=Ia();try{return za(2),ha.T=null,t(e,n,r,i)}finally{za(o),ha.T=a,0===Fu&&(Xu=Ns()+500)}},Vi.findAllNodes=jr,Vi.findBoundingRects=function(t,e){if(!$a)throw Error(i(363));e=jr(t,e),t=[];for(var n=0;n<e.length;n++)t.push(Za(e[n]));for(e=t.length-1;0<e;e--)for(var r=(n=t[e]).x,a=r+n.width,o=n.y,s=o+n.height,l=e-1;0<=l;l--)if(e!==l){var u=t[l],c=u.x,h=c+u.width,d=u.y,f=d+u.height;if(r>=c&&o>=d&&a<=h&&s<=f){t.splice(e,1);break}if(!(r!==c||n.width!==u.width||f<o||d>s)){d>o&&(u.height+=d-o,u.y=o),f<s&&(u.height=s-d),t.splice(e,1);break}if(!(o!==d||n.height!==u.height||h<r||c>a)){c>r&&(u.width+=c-r,u.x=r),h<a&&(u.width=a-c),t.splice(e,1);break}}return t},Vi.findHostInstance=Ii,Vi.findHostInstanceWithNoPortals=function(t){return null===(t=null!==(t=f(t))?p(t):null)?null:ya(t.stateNode)},Vi.findHostInstanceWithWarning=function(t){return Ii(t)},Vi.flushPassiveEffects=yi,Vi.flushSyncFromReconciler=function(t){var e=Fu;Fu|=1;var n=ha.T,r=Ia();try{if(za(2),ha.T=null,t)return t()}finally{za(r),ha.T=n,!(6&(Fu=e))&&Z(0)}},Vi.flushSyncWork=Zr,Vi.focusWithin=function(t,e){if(!$a)throw Error(i(363));for(e=Vr(t=Ur(t),e),e=Array.from(e),t=0;t<e.length;){var n=e[t++],r=n.tag;if(!eo(n)){if((5===r||26===r||27===r)&&ro(n.stateNode))return!0;for(n=n.child;null!==n;)e.push(n),n=n.sibling}}return!1},Vi.getFindAllNodesFailureDescription=function(t,e){if(!$a)throw Error(i(363));var n=0,r=[];t=[Ur(t),0];for(var a=0;a<t.length;){var o=t[a++],s=o.tag,l=t[a++],u=e[l];if((5!==s&&26!==s&&27!==s||!eo(o))&&(Br(o,u)&&(r.push(Hr(u)),++l>n&&(n=l)),l<e.length))for(o=o.child;null!==o;)t.push(o,l),o=o.sibling}if(n<e.length){for(t=[];n<e.length;n++)t.push(Hr(e[n]));return"findAllNodes was able to match part of the selector:\n  "+r.join(" > ")+"\n\nNo matching component was found for:\n  "+t.join(" > ")}return null},Vi.getPublicRootInstance=function(t){if(!(t=t.current).child)return null;switch(t.child.tag){case 27:case 5:return ya(t.child.stateNode);default:return t.child.stateNode}},Vi.injectIntoDevTools=function(){var t={bundleType:0,version:ga,rendererPackageName:pa,currentDispatcherRef:ha,findFiberByHostInstance:La,reconcilerVersion:"19.0.0"};if(null!==ma&&(t.rendererConfig=ma),"undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)t=!1;else{var e=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(e.isDisabled||!e.supportsFiber)t=!0;else{try{Is=e.inject(t),Gs=e}catch(n){}t=!!e.checkDCE}}return t},Vi.isAlreadyRendering=function(){return!1},Vi.observeVisibleRects=function(t,e,n,r){if(!$a)throw Error(i(363));t=jr(t,e);var a=io(t,n,r).disconnect;return{disconnect:function(){a()}}},Vi.shouldError=function(){return null},Vi.shouldSuspend=function(){return!1},Vi.startHostTransition=function(t,e,n,a){if(5!==t.tag)throw Error(i(476));var o=Le(t).queue;Re(t,o,e,Ka,null===n?r:function(){var e=Le(t).next.queue;return Be(t,e,{},Wr()),n(a)})},Vi.updateContainer=function(t,e,n,r){var i=e.current,a=Wr();return Gi(i,a,t,e,n,r),a},Vi.updateContainerSync=function(t,e,n,r){return 0===e.tag&&yi(),Gi(e.current,2,t,e,n,r),2},Vi},t.exports.default=t.exports,Object.defineProperty(t.exports,"__esModule",{value:!0})),We.exports;var t}const Qe=e((Ve||(Ve=1,je.exports=Xe()),je.exports));var $e,Je,Ze={exports:{}},tn={};var en=(Je||(Je=1,Ze.exports=($e||($e=1,tn.ConcurrentRoot=1,tn.ContinuousEventPriority=8,tn.DefaultEventPriority=32,tn.DiscreteEventPriority=2,tn.IdleEventPriority=268435456,tn.LegacyRoot=0,tn.NoEventPriority=0),tn)),Ze.exports),nn=c();const rn={children:!0,ref:!0,key:!0,style:!0,forwardedRef:!0,unstable_applyCache:!0,unstable_applyDrawHitFromCache:!0};let an=!1,on=!1;const sn=".react-konva-event",ln={};function un(t,e,n=ln){if(!an&&"zIndex"in e&&(an=!0),!on&&e.draggable){var r=void 0!==e.x||void 0!==e.y,i=e.onDragEnd||e.onDragMove;r&&!i&&(on=!0)}for(var a in n)if(!rn[a]){var o="on"===a.slice(0,2),s=n[a]!==e[a];if(o&&s)"content"===(f=a.substr(2).toLowerCase()).substr(0,7)&&(f="content"+f.substr(7,1).toUpperCase()+f.substr(8)),t.off(f,n[a]);!e.hasOwnProperty(a)&&t.setAttr(a,void 0)}var l=e._useStrictMode,u={},c=!1;const h={};for(var a in e)if(!rn[a]){o="on"===a.slice(0,2);var d=n[a]!==e[a];if(o&&d)"content"===(f=a.substr(2).toLowerCase()).substr(0,7)&&(f="content"+f.substr(7,1).toUpperCase()+f.substr(8)),e[a]&&(h[f]=e[a]);!o&&(e[a]!==n[a]||l&&e[a]!==t.getAttr(a))&&(c=!0,u[a]=e[a])}for(var f in c&&(t.setAttrs(u),cn(t)),h)t.on(f+sn,h[f])}function cn(t){if(!nn.Konva.autoDrawEnabled){var e=t.getLayer()||t.getStage();e&&e.batchDraw()}}var hn,dn,fn={exports:{}},gn={};var pn=(dn||(dn=1,fn.exports=(hn||(hn=1,function(t){function e(t,e){var n=t.length;t.push(e);t:for(;0<n;){var r=n-1>>>1,a=t[r];if(!(0<i(a,e)))break t;t[r]=e,t[n]=a,n=r}}function n(t){return 0===t.length?null:t[0]}function r(t){if(0===t.length)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var r=0,a=t.length,o=a>>>1;r<o;){var s=2*(r+1)-1,l=t[s],u=s+1,c=t[u];if(0>i(l,n))u<a&&0>i(c,l)?(t[r]=c,t[u]=n,r=u):(t[r]=l,t[s]=n,r=s);else{if(!(u<a&&0>i(c,n)))break t;t[r]=c,t[u]=n,r=u}}}return e}function i(t,e){var n=t.sortIndex-e.sortIndex;return 0!==n?n:t.id-e.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var l=[],u=[],c=1,h=null,d=3,f=!1,g=!1,p=!1,m="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function b(t){for(var i=n(u);null!==i;){if(null===i.callback)r(u);else{if(!(i.startTime<=t))break;r(u),i.sortIndex=i.expirationTime,e(l,i)}i=n(u)}}function _(t){if(p=!1,b(t),!g)if(null!==n(l))g=!0,N();else{var e=n(u);null!==e&&M(_,e.startTime-t)}}var S,x=!1,w=-1,C=5,k=-1;function P(){return!(t.unstable_now()-k<C)}function T(){if(x){var e=t.unstable_now();k=e;var i=!0;try{t:{g=!1,p&&(p=!1,y(w),w=-1),f=!0;var a=d;try{e:{for(b(e),h=n(l);null!==h&&!(h.expirationTime>e&&P());){var o=h.callback;if("function"==typeof o){h.callback=null,d=h.priorityLevel;var s=o(h.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){h.callback=s,b(e),i=!0;break e}h===n(l)&&r(l),b(e)}else r(l);h=n(l)}if(null!==h)i=!0;else{var c=n(u);null!==c&&M(_,c.startTime-e),i=!1}}break t}finally{h=null,d=a,f=!1}i=void 0}}finally{i?S():x=!1}}}if("function"==typeof v)S=function(){v(T)};else if("undefined"!=typeof MessageChannel){var E=new MessageChannel,F=E.port2;E.port1.onmessage=T,S=function(){F.postMessage(null)}}else S=function(){m(T,0)};function N(){x||(x=!0,S())}function M(e,n){w=m((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(t){t.callback=null},t.unstable_continueExecution=function(){g||f||(g=!0,N())},t.unstable_forceFrameRate=function(t){0>t||125<t||(C=0<t?Math.floor(1e3/t):5)},t.unstable_getCurrentPriorityLevel=function(){return d},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(t){switch(d){case 1:case 2:case 3:var e=3;break;default:e=d}var n=d;d=e;try{return t()}finally{d=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=d;d=t;try{return e()}finally{d=n}},t.unstable_scheduleCallback=function(r,i,a){var o=t.unstable_now();switch(a="object"==typeof a&&null!==a&&"number"==typeof(a=a.delay)&&0<a?o+a:o,r){case 1:var s=-1;break;case 2:s=250;break;case 5:s=**********;break;case 4:s=1e4;break;default:s=5e3}return r={id:c++,callback:i,priorityLevel:r,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>o?(r.sortIndex=a,e(u,r),null===n(l)&&r===n(u)&&(p?(y(w),w=-1):p=!0,M(_,a-o))):(r.sortIndex=s,e(l,r),g||f||(g=!0,N())),r},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(t){var e=d;return function(){var n=d;d=e;try{return t.apply(this,arguments)}finally{d=n}}}}(gn)),gn)),fn.exports);const mn={},yn={};Oe.Node.prototype._applyProps=un;let vn=en.DefaultEventPriority;const bn=setTimeout,_n=clearTimeout;function Sn(t,e,n){e._remove(),t.add(e),e.setZIndex(n.getZIndex()),cn(t)}const xn=Object.freeze(Object.defineProperty({__proto__:null,NotPendingTransition:null,appendChild:function(t,e){e.parent===t?e.moveToTop():t.add(e),cn(t)},appendChildToContainer:function(t,e){e.parent===t?e.moveToTop():t.add(e),cn(t)},appendInitialChild:function(t,e){"string"!=typeof e&&(t.add(e),cn(t))},cancelTimeout:_n,clearContainer:function(t){},commitMount:function(t,e,n){},commitTextUpdate:function(t,e,n){},commitUpdate:function(t,e,n,r){un(t,r,n)},createInstance:function(t,e,n){let r=Oe[t];r||(r=Oe.Group);const i={},a={};for(var o in e){"on"===o.slice(0,2)?a[o]=e[o]:i[o]=e[o]}const s=new r(i);return un(s,a),s},createTextInstance:function(t,e,n){},detachDeletedInstance:function(){},finalizeInitialChildren:function(t,e,n){return!1},getChildHostContext:function(){return mn},getCurrentEventPriority:function(){return en.DefaultEventPriority},getCurrentUpdatePriority:function(){return vn},getInstanceFromScope:function(){return null},getPublicInstance:function(t){return t},getRootHostContext:function(){return mn},hideInstance:function(t){t.hide(),cn(t)},hideTextInstance:function(t){},idlePriority:pn.unstable_IdlePriority,insertBefore:Sn,insertInContainerBefore:function(t,e,n){Sn(t,e,n)},isPrimaryRenderer:!1,maySuspendCommit:function(){return!1},noTimeout:-1,now:pn.unstable_now,preloadInstance:function(){return!0},prepareForCommit:function(){return null},preparePortalMount:function(){return null},prepareScopeUpdate:function(){},prepareUpdate:function(t,e,n,r){return yn},removeChild:function(t,e){e.destroy(),e.off(sn),cn(t)},removeChildFromContainer:function(t,e){e.destroy(),e.off(sn),cn(t)},requestPostPaintCallback:function(){},resetAfterCommit:function(){},resetFormInstance:function(){},resetTextContent:function(t){},resolveUpdatePriority:function(){return en.DiscreteEventPriority},run:pn.unstable_runWithPriority,scheduleMicrotask:t=>{t()},scheduleTimeout:bn,setCurrentUpdatePriority:function(t){vn=t},shouldAttemptEagerTransition:function(){return!1},shouldDeprioritizeSubtree:function(t,e){return!1},shouldSetTextContent:function(t,e){return!1},startSuspendingCommit:function(){},supportsHydration:!1,supportsMicrotasks:!0,supportsMutation:!0,supportsPersistence:!1,suspendInstance:function(){},unhideInstance:function(t,e){(null==e.visible||e.visible)&&t.show()},unhideTextInstance:function(t,e){},waitForCommitToBeReady:function(){return null},warnsIfNotActing:!1},Symbol.toStringTag,{value:"Module"}));function wn(t,e,n){if(!t)return;if(!0===n(t))return t;let r=t.child;for(;r;){const t=wn(r,e,n);if(t)return t;r=r.sibling}}function Cn(t){try{return Object.defineProperties(t,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(e){return t}}const kn=Cn(r.createContext(null));class Pn extends r.Component{render(){return r.createElement(kn.Provider,{value:this._reactInternals},this.props.children)}}const Tn=Symbol.for("react.context"),En=t=>null!==t&&"object"==typeof t&&"$$typeof"in t&&t.$$typeof===Tn;function Fn(){const t=function(){const t=r.useContext(kn);if(null===t)throw new Error("its-fine: useFiber must be called within a <FiberProvider />!");const e=r.useId();return r.useMemo((()=>{for(const n of[t,null==t?void 0:t.alternate]){if(!n)continue;const t=wn(n,!1,(t=>{let n=t.memoizedState;for(;n;){if(n.memoizedState===e)return!0;n=n.next}}));if(t)return t}}),[t,e])}(),[e]=r.useState((()=>new Map));e.clear();let n=t;for(;n;){const t=n.type;En(t)&&t!==kn&&!e.has(t)&&e.set(t,r.use(Cn(t))),n=n.return}return e}if(-1===i.version.indexOf("19"))throw new Error("react-konva version 19 is only compatible with React 19. Make sure to have the last version of react-konva and react or downgrade react-konva to version 18.");const Nn=t=>{const e=i.useRef(null),n=i.useRef(null),a=i.useRef(null),o=function(t){const e=i.useRef({});return i.useLayoutEffect((()=>{e.current=t})),i.useLayoutEffect((()=>()=>{e.current={}}),[]),e.current}(t),s=function(){const t=Fn();return r.useMemo((()=>Array.from(t.keys()).reduce(((e,n)=>i=>r.createElement(e,null,r.createElement(n.Provider,{...i,value:t.get(n)}))),(t=>r.createElement(Pn,{...t})))),[t])}(),l=e=>{const{forwardedRef:n}=t;n&&("function"==typeof n?n(e):n.current=e)};return i.useLayoutEffect((()=>(n.current=new Oe.Stage({width:t.width,height:t.height,container:e.current}),l(n.current),a.current=Dn.createContainer(n.current,en.ConcurrentRoot,null,!1,null,"",console.error,console.error,console.error,null),Dn.updateContainer(i.createElement(s,{},t.children),a.current,null,(()=>{})),()=>{l(null),Dn.updateContainer(null,a.current,null),n.current.destroy()})),[]),i.useLayoutEffect((()=>{l(n.current),un(n.current,t,o),Dn.updateContainer(i.createElement(s,{},t.children),a.current,null)})),i.createElement("div",{ref:e,id:t.id,accessKey:t.accessKey,className:t.className,role:t.role,style:t.style,tabIndex:t.tabIndex,title:t.title})},Mn="Layer",An="Line",Rn="Image",Ln="Transformer",Dn=Qe(xn);Dn.injectIntoDevTools({findHostInstanceByFiber:()=>null,bundleType:0,version:i.version,rendererPackageName:"react-konva"});const zn=i.forwardRef(((t,e)=>i.createElement(Pn,{},i.createElement(Nn,{...t,forwardedRef:e}))));export{Rn as I,ze as K,Mn as L,zn as S,Ln as T,An as a};
