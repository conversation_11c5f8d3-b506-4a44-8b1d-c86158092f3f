import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Share2, Download, ChevronDown, FileImage, FileText, X } from 'lucide-react';
// Using regular div elements with Tailwind classes instead of motion

const Header = ({ title, toggleSidebar, handleExport }) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showNameDialog, setShowNameDialog] = useState(false);
  const [exportType, setExportType] = useState('');
  const [fileName, setFileName] = useState('phac-y-design');
  const dropdownRef = useRef(null);
  const dialogRef = useRef(null);
  const inputRef = useRef(null);

  // Xử lý click bên ngoài dropdown để đóng dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Xử lý click bên ngoài dialog để đóng dialog
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dialogRef.current && !dialogRef.current.contains(event.target)) {
        // Không đóng dialog khi click bên ngoài
        // Người dùng phải click nút Hủy hoặc Xuất
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Tự động focus vào input khi dialog mở
  useEffect(() => {
    if (showNameDialog && inputRef.current) {
      setTimeout(() => {
        inputRef.current.focus();
        inputRef.current.select();
      }, 100);
    }
  }, [showNameDialog]);

  // Xử lý khi người dùng nhấn Enter trong input
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleExportWithName();
    }
  };

  // Xử lý xuất file với tên đã nhập
  const handleExportWithName = () => {
    if (fileName.trim()) {
      handleExport(exportType, fileName.trim());
      setShowNameDialog(false);
      setShowDropdown(false);
    }
  };
  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-16 sm:h-20 flex items-center justify-between px-3 sm:px-6 py-2 sm:py-4">
      {/* Left side - Logo and toggle button */}
      <div className="flex items-center space-x-2 sm:space-x-3">
        <div className="bg-white shadow-md rounded-lg sm:rounded-xl h-[40px] w-[40px] sm:h-[55px] sm:w-[55px] flex items-center justify-center">
          <Link to="/">
            <img src="/images/logo-mini.png" alt="Logo" className="h-7 sm:h-10 w-auto" />
          </Link>
        </div>

        <div
          className="bg-white shadow-md rounded-lg sm:rounded-xl w-[40px] h-[40px] sm:w-[55px] sm:h-[55px] flex items-center justify-center cursor-pointer hover:shadow-dexin transition-all duration-300 transform hover:scale-105 active:scale-95"
          onClick={toggleSidebar}
        >
          <div className="hover:rotate-12 hover:scale-110 transition-transform duration-200">
            <img src="/images/ty_chat_icon.png" alt="Toggle Sidebar" className="h-5 w-5 sm:h-7 sm:w-7" />
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg sm:rounded-xl h-[40px] sm:h-[55px] flex items-center justify-center py-1 sm:py-2 px-3 sm:px-6">
          <h1 className="text-sm sm:text-lg font-medium text-gray-800 truncate max-w-[100px] sm:max-w-full">{title || 'Untitled_1'}</h1>
        </div>
      </div>

      {/* Right side - Action buttons */}
      <div className="flex items-center space-x-2 sm:space-x-4">
        <div className="relative" ref={dropdownRef}>
          <button
            className="flex items-center justify-center space-x-1 sm:space-x-2 py-2 sm:py-3 px-2 sm:px-5 w-[100px] h-[40px] sm:w-[134px] sm:h-[55px] rounded-lg sm:rounded-xl shadow-md text-white bg-dexin-light hover:bg-dexin-light-90 transition-all duration-300 transform hover:scale-[1.03] active:scale-[0.97]"
            onClick={() => setShowDropdown(!showDropdown)}
          >
            <Download size={16} className="flex-shrink-0 sm:hidden" />
            <Download size={20} className="flex-shrink-0 hidden sm:block" />
            <span className="text-xs sm:text-base whitespace-nowrap">Tải xuống</span>
            <ChevronDown size={16} className="ml-1 flex-shrink-0" />
          </button>

          {/* Dropdown menu */}
          {showDropdown && (
            <div className="absolute right-0 mt-1 w-48 bg-white rounded-lg shadow-lg overflow-hidden z-50">
              <button
                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                onClick={() => {
                  setExportType('png');
                  setShowNameDialog(true);
                }}
              >
                <FileImage size={16} className="mr-2 text-gray-500" />
                <span>Xuất PNG</span>
              </button>
              <button
                className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                onClick={() => {
                  setExportType('pdf');
                  setShowNameDialog(true);
                }}
              >
                <FileText size={16} className="mr-2 text-gray-500" />
                <span>Xuất PDF</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Dialog đặt tên file */}
      {showNameDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div
            ref={dialogRef}
            className="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-md w-full mx-4 relative"
          >
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={() => setShowNameDialog(false)}
            >
              <X size={20} />
            </button>

            <h3 className="text-lg sm:text-xl font-medium text-gray-800 mb-4">
              {exportType === 'png' ? 'Xuất ảnh PNG' : 'Xuất file PDF'}
            </h3>

            <div className="mb-4">
              <label htmlFor="fileName" className="block text-sm font-medium text-gray-700 mb-1">
                Tên file
              </label>
              <input
                ref={inputRef}
                type="text"
                id="fileName"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-dexin-light focus:border-dexin-light"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Nhập tên file..."
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300"
                onClick={() => setShowNameDialog(false)}
              >
                Hủy
              </button>
              <button
                className="px-4 py-2 text-sm font-medium text-white bg-dexin-light rounded-md hover:bg-dexin-light-90 focus:outline-none focus:ring-2 focus:ring-dexin-light focus:ring-offset-2"
                onClick={handleExportWithName}
                disabled={!fileName.trim()}
              >
                Xuất
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Header;
