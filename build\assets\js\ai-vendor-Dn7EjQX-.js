import"./router-vendor-BEZ3q80F.js";import{g as e}from"./react-vendor-CFHJfABC.js";var t,n,r={exports:{}},o={};var i,l,s,a,u,c,f=(n||(n=1,r.exports=function(){if(t)return o;t=1;var e=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function r(t,n,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==n.key&&(o=""+n.key),"key"in n)for(var i in r={},n)"key"!==i&&(r[i]=n[i]);else r=n;return n=r.ref,{$$typeof:e,type:t,key:o,ref:void 0!==n?n:null,props:r}}return o.Fragment=n,o.jsx=r,o.jsxs=r,o}()),r.exports);(l=i||(i={})).STRING="string",l.NUMBER="number",l.INTEGER="integer",l.BOOLEAN="boolean",l.ARRAY="array",l.OBJECT="object",(a=s||(s={})).LANGUAGE_UNSPECIFIED="language_unspecified",a.PYTHON="python",(c=u||(u={})).OUTCOME_UNSPECIFIED="outcome_unspecified",c.OUTCOME_OK="outcome_ok",c.OUTCOME_FAILED="outcome_failed",c.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded";
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const d=["user","model","function","system"];var p,h,m,g,y,v,x,k,b,w,E,S,C,I,T,O;(h=p||(p={})).HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",h.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",h.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",h.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",h.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",h.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY",(g=m||(m={})).HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",g.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",g.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",g.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",g.BLOCK_NONE="BLOCK_NONE",(v=y||(y={})).HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",v.NEGLIGIBLE="NEGLIGIBLE",v.LOW="LOW",v.MEDIUM="MEDIUM",v.HIGH="HIGH",(k=x||(x={})).BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",k.SAFETY="SAFETY",k.OTHER="OTHER",(w=b||(b={})).FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",w.STOP="STOP",w.MAX_TOKENS="MAX_TOKENS",w.SAFETY="SAFETY",w.RECITATION="RECITATION",w.LANGUAGE="LANGUAGE",w.BLOCKLIST="BLOCKLIST",w.PROHIBITED_CONTENT="PROHIBITED_CONTENT",w.SPII="SPII",w.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",w.OTHER="OTHER",(S=E||(E={})).TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",S.RETRIEVAL_QUERY="RETRIEVAL_QUERY",S.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",S.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",S.CLASSIFICATION="CLASSIFICATION",S.CLUSTERING="CLUSTERING",(I=C||(C={})).MODE_UNSPECIFIED="MODE_UNSPECIFIED",I.AUTO="AUTO",I.ANY="ANY",I.NONE="NONE",(O=T||(T={})).MODE_UNSPECIFIED="MODE_UNSPECIFIED",O.MODE_DYNAMIC="MODE_DYNAMIC";
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class A extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}}class _ extends A{constructor(e,t){super(e),this.response=t}}class N extends A{constructor(e,t,n,r){super(e),this.status=t,this.statusText=n,this.errorDetails=r}}class P extends A{}class R extends A{}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */var D,L;(L=D||(D={})).GENERATE_CONTENT="generateContent",L.STREAM_GENERATE_CONTENT="streamGenerateContent",L.COUNT_TOKENS="countTokens",L.EMBED_CONTENT="embedContent",L.BATCH_EMBED_CONTENTS="batchEmbedContents";class M{constructor(e,t,n,r,o){this.model=e,this.task=t,this.apiKey=n,this.stream=r,this.requestOptions=o}toString(){var e,t;const n=(null===(e=this.requestOptions)||void 0===e?void 0:e.apiVersion)||"v1beta";let r=`${(null===(t=this.requestOptions)||void 0===t?void 0:t.baseUrl)||"https://generativelanguage.googleapis.com"}/${n}/${this.model}:${this.task}`;return this.stream&&(r+="?alt=sse"),r}}async function F(e){var t;const n=new Headers;n.append("Content-Type","application/json"),n.append("x-goog-api-client",function(e){const t=[];return(null==e?void 0:e.apiClient)&&t.push(e.apiClient),t.push("genai-js/0.24.0"),t.join(" ")}(e.requestOptions)),n.append("x-goog-api-key",e.apiKey);let r=null===(t=e.requestOptions)||void 0===t?void 0:t.customHeaders;if(r){if(!(r instanceof Headers))try{r=new Headers(r)}catch(o){throw new P(`unable to convert customHeaders value ${JSON.stringify(r)} to Headers: ${o.message}`)}for(const[e,t]of r.entries()){if("x-goog-api-key"===e)throw new P(`Cannot set reserved header name ${e}`);if("x-goog-api-client"===e)throw new P(`Header name ${e} can only be set using the apiClient field`);n.append(e,t)}}return n}async function z(e,t,n,r,o,i={},l=fetch){const{url:s,fetchOptions:a}=await async function(e,t,n,r,o,i){const l=new M(e,t,n,r,i);return{url:l.toString(),fetchOptions:Object.assign(Object.assign({},j(i)),{method:"POST",headers:await F(l),body:o})}}(e,t,n,r,o,i);return async function(e,t,n=fetch){let r;try{r=await n(e,t)}catch(o){!function(e,t){let n=e;"AbortError"===n.name?(n=new R(`Request aborted when fetching ${t.toString()}: ${e.message}`),n.stack=e.stack):e instanceof N||e instanceof P||(n=new A(`Error fetching from ${t.toString()}: ${e.message}`),n.stack=e.stack);throw n}(o,e)}r.ok||await async function(e,t){let n,r="";try{const t=await e.json();r=t.error.message,t.error.details&&(r+=` ${JSON.stringify(t.error.details)}`,n=t.error.details)}catch(o){}throw new N(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${r}`,e.status,e.statusText,n)}(r,e);return r}(s,a,l)}function j(e){const t={};if(void 0!==(null==e?void 0:e.signal)||(null==e?void 0:e.timeout)>=0){const n=new AbortController;(null==e?void 0:e.timeout)>=0&&setTimeout((()=>n.abort()),e.timeout),(null==e?void 0:e.signal)&&e.signal.addEventListener("abort",(()=>{n.abort()})),t.signal=n.signal}return t}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function B(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length,V(e.candidates[0]))throw new _(`${q(e)}`,e);return function(e){var t,n,r,o;const i=[];if(null===(n=null===(t=e.candidates)||void 0===t?void 0:t[0].content)||void 0===n?void 0:n.parts)for(const l of null===(o=null===(r=e.candidates)||void 0===r?void 0:r[0].content)||void 0===o?void 0:o.parts)l.text&&i.push(l.text),l.executableCode&&i.push("\n```"+l.executableCode.language+"\n"+l.executableCode.code+"\n```\n"),l.codeExecutionResult&&i.push("\n```\n"+l.codeExecutionResult.output+"\n```\n");return i.length>0?i.join(""):""}(e)}if(e.promptFeedback)throw new _(`Text not available. ${q(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length,V(e.candidates[0]))throw new _(`${q(e)}`,e);return H(e)[0]}if(e.promptFeedback)throw new _(`Function call not available. ${q(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length,V(e.candidates[0]))throw new _(`${q(e)}`,e);return H(e)}if(e.promptFeedback)throw new _(`Function call not available. ${q(e)}`,e)},e}function H(e){var t,n,r,o;const i=[];if(null===(n=null===(t=e.candidates)||void 0===t?void 0:t[0].content)||void 0===n?void 0:n.parts)for(const l of null===(o=null===(r=e.candidates)||void 0===r?void 0:r[0].content)||void 0===o?void 0:o.parts)l.functionCall&&i.push(l.functionCall);return i.length>0?i:void 0}const U=[b.RECITATION,b.SAFETY,b.LANGUAGE];function V(e){return!!e.finishReason&&U.includes(e.finishReason)}function q(e){var t,n,r;let o="";if(e.candidates&&0!==e.candidates.length||!e.promptFeedback){if(null===(r=e.candidates)||void 0===r?void 0:r[0]){const t=e.candidates[0];V(t)&&(o+=`Candidate was blocked due to ${t.finishReason}`,t.finishMessage&&(o+=`: ${t.finishMessage}`))}}else o+="Response was blocked",(null===(t=e.promptFeedback)||void 0===t?void 0:t.blockReason)&&(o+=` due to ${e.promptFeedback.blockReason}`),(null===(n=e.promptFeedback)||void 0===n?void 0:n.blockReasonMessage)&&(o+=`: ${e.promptFeedback.blockReasonMessage}`);return o}function Y(e){return this instanceof Y?(this.v=e,this):new Y(e)}function $(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(e,t||[]),i=[];return r={},l("next"),l("throw"),l("return"),r[Symbol.asyncIterator]=function(){return this},r;function l(e){o[e]&&(r[e]=function(t){return new Promise((function(n,r){i.push([e,t,n,r])>1||s(e,t)}))})}function s(e,t){try{(n=o[e](t)).value instanceof Y?Promise.resolve(n.value.v).then(a,u):c(i[0][2],n)}catch(r){c(i[0][3],r)}var n}function a(e){s("next",e)}function u(e){s("throw",e)}function c(e,t){e(t),i.shift(),i.length&&s(i[0][0],i[0][1])}}"function"==typeof SuppressedError&&SuppressedError;
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const K=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function G(e){const t=function(e){const t=e.getReader();return new ReadableStream({start(e){let n="";return r();function r(){return t.read().then((({value:t,done:o})=>{if(o)return n.trim()?void e.error(new A("Failed to parse stream")):void e.close();n+=t;let i,l=n.match(K);for(;l;){try{i=JSON.parse(l[1])}catch(s){return void e.error(new A(`Error parsing JSON response: "${l[1]}"`))}e.enqueue(i),n=n.substring(l[0].length),l=n.match(K)}return r()})).catch((e=>{let t=e;throw t.stack=e.stack,t="AbortError"===t.name?new R("Request aborted when reading from the stream"):new A("Error reading from the stream"),t}))}}})}(e.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))),[n,r]=t.tee();return{stream:J(n),response:W(r)}}async function W(e){const t=[],n=e.getReader();for(;;){const{done:e,value:r}=await n.read();if(e)return B(X(t));t.push(r)}}function J(e){return $(this,arguments,(function*(){const t=e.getReader();for(;;){const{value:e,done:n}=yield Y(t.read());if(n)break;yield yield Y(B(e))}}))}function X(e){const t=e[e.length-1],n={promptFeedback:null==t?void 0:t.promptFeedback};for(const r of e){if(r.candidates){let e=0;for(const t of r.candidates)if(n.candidates||(n.candidates=[]),n.candidates[e]||(n.candidates[e]={index:e}),n.candidates[e].citationMetadata=t.citationMetadata,n.candidates[e].groundingMetadata=t.groundingMetadata,n.candidates[e].finishReason=t.finishReason,n.candidates[e].finishMessage=t.finishMessage,n.candidates[e].safetyRatings=t.safetyRatings,t.content&&t.content.parts){n.candidates[e].content||(n.candidates[e].content={role:t.content.role||"user",parts:[]});const r={};for(const o of t.content.parts)o.text&&(r.text=o.text),o.functionCall&&(r.functionCall=o.functionCall),o.executableCode&&(r.executableCode=o.executableCode),o.codeExecutionResult&&(r.codeExecutionResult=o.codeExecutionResult),0===Object.keys(r).length&&(r.text=""),n.candidates[e].content.parts.push(r)}e++}r.usageMetadata&&(n.usageMetadata=r.usageMetadata)}return n}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Q(e,t,n,r){return G(await z(t,D.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(n),r))}async function Z(e,t,n,r){const o=await z(t,D.GENERATE_CONTENT,e,!1,JSON.stringify(n),r);return{response:B(await o.json())}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ee(e){if(null!=e)return"string"==typeof e?{role:"system",parts:[{text:e}]}:e.text?{role:"system",parts:[e]}:e.parts?e.role?e:{role:"system",parts:e.parts}:void 0}function te(e){let t=[];if("string"==typeof e)t=[{text:e}];else for(const n of e)"string"==typeof n?t.push({text:n}):t.push(n);return function(e){const t={role:"user",parts:[]},n={role:"function",parts:[]};let r=!1,o=!1;for(const i of e)"functionResponse"in i?(n.parts.push(i),o=!0):(t.parts.push(i),r=!0);if(r&&o)throw new A("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!r&&!o)throw new A("No content is provided for sending chat message.");if(r)return t;return n}(t)}function ne(e){let t;if(e.contents)t=e;else{t={contents:[te(e)]}}return e.systemInstruction&&(t.systemInstruction=ee(e.systemInstruction)),t}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const re=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],oe={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function ie(e){var t;if(void 0===e.candidates||0===e.candidates.length)return!1;const n=null===(t=e.candidates[0])||void 0===t?void 0:t.content;if(void 0===n)return!1;if(void 0===n.parts||0===n.parts.length)return!1;for(const r of n.parts){if(void 0===r||0===Object.keys(r).length)return!1;if(void 0!==r.text&&""===r.text)return!1}return!0}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const le="SILENT_ERROR";class se{constructor(e,t,n,r={}){this.model=t,this.params=n,this._requestOptions=r,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,(null==n?void 0:n.history)&&(!function(e){let t=!1;for(const n of e){const{role:e,parts:r}=n;if(!t&&"user"!==e)throw new A(`First content should be with role 'user', got ${e}`);if(!d.includes(e))throw new A(`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(d)}`);if(!Array.isArray(r))throw new A("Content should have 'parts' property with an array of Parts");if(0===r.length)throw new A("Each Content should have at least one part");const o={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(const t of r)for(const e of re)e in t&&(o[e]+=1);const i=oe[e];for(const t of re)if(!i.includes(t)&&o[t]>0)throw new A(`Content with role '${e}' can't contain '${t}' part`);t=!0}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,t={}){var n,r,o,i,l,s;await this._sendPromise;const a=te(e),u={safetySettings:null===(n=this.params)||void 0===n?void 0:n.safetySettings,generationConfig:null===(r=this.params)||void 0===r?void 0:r.generationConfig,tools:null===(o=this.params)||void 0===o?void 0:o.tools,toolConfig:null===(i=this.params)||void 0===i?void 0:i.toolConfig,systemInstruction:null===(l=this.params)||void 0===l?void 0:l.systemInstruction,cachedContent:null===(s=this.params)||void 0===s?void 0:s.cachedContent,contents:[...this._history,a]},c=Object.assign(Object.assign({},this._requestOptions),t);let f;return this._sendPromise=this._sendPromise.then((()=>Z(this._apiKey,this.model,u,c))).then((e=>{var t;if(ie(e.response)){this._history.push(a);const n=Object.assign({parts:[],role:"model"},null===(t=e.response.candidates)||void 0===t?void 0:t[0].content);this._history.push(n)}else{q(e.response)}f=e})),await this._sendPromise,f}async sendMessageStream(e,t={}){var n,r,o,i,l,s;await this._sendPromise;const a=te(e),u={safetySettings:null===(n=this.params)||void 0===n?void 0:n.safetySettings,generationConfig:null===(r=this.params)||void 0===r?void 0:r.generationConfig,tools:null===(o=this.params)||void 0===o?void 0:o.tools,toolConfig:null===(i=this.params)||void 0===i?void 0:i.toolConfig,systemInstruction:null===(l=this.params)||void 0===l?void 0:l.systemInstruction,cachedContent:null===(s=this.params)||void 0===s?void 0:s.cachedContent,contents:[...this._history,a]},c=Object.assign(Object.assign({},this._requestOptions),t),f=Q(this._apiKey,this.model,u,c);return this._sendPromise=this._sendPromise.then((()=>f)).catch((e=>{throw new Error(le)})).then((e=>e.response)).then((e=>{if(ie(e)){this._history.push(a);const t=Object.assign({},e.candidates[0].content);t.role||(t.role="model"),this._history.push(t)}else{q(e)}})).catch((e=>{e.message})),f}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
class ae{constructor(e,t,n={}){this.apiKey=e,this._requestOptions=n,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=ee(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var n;const r=ne(e),o=Object.assign(Object.assign({},this._requestOptions),t);return Z(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},r),o)}async generateContentStream(e,t={}){var n;const r=ne(e),o=Object.assign(Object.assign({},this._requestOptions),t);return Q(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},r),o)}startChat(e){var t;return new se(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(t=this.cachedContent)||void 0===t?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){const n=function(e,t){var n;let r={model:null==t?void 0:t.model,generationConfig:null==t?void 0:t.generationConfig,safetySettings:null==t?void 0:t.safetySettings,tools:null==t?void 0:t.tools,toolConfig:null==t?void 0:t.toolConfig,systemInstruction:null==t?void 0:t.systemInstruction,cachedContent:null===(n=null==t?void 0:t.cachedContent)||void 0===n?void 0:n.name,contents:[]};const o=null!=e.generateContentRequest;if(e.contents){if(o)throw new P("CountTokensRequest must have one of contents or generateContentRequest, not both.");r.contents=e.contents}else if(o)r=Object.assign(Object.assign({},r),e.generateContentRequest);else{const t=te(e);r.contents=[t]}return{generateContentRequest:r}}(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),r=Object.assign(Object.assign({},this._requestOptions),t);return async function(e,t,n,r){return(await z(t,D.COUNT_TOKENS,e,!1,JSON.stringify(n),r)).json()}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(this.apiKey,this.model,n,r)}async embedContent(e,t={}){const n=function(e){if("string"==typeof e||Array.isArray(e))return{content:te(e)};return e}(e),r=Object.assign(Object.assign({},this._requestOptions),t);return async function(e,t,n,r){return(await z(t,D.EMBED_CONTENT,e,!1,JSON.stringify(n),r)).json()}(this.apiKey,this.model,n,r)}async batchEmbedContents(e,t={}){const n=Object.assign(Object.assign({},this._requestOptions),t);return async function(e,t,n,r){const o=n.requests.map((e=>Object.assign(Object.assign({},e),{model:t})));return(await z(t,D.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:o}),r)).json()}(this.apiKey,this.model,e,n)}}
/**
 * @license
 * Copyright 2024 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ue{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new A("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new ae(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,n){if(!e.name)throw new P("Cached content must contain a `name` field.");if(!e.model)throw new P("Cached content must contain a `model` field.");const r=["model","systemInstruction"];for(const i of r)if((null==t?void 0:t[i])&&e[i]&&(null==t?void 0:t[i])!==e[i]){if("model"===i){if((t.model.startsWith("models/")?t.model.replace("models/",""):t.model)===(e.model.startsWith("models/")?e.model.replace("models/",""):e.model))continue}throw new P(`Different value for "${i}" specified in modelParams (${t[i]}) and cachedContent (${e[i]})`)}const o=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new ae(this.apiKey,o,n)}}function ce(){}const fe=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,de=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,pe={};function he(e,t){return(pe.jsx?de:fe).test(e)}const me=/[ \t\n\f\r]/g;function ge(e){return""===e.replace(me,"")}class ye{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function ve(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new ye(n,r,t)}function xe(e){return e.toLowerCase()}ye.prototype.normal={},ye.prototype.property={},ye.prototype.space=void 0;class ke{constructor(e,t){this.attribute=t,this.property=e}}ke.prototype.attribute="",ke.prototype.booleanish=!1,ke.prototype.boolean=!1,ke.prototype.commaOrSpaceSeparated=!1,ke.prototype.commaSeparated=!1,ke.prototype.defined=!1,ke.prototype.mustUseProperty=!1,ke.prototype.number=!1,ke.prototype.overloadedBoolean=!1,ke.prototype.property="",ke.prototype.spaceSeparated=!1,ke.prototype.space=void 0;let be=0;const we=Ae(),Ee=Ae(),Se=Ae(),Ce=Ae(),Ie=Ae(),Te=Ae(),Oe=Ae();function Ae(){return 2**++be}const _e=Object.freeze(Object.defineProperty({__proto__:null,boolean:we,booleanish:Ee,commaOrSpaceSeparated:Oe,commaSeparated:Te,number:Ce,overloadedBoolean:Se,spaceSeparated:Ie},Symbol.toStringTag,{value:"Module"})),Ne=Object.keys(_e);class Pe extends ke{constructor(e,t,n,r){let o=-1;if(super(e,t),Re(this,"space",r),"number"==typeof n)for(;++o<Ne.length;){const e=Ne[o];Re(this,Ne[o],(n&_e[e])===_e[e])}}}function Re(e,t,n){n&&(e[t]=n)}function De(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new Pe(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[xe(r)]=r,n[xe(i.attribute)]=r}return new ye(t,n,e.space)}Pe.prototype.defined=!0;const Le=De({properties:{ariaActiveDescendant:null,ariaAtomic:Ee,ariaAutoComplete:null,ariaBusy:Ee,ariaChecked:Ee,ariaColCount:Ce,ariaColIndex:Ce,ariaColSpan:Ce,ariaControls:Ie,ariaCurrent:null,ariaDescribedBy:Ie,ariaDetails:null,ariaDisabled:Ee,ariaDropEffect:Ie,ariaErrorMessage:null,ariaExpanded:Ee,ariaFlowTo:Ie,ariaGrabbed:Ee,ariaHasPopup:null,ariaHidden:Ee,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:Ie,ariaLevel:Ce,ariaLive:null,ariaModal:Ee,ariaMultiLine:Ee,ariaMultiSelectable:Ee,ariaOrientation:null,ariaOwns:Ie,ariaPlaceholder:null,ariaPosInSet:Ce,ariaPressed:Ee,ariaReadOnly:Ee,ariaRelevant:null,ariaRequired:Ee,ariaRoleDescription:Ie,ariaRowCount:Ce,ariaRowIndex:Ce,ariaRowSpan:Ce,ariaSelected:Ee,ariaSetSize:Ce,ariaSort:null,ariaValueMax:Ce,ariaValueMin:Ce,ariaValueNow:Ce,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function Me(e,t){return t in e?e[t]:t}function Fe(e,t){return Me(e,t.toLowerCase())}const ze=De({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Te,acceptCharset:Ie,accessKey:Ie,action:null,allow:null,allowFullScreen:we,allowPaymentRequest:we,allowUserMedia:we,alt:null,as:null,async:we,autoCapitalize:null,autoComplete:Ie,autoFocus:we,autoPlay:we,blocking:Ie,capture:null,charSet:null,checked:we,cite:null,className:Ie,cols:Ce,colSpan:null,content:null,contentEditable:Ee,controls:we,controlsList:Ie,coords:Ce|Te,crossOrigin:null,data:null,dateTime:null,decoding:null,default:we,defer:we,dir:null,dirName:null,disabled:we,download:Se,draggable:Ee,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:we,formTarget:null,headers:Ie,height:Ce,hidden:we,high:Ce,href:null,hrefLang:null,htmlFor:Ie,httpEquiv:Ie,id:null,imageSizes:null,imageSrcSet:null,inert:we,inputMode:null,integrity:null,is:null,isMap:we,itemId:null,itemProp:Ie,itemRef:Ie,itemScope:we,itemType:Ie,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:we,low:Ce,manifest:null,max:null,maxLength:Ce,media:null,method:null,min:null,minLength:Ce,multiple:we,muted:we,name:null,nonce:null,noModule:we,noValidate:we,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:we,optimum:Ce,pattern:null,ping:Ie,placeholder:null,playsInline:we,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:we,referrerPolicy:null,rel:Ie,required:we,reversed:we,rows:Ce,rowSpan:Ce,sandbox:Ie,scope:null,scoped:we,seamless:we,selected:we,shadowRootClonable:we,shadowRootDelegatesFocus:we,shadowRootMode:null,shape:null,size:Ce,sizes:null,slot:null,span:Ce,spellCheck:Ee,src:null,srcDoc:null,srcLang:null,srcSet:null,start:Ce,step:null,style:null,tabIndex:Ce,target:null,title:null,translate:null,type:null,typeMustMatch:we,useMap:null,value:Ee,width:Ce,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:Ie,axis:null,background:null,bgColor:null,border:Ce,borderColor:null,bottomMargin:Ce,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:we,declare:we,event:null,face:null,frame:null,frameBorder:null,hSpace:Ce,leftMargin:Ce,link:null,longDesc:null,lowSrc:null,marginHeight:Ce,marginWidth:Ce,noResize:we,noHref:we,noShade:we,noWrap:we,object:null,profile:null,prompt:null,rev:null,rightMargin:Ce,rules:null,scheme:null,scrolling:Ee,standby:null,summary:null,text:null,topMargin:Ce,valueType:null,version:null,vAlign:null,vLink:null,vSpace:Ce,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:we,disableRemotePlayback:we,prefix:null,property:null,results:Ce,security:null,unselectable:null},space:"html",transform:Fe}),je=De({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Oe,accentHeight:Ce,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:Ce,amplitude:Ce,arabicForm:null,ascent:Ce,attributeName:null,attributeType:null,azimuth:Ce,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:Ce,by:null,calcMode:null,capHeight:Ce,className:Ie,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:Ce,diffuseConstant:Ce,direction:null,display:null,dur:null,divisor:Ce,dominantBaseline:null,download:we,dx:null,dy:null,edgeMode:null,editable:null,elevation:Ce,enableBackground:null,end:null,event:null,exponent:Ce,externalResourcesRequired:null,fill:null,fillOpacity:Ce,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Te,g2:Te,glyphName:Te,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:Ce,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:Ce,horizOriginX:Ce,horizOriginY:Ce,id:null,ideographic:Ce,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:Ce,k:Ce,k1:Ce,k2:Ce,k3:Ce,k4:Ce,kernelMatrix:Oe,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:Ce,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:Ce,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:Ce,overlineThickness:Ce,paintOrder:null,panose1:null,path:null,pathLength:Ce,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:Ie,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:Ce,pointsAtY:Ce,pointsAtZ:Ce,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Oe,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Oe,rev:Oe,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Oe,requiredFeatures:Oe,requiredFonts:Oe,requiredFormats:Oe,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:Ce,specularExponent:Ce,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:Ce,strikethroughThickness:Ce,string:null,stroke:null,strokeDashArray:Oe,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:Ce,strokeOpacity:Ce,strokeWidth:null,style:null,surfaceScale:Ce,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Oe,tabIndex:Ce,tableValues:null,target:null,targetX:Ce,targetY:Ce,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Oe,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:Ce,underlineThickness:Ce,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:Ce,values:null,vAlphabetic:Ce,vMathematical:Ce,vectorEffect:null,vHanging:Ce,vIdeographic:Ce,version:null,vertAdvY:Ce,vertOriginX:Ce,vertOriginY:Ce,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:Ce,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Me}),Be=De({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),He=De({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Fe}),Ue=De({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),Ve={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},qe=/[A-Z]/g,Ye=/-[a-z]/g,$e=/^data[-\w.:]+$/i;function Ke(e){return"-"+e.toLowerCase()}function Ge(e){return e.charAt(1).toUpperCase()}const We=ve([Le,ze,Be,He,Ue],"html"),Je=ve([Le,je,Be,He,Ue],"svg");var Xe,Qe,Ze,et={};function tt(){if(Ze)return et;Ze=1;var e=et&&et.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(et,"__esModule",{value:!0}),et.default=function(e,n){var r=null;if(!e||"string"!=typeof e)return r;var o=(0,t.default)(e),i="function"==typeof n;return o.forEach((function(e){if("declaration"===e.type){var t=e.property,o=e.value;i?n(t,o,e):o&&((r=r||{})[t]=o)}})),r};var t=e(function(){if(Qe)return Xe;Qe=1;var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,t=/\n/g,n=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,i=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,l=/^[;\s]*/,s=/^\s+|\s+$/g,a="";function u(e){return e?e.replace(s,a):a}return Xe=function(s,c){if("string"!=typeof s)throw new TypeError("First argument must be a string");if(!s)return[];c=c||{};var f=1,d=1;function p(e){var n=e.match(t);n&&(f+=n.length);var r=e.lastIndexOf("\n");d=~r?e.length-r:d+e.length}function h(){var e={line:f,column:d};return function(t){return t.position=new m(e),v(),t}}function m(e){this.start=e,this.end={line:f,column:d},this.source=c.source}function g(e){var t=new Error(c.source+":"+f+":"+d+": "+e);if(t.reason=e,t.filename=c.source,t.line=f,t.column=d,t.source=s,!c.silent)throw t}function y(e){var t=e.exec(s);if(t){var n=t[0];return p(n),s=s.slice(n.length),t}}function v(){y(n)}function x(e){var t;for(e=e||[];t=k();)!1!==t&&e.push(t);return e}function k(){var e=h();if("/"==s.charAt(0)&&"*"==s.charAt(1)){for(var t=2;a!=s.charAt(t)&&("*"!=s.charAt(t)||"/"!=s.charAt(t+1));)++t;if(t+=2,a===s.charAt(t-1))return g("End of comment missing");var n=s.slice(2,t-2);return d+=2,p(n),s=s.slice(t),d+=2,e({type:"comment",comment:n})}}function b(){var t=h(),n=y(r);if(n){if(k(),!y(o))return g("property missing ':'");var s=y(i),c=t({type:"declaration",property:u(n[0].replace(e,a)),value:s?u(s[0].replace(e,a)):a});return y(l),c}}return m.prototype.content=s,v(),function(){var e,t=[];for(x(t);e=b();)!1!==e&&(t.push(e),x(t));return t}()}}());return et}var nt,rt,ot,it={};function lt(){if(nt)return it;nt=1,Object.defineProperty(it,"__esModule",{value:!0}),it.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,t=/-([a-z])/g,n=/^[^-]+$/,r=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,i=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};return it.camelCase=function(s,a){return void 0===a&&(a={}),function(t){return!t||n.test(t)||e.test(t)}(s)?s:(s=s.toLowerCase(),(s=a.reactCompat?s.replace(o,l):s.replace(r,l)).replace(t,i))},it}const st=e(function(){if(ot)return rt;ot=1;var e=(rt&&rt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(tt()),t=lt();function n(n,r){var o={};return n&&"string"==typeof n?((0,e.default)(n,(function(e,n){e&&n&&(o[(0,t.camelCase)(e,r)]=n)})),o):o}return n.default=n,rt=n}()),at=ct("end"),ut=ct("start");function ct(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function ft(e){return e&&"object"==typeof e?"position"in e||"type"in e?pt(e.position):"start"in e||"end"in e?pt(e):"line"in e||"column"in e?dt(e):"":""}function dt(e){return ht(e&&e.line)+":"+ht(e&&e.column)}function pt(e){return dt(e&&e.start)+"-"+dt(e&&e.end)}function ht(e){return e&&"number"==typeof e?e:1}class mt extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",o={},i=!1;if(t&&(o="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!o.cause&&e&&(i=!0,r=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){const e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}const l=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=l?l.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=l?l.line:void 0,this.name=ft(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=i&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}mt.prototype.file="",mt.prototype.name="",mt.prototype.reason="",mt.prototype.message="",mt.prototype.stack="",mt.prototype.column=void 0,mt.prototype.line=void 0,mt.prototype.ancestors=void 0,mt.prototype.cause=void 0,mt.prototype.fatal=void 0,mt.prototype.place=void 0,mt.prototype.ruleId=void 0,mt.prototype.source=void 0;const gt={}.hasOwnProperty,yt=new Map,vt=/[A-Z]/g,xt=new Set(["table","tbody","thead","tfoot","tr"]),kt=new Set(["td","th"]),bt="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function wt(e,t){if(!t||void 0===t.Fragment)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if("function"!=typeof t.jsxDEV)throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=function(e,t){return n;function n(n,r,o,i){const l=Array.isArray(o.children),s=ut(n);return t(r,o,i,l,{columnNumber:s?s.column-1:void 0,fileName:e,lineNumber:s?s.line:void 0},void 0)}}(n,t.jsxDEV)}else{if("function"!=typeof t.jsx)throw new TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw new TypeError("Expected `jsxs` in production options");r=function(e,t,n){return r;function r(e,r,o,i){const l=Array.isArray(o.children)?n:t;return i?l(r,o,i):l(r,o)}}(0,t.jsx,t.jsxs)}const o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?Je:We,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},i=Et(o,e,void 0);return i&&"string"!=typeof i?i:o.create(e,o.Fragment,{children:i||void 0},void 0)}function Et(e,t,n){return"element"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(o=Je,e.schema=o);e.ancestors.push(t);const i=Ot(e,t.tagName,!1),l=function(e,t){const n={};let r,o;for(o in t.properties)if("children"!==o&&gt.call(t.properties,o)){const i=Tt(e,o,t.properties[o]);if(i){const[o,l]=i;e.tableCellAlignToStyle&&"align"===o&&"string"==typeof l&&kt.has(t.tagName)?r=l:n[o]=l}}if(r){(n.style||(n.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=r}return n}(e,t);let s=It(e,t);xt.has(t.tagName)&&(s=s.filter((function(e){return"string"!=typeof e||!("object"==typeof(t=e)?"text"===t.type&&ge(t.value):ge(t));var t})));return St(e,l,i,t),Ct(l,s),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){const n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}At(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.name&&"html"===r.space&&(o=Je,e.schema=o);e.ancestors.push(t);const i=null===t.name?e.Fragment:Ot(e,t.name,!0),l=function(e,t){const n={};for(const r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){const t=r.data.estree.body[0];t.type;const o=t.expression;o.type;const i=o.properties[0];i.type,Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else At(e,t.position);else{const o=r.name;let i;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){const t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else At(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),s=It(e,t);return St(e,l,i,t),Ct(l,s),e.ancestors.pop(),e.schema=r,e.create(t,i,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);At(e,t.position)}(e,t):"root"===t.type?function(e,t,n){const r={};return Ct(r,It(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?function(e,t){return t.value}(0,t):void 0}function St(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function Ct(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function It(e,t){const n=[];let r=-1;const o=e.passKeys?new Map:yt;for(;++r<t.children.length;){const i=t.children[r];let l;if(e.passKeys){const e="element"===i.type?i.tagName:"mdxJsxFlowElement"===i.type||"mdxJsxTextElement"===i.type?i.name:void 0;if(e){const t=o.get(e)||0;l=e+"-"+t,o.set(e,t+1)}}const s=Et(e,i,l);void 0!==s&&n.push(s)}return n}function Tt(e,t,n){const r=function(e,t){const n=xe(t);let r=t,o=ke;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&$e.test(t)){if("-"===t.charAt(4)){const e=t.slice(5).replace(Ye,Ge);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{const e=t.slice(4);if(!Ye.test(e)){let n=e.replace(qe,Ke);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}o=Pe}return new o(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e){const t={};return(""===e[e.length-1]?[...e,""]:e).join((t.padRight?" ":"")+","+(!1===t.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return st(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const t=n,r=new mt("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw r.file=e.filePath||void 0,r.url=bt+"#cannot-parse-style-attribute",r}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){const t={};let n;for(n in e)gt.call(e,n)&&(t[_t(n)]=e[n]);return t}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?Ve[r.property]||r.property:r.attribute,n]}}function Ot(e,t,n){let r;if(n)if(t.includes(".")){const e=t.split(".");let n,o=-1;for(;++o<e.length;){const t=he(e[o])?{type:"Identifier",name:e[o]}:{type:"Literal",value:e[o]};n=n?{type:"MemberExpression",object:n,property:t,computed:Boolean(o&&"Literal"===t.type),optional:!1}:t}r=n}else r=he(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){const t=r.value;return gt.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);At(e)}function At(e,t){const n=new mt("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=bt+"#cannot-handle-mdx-estrees-without-createevaluater",n}function _t(e){let t=e.replace(vt,Nt);return"ms-"===t.slice(0,3)&&(t="-"+t),t}function Nt(e){return"-"+e.toLowerCase()}const Pt={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},Rt={};function Dt(e,t){return Lt(e,"boolean"!=typeof Rt.includeImageAlt||Rt.includeImageAlt,"boolean"!=typeof Rt.includeHtml||Rt.includeHtml)}function Lt(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Mt(e.children,t,n)}return Array.isArray(e)?Mt(e,t,n):""}function Mt(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=Lt(e[o],t,n);return r.join("")}const Ft=document.createElement("i");function zt(e){const t="&"+e+";";Ft.innerHTML=t;const n=Ft.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&(n!==t&&n)}function jt(e,t,n,r){const o=e.length;let i,l=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)i=Array.from(r),i.unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);l<r.length;)i=r.slice(l,l+1e4),i.unshift(t,0),e.splice(...i),l+=1e4,t+=1e4}function Bt(e,t){return e.length>0?(jt(e,e.length,0,t),e):t}const Ht={}.hasOwnProperty;function Ut(e){const t={};let n=-1;for(;++n<e.length;)Vt(t,e[n]);return t}function Vt(e,t){let n;for(n in t){const r=(Ht.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];let i;if(o)for(i in o){Ht.call(r,i)||(r[i]=[]);const e=o[i];qt(r[i],Array.isArray(e)?e:e?[e]:[])}}}function qt(e,t){let n=-1;const r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);jt(e,0,0,r)}function Yt(e,t){const n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||!(65535&~n)||65534==(65535&n)||n>1114111?"�":String.fromCodePoint(n)}function $t(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const Kt=ln(/[A-Za-z]/),Gt=ln(/[\dA-Za-z]/),Wt=ln(/[#-'*+\--9=?A-Z^-~]/);function Jt(e){return null!==e&&(e<32||127===e)}const Xt=ln(/\d/),Qt=ln(/[\dA-Fa-f]/),Zt=ln(/[!-/:-@[-`{-~]/);function en(e){return null!==e&&e<-2}function tn(e){return null!==e&&(e<0||32===e)}function nn(e){return-2===e||-1===e||32===e}const rn=ln(new RegExp("\\p{P}|\\p{S}","u")),on=ln(/\s/);function ln(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function sn(e){const t=[];let n=-1,r=0,o=0;for(;++n<e.length;){const i=e.charCodeAt(n);let l="";if(37===i&&Gt(e.charCodeAt(n+1))&&Gt(e.charCodeAt(n+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(l=String.fromCharCode(i));else if(i>55295&&i<57344){const t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(l=String.fromCharCode(i,t),o=1):l="�"}else l=String.fromCharCode(i);l&&(t.push(e.slice(r,n),encodeURIComponent(l)),r=n+o+1,l=""),o&&(n+=o,o=0)}return t.join("")+e.slice(r)}function an(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){if(nn(r))return e.enter(n),l(r);return t(r)};function l(r){return nn(r)&&i++<o?(e.consume(r),l):(e.exit(n),t(r))}}const un={tokenize:function(e){const t=e.attempt(this.parser.constructs.contentInitial,(function(n){if(null===n)return void e.consume(n);return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),an(e,t,"linePrefix")}),(function(t){return e.enter("paragraph"),r(t)}));let n;return t;function r(t){const r=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=r),n=r,o(t)}function o(t){return null===t?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(t)):en(t)?(e.consume(t),e.exit("chunkText"),r):(e.consume(t),o)}}};const cn={tokenize:function(e){const t=this,n=[];let r,o,i,l=0;return s;function s(r){if(l<n.length){const o=n[l];return t.containerState=o[1],e.attempt(o[0].continuation,a,u)(r)}return u(r)}function a(e){if(l++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,r&&v();const n=t.events.length;let o,i=n;for(;i--;)if("exit"===t.events[i][0]&&"chunkFlow"===t.events[i][1].type){o=t.events[i][1].end;break}y(l);let s=n;for(;s<t.events.length;)t.events[s][1].end={...o},s++;return jt(t.events,i+1,0,t.events.slice(n)),t.events.length=s,u(e)}return s(e)}function u(o){if(l===n.length){if(!r)return d(o);if(r.currentConstruct&&r.currentConstruct.concrete)return h(o);t.interrupt=Boolean(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(fn,c,f)(o)}function c(e){return r&&v(),y(l),d(e)}function f(e){return t.parser.lazy[t.now().line]=l!==n.length,i=t.now().offset,h(e)}function d(n){return t.containerState={},e.attempt(fn,p,h)(n)}function p(e){return l++,n.push([t.currentConstruct,t.containerState]),d(e)}function h(n){return null===n?(r&&v(),y(0),void e.consume(n)):(r=r||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:r,contentType:"flow",previous:o}),m(n))}function m(n){return null===n?(g(e.exit("chunkFlow"),!0),y(0),void e.consume(n)):en(n)?(e.consume(n),g(e.exit("chunkFlow")),l=0,t.interrupt=void 0,s):(e.consume(n),m)}function g(e,n){const s=t.sliceStream(e);if(n&&s.push(null),e.previous=o,o&&(o.next=e),o=e,r.defineSkip(e.start),r.write(s),t.parser.lazy[e.start.line]){let e=r.events.length;for(;e--;)if(r.events[e][1].start.offset<i&&(!r.events[e][1].end||r.events[e][1].end.offset>i))return;const n=t.events.length;let o,s,a=n;for(;a--;)if("exit"===t.events[a][0]&&"chunkFlow"===t.events[a][1].type){if(o){s=t.events[a][1].end;break}o=!0}for(y(l),e=n;e<t.events.length;)t.events[e][1].end={...s},e++;jt(t.events,a+1,0,t.events.slice(n)),t.events.length=e}}function y(r){let o=n.length;for(;o-- >r;){const r=n[o];t.containerState=r[1],r[0].exit.call(t,e)}n.length=r}function v(){r.write([null]),o=void 0,r=void 0,t.containerState._closeFlow=void 0}}},fn={tokenize:function(e,t,n){return an(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};function dn(e){return null===e||tn(e)||on(e)?1:rn(e)?2:void 0}function pn(e,t,n){const r=[];let o=-1;for(;++o<e.length;){const i=e[o].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}const hn={name:"attention",resolveAll:function(e,t){let n,r,o,i,l,s,a,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close)for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;s=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;const f={...e[n][1].end},d={...e[c][1].start};mn(f,-s),mn(d,s),i={type:s>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},l={type:s>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:d},o={type:s>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:s>1?"strong":"emphasis",start:{...i.start},end:{...l.end}},e[n][1].end={...i.start},e[c][1].start={...l.end},a=[],e[n][1].end.offset-e[n][1].start.offset&&(a=Bt(a,[["enter",e[n][1],t],["exit",e[n][1],t]])),a=Bt(a,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",o,t]]),a=Bt(a,pn(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),a=Bt(a,[["exit",o,t],["enter",l,t],["exit",l,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,a=Bt(a,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,jt(e,n-1,c-n+3,a),c=n+a.length-u-2;break}c=-1;for(;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,o=dn(r);let i;return function(t){return i=t,e.enter("attentionSequence"),l(t)};function l(s){if(s===i)return e.consume(s),l;const a=e.exit("attentionSequence"),u=dn(s),c=!u||2===u&&o||n.includes(s),f=!o||2===o&&u||n.includes(r);return a._open=Boolean(42===i?c:c&&(o||!f)),a._close=Boolean(42===i?f:f&&(u||!c)),t(s)}}};function mn(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const gn={name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o};function o(t){return Kt(t)?(e.consume(t),i):64===t?n(t):a(t)}function i(e){return 43===e||45===e||46===e||Gt(e)?(r=1,l(e)):a(e)}function l(t){return 58===t?(e.consume(t),r=0,s):(43===t||45===t||46===t||Gt(t))&&r++<32?(e.consume(t),l):(r=0,a(t))}function s(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||Jt(r)?n(r):(e.consume(r),s)}function a(t){return 64===t?(e.consume(t),u):Wt(t)?(e.consume(t),a):n(t)}function u(e){return Gt(e)?c(e):n(e)}function c(n){return 46===n?(e.consume(n),r=0,u):62===n?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t):f(n)}function f(t){if((45===t||Gt(t))&&r++<63){const n=45===t?f:c;return e.consume(t),n}return n(t)}}};const yn={partial:!0,tokenize:function(e,t,n){return function(t){return nn(t)?an(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||en(e)?t(e):n(e)}}};const vn={continuation:{tokenize:function(e,t,n){const r=this;return function(t){if(nn(t))return an(e,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t);return o(t)};function o(r){return e.attempt(vn,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){const r=this;return function(t){if(62===t){const n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),o}return n(t)};function o(n){return nn(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};const xn={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return Zt(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}};const kn={name:"characterReference",tokenize:function(e,t,n){const r=this;let o,i,l=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),s};function s(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),a):(e.enter("characterReferenceValue"),o=31,i=Gt,u(t))}function a(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,i=Qt,u):(e.enter("characterReferenceValue"),o=7,i=Xt,u(t))}function u(s){if(59===s&&l){const o=e.exit("characterReferenceValue");return i!==Gt||zt(r.sliceSerialize(o))?(e.enter("characterReferenceMarker"),e.consume(s),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(s)}return i(s)&&l++<o?(e.consume(s),u):n(s)}}};const bn={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(null===t)return n(t);return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},wn={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){const r=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return l;function l(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a}function a(t){return e.enter("codeFencedFence"),nn(t)?an(e,u,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):u(t)}function u(t){return t===i?(e.enter("codeFencedFenceSequence"),c(t)):n(t)}function c(t){return t===i?(o++,e.consume(t),c):o>=s?(e.exit("codeFencedFenceSequence"),nn(t)?an(e,f,"whitespace")(t):f(t)):n(t)}function f(r){return null===r||en(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}};let i,l=0,s=0;return function(t){return function(t){const n=r.events[r.events.length-1];return l=n&&"linePrefix"===n[1].type?n[2].sliceSerialize(n[1],!0).length:0,i=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),a(t)}(t)};function a(t){return t===i?(s++,e.consume(t),a):s<3?n(t):(e.exit("codeFencedFenceSequence"),nn(t)?an(e,u,"whitespace")(t):u(t))}function u(n){return null===n||en(n)?(e.exit("codeFencedFence"),r.interrupt?t(n):e.check(bn,p,v)(n)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),c(n))}function c(t){return null===t||en(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(t)):nn(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),an(e,f,"whitespace")(t)):96===t&&t===i?n(t):(e.consume(t),c)}function f(t){return null===t||en(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),d(t))}function d(t){return null===t||en(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(t)):96===t&&t===i?n(t):(e.consume(t),d)}function p(t){return e.attempt(o,v,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),m}function m(t){return l>0&&nn(t)?an(e,g,"linePrefix",l+1)(t):g(t)}function g(t){return null===t||en(t)?e.check(bn,p,v)(t):(e.enter("codeFlowValue"),y(t))}function y(t){return null===t||en(t)?(e.exit("codeFlowValue"),g(t)):(e.consume(t),y)}function v(n){return e.exit("codeFenced"),t(n)}}};const En={name:"codeIndented",tokenize:function(e,t,n){const r=this;return function(t){return e.enter("codeIndented"),an(e,o,"linePrefix",5)(t)};function o(e){const t=r.events[r.events.length-1];return t&&"linePrefix"===t[1].type&&t[2].sliceSerialize(t[1],!0).length>=4?i(e):n(e)}function i(t){return null===t?s(t):en(t)?e.attempt(Sn,i,s)(t):(e.enter("codeFlowValue"),l(t))}function l(t){return null===t||en(t)?(e.exit("codeFlowValue"),i(t)):(e.consume(t),l)}function s(n){return e.exit("codeIndented"),t(n)}}},Sn={partial:!0,tokenize:function(e,t,n){const r=this;return o;function o(t){return r.parser.lazy[r.now().line]?n(t):en(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o):an(e,i,"linePrefix",5)(t)}function i(e){const i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):en(e)?o(e):n(e)}}};const Cn={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,o=3;if(!("lineEnding"!==e[o][1].type&&"space"!==e[o][1].type||"lineEnding"!==e[r][1].type&&"space"!==e[r][1].type))for(t=o;++t<r;)if("codeTextData"===e[t][1].type){e[o][1].type="codeTextPadding",e[r][1].type="codeTextPadding",o+=2,r-=2;break}t=o-1,r++;for(;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):t!==r&&"lineEnding"!==e[t][1].type||(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,o,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),l(t)};function l(t){return 96===t?(e.consume(t),i++,l):(e.exit("codeTextSequence"),s(t))}function s(t){return null===t?n(t):32===t?(e.enter("space"),e.consume(t),e.exit("space"),s):96===t?(o=e.enter("codeTextSequence"),r=0,u(t)):en(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s):(e.enter("codeTextData"),a(t))}function a(t){return null===t||32===t||96===t||en(t)?(e.exit("codeTextData"),s(t)):(e.consume(t),a)}function u(n){return 96===n?(e.consume(n),r++,u):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(o.type="codeTextData",a(n))}}};class In{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){const n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){const r=t||0;this.setCursor(Math.trunc(e));const o=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return n&&Tn(this.left,n),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),Tn(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),Tn(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&0===this.right.length||e<0&&0===this.left.length))if(e<this.left.length){const t=this.left.splice(e,Number.POSITIVE_INFINITY);Tn(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);Tn(this.left,t.reverse())}}}function Tn(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function On(e){const t={};let n,r,o,i,l,s,a,u=-1;const c=new In(e);for(;++u<c.length;){for(;u in t;)u=t[u];if(n=c.get(u),u&&"chunkFlow"===n[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&(s=n[1]._tokenizer.events,o=0,o<s.length&&"lineEndingBlank"===s[o][1].type&&(o+=2),o<s.length&&"content"===s[o][1].type))for(;++o<s.length&&"content"!==s[o][1].type;)"chunkText"===s[o][1].type&&(s[o][1]._isInFirstContentOfListItem=!0,o++);if("enter"===n[0])n[1].contentType&&(Object.assign(t,An(c,u)),u=t[u],a=!0);else if(n[1]._container){for(o=u,r=void 0;o--;)if(i=c.get(o),"lineEnding"===i[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(r&&(c.get(r)[1].type="lineEndingBlank"),i[1].type="lineEnding",r=o);else if("linePrefix"!==i[1].type&&"listItemIndent"!==i[1].type)break;r&&(n[1].end={...c.get(r)[1].start},l=c.slice(r,u),l.unshift(n),c.splice(r,u-r+1,l))}}return jt(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}function An(e,t){const n=e.get(t)[1],r=e.get(t)[2];let o=t-1;const i=[];let l=n._tokenizer;l||(l=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));const s=l.events,a=[],u={};let c,f,d=-1,p=n,h=0,m=0;const g=[m];for(;p;){for(;e.get(++o)[1]!==p;);i.push(o),p._tokenizer||(c=r.sliceStream(p),p.next||c.push(null),f&&l.defineSkip(p.start),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(c),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),f=p,p=p.next}for(p=n;++d<s.length;)"exit"===s[d][0]&&"enter"===s[d-1][0]&&s[d][1].type===s[d-1][1].type&&s[d][1].start.line!==s[d][1].end.line&&(m=d+1,g.push(m),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(l.events=[],p?(p._tokenizer=void 0,p.previous=void 0):g.pop(),d=g.length;d--;){const t=s.slice(g[d],g[d+1]),n=i.pop();a.push([n,n+t.length-1]),e.splice(n,2,t)}for(a.reverse(),d=-1;++d<a.length;)u[h+a[d][0]]=h+a[d][1],h+=a[d][1]-a[d][0]-1;return u}const _n={resolve:function(e){return On(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?o(t):en(t)?e.check(Nn,i,o)(t):(e.consume(t),r)}function o(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},Nn={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),an(e,o,"linePrefix")};function o(o){if(null===o||en(o))return n(o);const i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(o):e.interrupt(r.parser.constructs.flow,n,t)(o)}}};function Pn(e,t,n,r,o,i,l,s,a){const u=a||Number.POSITIVE_INFINITY;let c=0;return function(t){if(60===t)return e.enter(r),e.enter(o),e.enter(i),e.consume(t),e.exit(i),f;if(null===t||32===t||41===t||Jt(t))return n(t);return e.enter(r),e.enter(l),e.enter(s),e.enter("chunkString",{contentType:"string"}),h(t)};function f(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(o),e.exit(r),t):(e.enter(s),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(s),f(t)):null===t||60===t||en(t)?n(t):(e.consume(t),92===t?p:d)}function p(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function h(o){return c||null!==o&&41!==o&&!tn(o)?c<u&&40===o?(e.consume(o),c++,h):41===o?(e.consume(o),c--,h):null===o||32===o||40===o||Jt(o)?n(o):(e.consume(o),92===o?m:h):(e.exit("chunkString"),e.exit(s),e.exit(l),e.exit(r),t(o))}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function Rn(e,t,n,r,o,i){const l=this;let s,a=0;return function(t){return e.enter(r),e.enter(o),e.consume(t),e.exit(o),e.enter(i),u};function u(f){return a>999||null===f||91===f||93===f&&!s||94===f&&!a&&"_hiddenFootnoteSupport"in l.parser.constructs?n(f):93===f?(e.exit(i),e.enter(o),e.consume(f),e.exit(o),e.exit(r),t):en(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(t){return null===t||91===t||93===t||en(t)||a++>999?(e.exit("chunkString"),u(t)):(e.consume(t),s||(s=!nn(t)),92===t?f:c)}function f(t){return 91===t||92===t||93===t?(e.consume(t),a++,c):c(t)}}function Dn(e,t,n,r,o,i){let l;return function(t){if(34===t||39===t||40===t)return e.enter(r),e.enter(o),e.consume(t),e.exit(o),l=40===t?41:t,s;return n(t)};function s(n){return n===l?(e.enter(o),e.consume(n),e.exit(o),e.exit(r),t):(e.enter(i),a(n))}function a(t){return t===l?(e.exit(i),s(l)):null===t?n(t):en(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),an(e,a,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===l||null===t||en(t)?(e.exit("chunkString"),a(t)):(e.consume(t),92===t?c:u)}function c(t){return t===l||92===t?(e.consume(t),u):u(t)}}function Ln(e,t){let n;return function r(o){if(en(o))return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),n=!0,r;if(nn(o))return an(e,r,n?"linePrefix":"lineSuffix")(o);return t(o)}}const Mn={name:"definition",tokenize:function(e,t,n){const r=this;let o;return function(t){return e.enter("definition"),function(t){return Rn.call(r,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)}(t)};function i(t){return o=$t(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l):n(t)}function l(t){return tn(t)?Ln(e,s)(t):s(t)}function s(t){return Pn(e,a,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function a(t){return e.attempt(Fn,u,u)(t)}function u(t){return nn(t)?an(e,c,"whitespace")(t):c(t)}function c(i){return null===i||en(i)?(e.exit("definition"),r.parser.defined.push(o),t(i)):n(i)}}},Fn={partial:!0,tokenize:function(e,t,n){return function(t){return tn(t)?Ln(e,r)(t):n(t)};function r(t){return Dn(e,o,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function o(t){return nn(t)?an(e,i,"whitespace")(t):i(t)}function i(e){return null===e||en(e)?t(e):n(e)}}};const zn={name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return en(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}};const jn={name:"headingAtx",resolve:function(e,t){let n,r,o=e.length-2,i=3;"whitespace"===e[i][1].type&&(i+=2);o-2>i&&"whitespace"===e[o][1].type&&(o-=2);"atxHeadingSequence"===e[o][1].type&&(i===o-1||o-4>i&&"whitespace"===e[o-2][1].type)&&(o-=i+1===o?2:4);o>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[o][1].end},r={type:"chunkText",start:e[i][1].start,end:e[o][1].end,contentType:"text"},jt(e,i,o-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]]));return e},tokenize:function(e,t,n){let r=0;return function(t){return e.enter("atxHeading"),function(t){return e.enter("atxHeadingSequence"),o(t)}(t)};function o(t){return 35===t&&r++<6?(e.consume(t),o):null===t||tn(t)?(e.exit("atxHeadingSequence"),i(t)):n(t)}function i(n){return 35===n?(e.enter("atxHeadingSequence"),l(n)):null===n||en(n)?(e.exit("atxHeading"),t(n)):nn(n)?an(e,i,"whitespace")(n):(e.enter("atxHeadingText"),s(n))}function l(t){return 35===t?(e.consume(t),l):(e.exit("atxHeadingSequence"),i(t))}function s(t){return null===t||35===t||tn(t)?(e.exit("atxHeadingText"),i(t)):(e.consume(t),s)}}};const Bn=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Hn=["pre","script","style","textarea"],Un={concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2));return e},tokenize:function(e,t,n){const r=this;let o,i,l,s,a;return function(t){return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),u}(t)};function u(s){return 33===s?(e.consume(s),c):47===s?(e.consume(s),i=!0,p):63===s?(e.consume(s),o=3,r.interrupt?t:D):Kt(s)?(e.consume(s),l=String.fromCharCode(s),h):n(s)}function c(i){return 45===i?(e.consume(i),o=2,f):91===i?(e.consume(i),o=5,s=0,d):Kt(i)?(e.consume(i),o=4,r.interrupt?t:D):n(i)}function f(o){return 45===o?(e.consume(o),r.interrupt?t:D):n(o)}function d(o){const i="CDATA[";return o===i.charCodeAt(s++)?(e.consume(o),6===s?r.interrupt?t:I:d):n(o)}function p(t){return Kt(t)?(e.consume(t),l=String.fromCharCode(t),h):n(t)}function h(s){if(null===s||47===s||62===s||tn(s)){const a=47===s,u=l.toLowerCase();return a||i||!Hn.includes(u)?Bn.includes(l.toLowerCase())?(o=6,a?(e.consume(s),m):r.interrupt?t(s):I(s)):(o=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(s):i?g(s):y(s)):(o=1,r.interrupt?t(s):I(s))}return 45===s||Gt(s)?(e.consume(s),l+=String.fromCharCode(s),h):n(s)}function m(o){return 62===o?(e.consume(o),r.interrupt?t:I):n(o)}function g(t){return nn(t)?(e.consume(t),g):S(t)}function y(t){return 47===t?(e.consume(t),S):58===t||95===t||Kt(t)?(e.consume(t),v):nn(t)?(e.consume(t),y):S(t)}function v(t){return 45===t||46===t||58===t||95===t||Gt(t)?(e.consume(t),v):x(t)}function x(t){return 61===t?(e.consume(t),k):nn(t)?(e.consume(t),x):y(t)}function k(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,b):nn(t)?(e.consume(t),k):w(t)}function b(t){return t===a?(e.consume(t),a=null,E):null===t||en(t)?n(t):(e.consume(t),b)}function w(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||tn(t)?x(t):(e.consume(t),w)}function E(e){return 47===e||62===e||nn(e)?y(e):n(e)}function S(t){return 62===t?(e.consume(t),C):n(t)}function C(t){return null===t||en(t)?I(t):nn(t)?(e.consume(t),C):n(t)}function I(t){return 45===t&&2===o?(e.consume(t),_):60===t&&1===o?(e.consume(t),N):62===t&&4===o?(e.consume(t),L):63===t&&3===o?(e.consume(t),D):93===t&&5===o?(e.consume(t),R):!en(t)||6!==o&&7!==o?null===t||en(t)?(e.exit("htmlFlowData"),T(t)):(e.consume(t),I):(e.exit("htmlFlowData"),e.check(Vn,M,T)(t))}function T(t){return e.check(qn,O,M)(t)}function O(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),A}function A(t){return null===t||en(t)?T(t):(e.enter("htmlFlowData"),I(t))}function _(t){return 45===t?(e.consume(t),D):I(t)}function N(t){return 47===t?(e.consume(t),l="",P):I(t)}function P(t){if(62===t){const n=l.toLowerCase();return Hn.includes(n)?(e.consume(t),L):I(t)}return Kt(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),P):I(t)}function R(t){return 93===t?(e.consume(t),D):I(t)}function D(t){return 62===t?(e.consume(t),L):45===t&&2===o?(e.consume(t),D):I(t)}function L(t){return null===t||en(t)?(e.exit("htmlFlowData"),M(t)):(e.consume(t),L)}function M(n){return e.exit("htmlFlow"),t(n)}}},Vn={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(yn,t,n)}}},qn={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(en(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o;return n(t)};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}};const Yn={name:"htmlText",tokenize:function(e,t,n){const r=this;let o,i,l;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),s};function s(t){return 33===t?(e.consume(t),a):47===t?(e.consume(t),k):63===t?(e.consume(t),v):Kt(t)?(e.consume(t),E):n(t)}function a(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,p):Kt(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),d):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),f):en(t)?(l=c,P(t)):(e.consume(t),c)}function f(t){return 45===t?(e.consume(t),d):c(t)}function d(e){return 62===e?N(e):45===e?f(e):c(e)}function p(t){const r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),6===i?h:p):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):en(t)?(l=h,P(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?N(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?N(t):en(t)?(l=y,P(t)):(e.consume(t),y)}function v(t){return null===t?n(t):63===t?(e.consume(t),x):en(t)?(l=v,P(t)):(e.consume(t),v)}function x(e){return 62===e?N(e):v(e)}function k(t){return Kt(t)?(e.consume(t),b):n(t)}function b(t){return 45===t||Gt(t)?(e.consume(t),b):w(t)}function w(t){return en(t)?(l=w,P(t)):nn(t)?(e.consume(t),w):N(t)}function E(t){return 45===t||Gt(t)?(e.consume(t),E):47===t||62===t||tn(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),N):58===t||95===t||Kt(t)?(e.consume(t),C):en(t)?(l=S,P(t)):nn(t)?(e.consume(t),S):N(t)}function C(t){return 45===t||46===t||58===t||95===t||Gt(t)?(e.consume(t),C):I(t)}function I(t){return 61===t?(e.consume(t),T):en(t)?(l=I,P(t)):nn(t)?(e.consume(t),I):S(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,O):en(t)?(l=T,P(t)):nn(t)?(e.consume(t),T):(e.consume(t),A)}function O(t){return t===o?(e.consume(t),o=void 0,_):null===t?n(t):en(t)?(l=O,P(t)):(e.consume(t),O)}function A(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||tn(t)?S(t):(e.consume(t),A)}function _(e){return 47===e||62===e||tn(e)?S(e):n(e)}function N(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function P(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),R}function R(t){return nn(t)?an(e,D,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):D(t)}function D(t){return e.enter("htmlTextData"),l(t)}}};const $n={name:"labelEnd",resolveAll:function(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){const e="labelImage"===r.type?4:2;r.type="data",t+=e}}e.length!==n.length&&jt(e,0,e.length,n);return e},resolveTo:function(e,t){let n,r,o,i,l=e.length,s=0;for(;l--;)if(n=e[l][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[l][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(o){if("enter"===e[l][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=l,"labelLink"!==n.type)){s=2;break}}else"labelEnd"===n.type&&(o=l);const a={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[o][1].end}},c={type:"labelText",start:{...e[r+s+2][1].end},end:{...e[o-2][1].start}};return i=[["enter",a,t],["enter",u,t]],i=Bt(i,e.slice(r+1,r+s+3)),i=Bt(i,[["enter",c,t]]),i=Bt(i,pn(t.parser.constructs.insideSpan.null,e.slice(r+s+4,o-3),t)),i=Bt(i,[["exit",c,t],e[o-2],e[o-1],["exit",u,t]]),i=Bt(i,e.slice(o+1)),i=Bt(i,[["exit",a,t]]),jt(e,r,e.length,i),e},tokenize:function(e,t,n){const r=this;let o,i,l=r.events.length;for(;l--;)if(("labelImage"===r.events[l][1].type||"labelLink"===r.events[l][1].type)&&!r.events[l][1]._balanced){o=r.events[l][1];break}return function(t){if(!o)return n(t);if(o._inactive)return c(t);return i=r.parser.defined.includes($t(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),s};function s(t){return 40===t?e.attempt(Kn,u,i?u:c)(t):91===t?e.attempt(Gn,u,i?a:c)(t):i?u(t):c(t)}function a(t){return e.attempt(Wn,u,c)(t)}function u(e){return t(e)}function c(e){return o._balanced=!0,n(e)}}},Kn={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return tn(t)?Ln(e,o)(t):o(t)}function o(t){return 41===t?u(t):Pn(e,i,l,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return tn(t)?Ln(e,s)(t):u(t)}function l(e){return n(e)}function s(t){return 34===t||39===t||40===t?Dn(e,a,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function a(t){return tn(t)?Ln(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},Gn={tokenize:function(e,t,n){const r=this;return function(t){return Rn.call(r,e,o,i,"reference","referenceMarker","referenceString")(t)};function o(e){return r.parser.defined.includes($t(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},Wn={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}};const Jn={name:"labelStartImage",resolveAll:$n.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),o};function o(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};const Xn={name:"labelStartLink",resolveAll:$n.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),o};function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};const Qn={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),an(e,t,"linePrefix")}}};const Zn={name:"thematicBreak",tokenize:function(e,t,n){let r,o=0;return function(t){return e.enter("thematicBreak"),function(e){return r=e,i(e)}(t)};function i(i){return i===r?(e.enter("thematicBreakSequence"),l(i)):o>=3&&(null===i||en(i))?(e.exit("thematicBreak"),t(i)):n(i)}function l(t){return t===r?(e.consume(t),o++,l):(e.exit("thematicBreakSequence"),nn(t)?an(e,i,"whitespace")(t):i(t))}}};const er={continuation:{tokenize:function(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(yn,(function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,an(e,t,"listItemIndent",r.containerState.size+1)(n)}),(function(n){if(r.containerState.furtherBlankLines||!nn(n))return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(n);return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(nr,t,o)(n)}));function o(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,an(e,e.attempt(er,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){const r=this,o=r.events[r.events.length-1];let i=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,l=0;return function(t){const o=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===o?!r.containerState.marker||t===r.containerState.marker:Xt(t)){if(r.containerState.type||(r.containerState.type=o,e.enter(o,{_container:!0})),"listUnordered"===o)return e.enter("listItemPrefix"),42===t||45===t?e.check(Zn,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),s(t)}return n(t)};function s(t){return Xt(t)&&++l<10?(e.consume(t),s):(!r.interrupt||l<2)&&(r.containerState.marker?t===r.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),a(t)):n(t)}function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(yn,r.interrupt?n:u,e.attempt(tr,f,c))}function u(e){return r.containerState.initialBlankLine=!0,i++,f(e)}function c(t){return nn(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),f):n(t)}function f(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},tr={partial:!0,tokenize:function(e,t,n){const r=this;return an(e,(function(e){const o=r.events[r.events.length-1];return!nn(e)&&o&&"listItemPrefixWhitespace"===o[1].type?t(e):n(e)}),"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},nr={partial:!0,tokenize:function(e,t,n){const r=this;return an(e,(function(e){const o=r.events[r.events.length-1];return o&&"listItemIndent"===o[1].type&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?t(e):n(e)}),"listItemIndent",r.containerState.size+1)}};const rr={name:"setextUnderline",resolveTo:function(e,t){let n,r,o,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),o||"definition"!==e[i][1].type||(o=i);const l={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};e[r][1].type="setextHeadingText",o?(e.splice(r,0,["enter",l,t]),e.splice(o+1,0,["exit",e[n][1],t]),e[n][1].end={...e[o][1].end}):e[n][1]=l;return e.push(["exit",l,t]),e},tokenize:function(e,t,n){const r=this;let o;return function(t){let l,s=r.events.length;for(;s--;)if("lineEnding"!==r.events[s][1].type&&"linePrefix"!==r.events[s][1].type&&"content"!==r.events[s][1].type){l="paragraph"===r.events[s][1].type;break}if(!r.parser.lazy[r.now().line]&&(r.interrupt||l))return e.enter("setextHeadingLine"),o=t,function(t){return e.enter("setextHeadingLineSequence"),i(t)}(t);return n(t)};function i(t){return t===o?(e.consume(t),i):(e.exit("setextHeadingLineSequence"),nn(t)?an(e,l,"lineSuffix")(t):l(t))}function l(r){return null===r||en(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}};const or={tokenize:function(e){const t=this,n=e.attempt(yn,(function(r){if(null===r)return void e.consume(r);return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}),e.attempt(this.parser.constructs.flowInitial,r,an(e,e.attempt(this.parser.constructs.flow,r,e.attempt(_n,r)),"linePrefix")));return n;function r(r){if(null!==r)return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n;e.consume(r)}}};const ir={resolveAll:ur()},lr=ar("string"),sr=ar("text");function ar(e){return{resolveAll:ur("text"===e?cr:void 0),tokenize:function(t){const n=this,r=this.parser.constructs[e],o=t.attempt(r,i,l);return i;function i(e){return a(e)?o(e):l(e)}function l(e){if(null!==e)return t.enter("data"),t.consume(e),s;t.consume(e)}function s(e){return a(e)?(t.exit("data"),o(e)):(t.consume(e),s)}function a(e){if(null===e)return!0;const t=r[e];let o=-1;if(t)for(;++o<t.length;){const e=t[o];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function ur(e){return function(t,n){let r,o=-1;for(;++o<=t.length;)void 0===r?t[o]&&"data"===t[o][1].type&&(r=o,o++):t[o]&&"data"===t[o][1].type||(o!==r+2&&(t[r][1].end=t[o-1][1].end,t.splice(r+2,o-r-2),o=r+2),r=void 0);return e?e(t,n):t}}function cr(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){const r=e[n-1][1],o=t.sliceStream(r);let i,l=o.length,s=-1,a=0;for(;l--;){const e=o[l];if("string"==typeof e){for(s=e.length;32===e.charCodeAt(s-1);)a++,s--;if(s)break;s=-1}else if(-2===e)i=!0,a++;else if(-1!==e){l++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(a=0),a){const o={type:n===e.length||i||a<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:l?s:r.start._bufferIndex+s,_index:r.start._index+l,line:r.end.line,column:r.end.column-a,offset:r.end.offset-a},end:{...r.end}};r.end={...o.start},r.start.offset===r.end.offset?Object.assign(r,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}const fr={42:er,43:er,45:er,48:er,49:er,50:er,51:er,52:er,53:er,54:er,55:er,56:er,57:er,62:vn},dr={91:Mn},pr={[-2]:En,[-1]:En,32:En},hr={35:jn,42:Zn,45:[rr,Zn],60:Un,61:rr,95:Zn,96:wn,126:wn},mr={38:kn,92:xn},gr={[-5]:Qn,[-4]:Qn,[-3]:Qn,33:Jn,38:kn,42:hn,60:[gn,Yn],91:Xn,92:[zn,xn],93:$n,95:hn,96:Cn},yr={null:[hn,ir]},vr=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:{null:[42,95]},contentInitial:dr,disable:{null:[]},document:fr,flow:hr,flowInitial:pr,insideSpan:yr,string:mr,text:gr},Symbol.toStringTag,{value:"Module"}));function xr(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const o={},i=[];let l=[],s=[];const a={attempt:g((function(e,t){y(e,t.from)})),check:g(m),consume:function(e){en(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,v()):-1!==e&&(r.column++,r.offset++);r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++));u.previous=e},enter:function(e,t){const n=t||{};return n.type=e,n.start=d(),u.events.push(["enter",n,u]),s.push(n),n},exit:function(e){const t=s.pop();return t.end=d(),u.events.push(["exit",t,u]),t},interrupt:g(m,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){o[e.line]=e.column,v()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n=-1;const r=[];let o;for(;++n<e.length;){const i=e[n];let l;if("string"==typeof i)l=i;else switch(i){case-5:l="\r";break;case-4:l="\n";break;case-3:l="\r\n";break;case-2:l=t?" ":"\t";break;case-1:if(!t&&o)continue;l=" ";break;default:l=String.fromCharCode(i)}o=-2===i,r.push(l)}return r.join("")}(f(e),t)},sliceStream:f,write:function(e){if(l=Bt(l,e),p(),null!==l[l.length-1])return[];return y(t,0),u.events=pn(i,u.events,u),u.events}};let c=t.tokenize.call(u,a);return t.resolveAll&&i.push(t),u;function f(e){return function(e,t){const n=t.start._index,r=t.start._bufferIndex,o=t.end._index,i=t.end._bufferIndex;let l;if(n===o)l=[e[n].slice(r,i)];else{if(l=e.slice(n,o),r>-1){const e=l[0];"string"==typeof e?l[0]=e.slice(r):l.shift()}i>0&&l.push(e[o].slice(0,i))}return l}(l,e)}function d(){const{_bufferIndex:e,_index:t,line:n,column:o,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:o,offset:i}}function p(){let e;for(;r._index<l.length;){const t=l[r._index];if("string"==typeof t)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<t.length;)h(t.charCodeAt(r._bufferIndex));else h(t)}}function h(e){c=c(e)}function m(e,t){t.restore()}function g(e,t){return function(n,o,i){let l,c,f,p;return Array.isArray(n)?h(n):"tokenize"in n?h([n]):function(e){return t;function t(t){const n=null!==t&&e[t],r=null!==t&&e.null;return h([...Array.isArray(n)?n:n?[n]:[],...Array.isArray(r)?r:r?[r]:[]])(t)}}(n);function h(e){return l=e,c=0,0===e.length?i:m(e[c])}function m(e){return function(n){p=function(){const e=d(),t=u.previous,n=u.currentConstruct,o=u.events.length,i=Array.from(s);return{from:o,restore:l};function l(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=o,s=i,v()}}(),f=e,e.partial||(u.currentConstruct=e);if(e.name&&u.parser.constructs.disable.null.includes(e.name))return y();return e.tokenize.call(t?Object.assign(Object.create(u),t):u,a,g,y)(n)}}function g(t){return e(f,p),o}function y(e){return p.restore(),++c<l.length?m(l[c]):i}}}function y(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&jt(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function v(){r.line in o&&r.column<2&&(r.column=o[r.line],r.offset+=o[r.line]-1)}}const kr=/[\0\t\n\r]/g;const br=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function wr(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){const e=n.charCodeAt(1),t=120===e||88===e;return Yt(n.slice(t?2:1),t?16:10)}return zt(n)||e}const Er={}.hasOwnProperty;function Sr(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),function(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(te),autolinkProtocol:C,autolinkEmail:C,atxHeading:i(X),blockQuote:i($),characterEscape:C,characterReference:C,codeFenced:i(K),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:i(K,l),codeText:i(G,l),codeTextData:C,data:C,codeFlowValue:C,definition:i(W),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:i(J),hardBreakEscape:i(Q),hardBreakTrailing:i(Q),htmlFlow:i(Z,l),htmlFlowData:C,htmlText:i(Z,l),htmlTextData:C,image:i(ee),label:l,link:i(te),listItem:i(re),listItemValue:d,listOrdered:i(ne,f),listUnordered:i(ne),paragraph:i(oe),reference:j,referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:i(X),strong:i(ie),thematicBreak:i(se)},exit:{atxHeading:a(),atxHeadingSequence:b,autolink:a(),autolinkEmail:Y,autolinkProtocol:q,blockQuote:a(),characterEscapeValue:I,characterReferenceMarkerHexadecimal:H,characterReferenceMarkerNumeric:H,characterReferenceValue:U,characterReference:V,codeFenced:a(g),codeFencedFence:m,codeFencedFenceInfo:p,codeFencedFenceMeta:h,codeFlowValue:I,codeIndented:a(y),codeText:a(N),codeTextData:I,data:I,definition:a(),definitionDestinationString:k,definitionLabelString:v,definitionTitleString:x,emphasis:a(),hardBreakEscape:a(O),hardBreakTrailing:a(O),htmlFlow:a(A),htmlFlowData:I,htmlText:a(_),htmlTextData:I,image:a(R),label:L,labelText:D,lineEnding:T,link:a(P),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:B,resourceDestinationString:M,resourceTitleString:F,resource:z,setextHeading:a(S),setextHeadingLineSequence:E,setextHeadingText:w,strong:a(),thematicBreak:a()}};Ir(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(e){let r={type:"root",children:[]};const i={stack:[r],tokenStack:[],config:t,enter:s,exit:u,buffer:l,resume:c,data:n},a=[];let f=-1;for(;++f<e.length;)if("listOrdered"===e[f][1].type||"listUnordered"===e[f][1].type)if("enter"===e[f][0])a.push(f);else{f=o(e,a.pop(),f)}for(f=-1;++f<e.length;){const n=t[e[f][0]];Er.call(n,e[f][1].type)&&n[e[f][1].type].call(Object.assign({sliceSerialize:e[f][2].sliceSerialize},i),e[f][1])}if(i.tokenStack.length>0){const e=i.tokenStack[i.tokenStack.length-1];(e[1]||Or).call(i,void 0,e[0])}for(r.position={start:Cr(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:Cr(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},f=-1;++f<t.transforms.length;)r=t.transforms[f](r)||r;return r}function o(e,t,n){let r,o,i,l,s=t-1,a=-1,u=!1;for(;++s<=n;){const t=e[s];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?a++:a--,l=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||l||a||i||(i=s),l=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:l=void 0}if(!a&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===a&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let l=s;for(o=void 0;l--;){const t=e[l];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;o&&(e[o][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",o=l}else if("linePrefix"!==t[1].type&&"blockQuotePrefix"!==t[1].type&&"blockQuotePrefixWhitespace"!==t[1].type&&"blockQuoteMarker"!==t[1].type&&"listItemIndent"!==t[1].type)break}i&&(!o||i<o)&&(r._spread=!0),r.end=Object.assign({},o?e[o][1].start:t[1].end),e.splice(o||s,0,["exit",r,t[2]]),s++,n++}if("listItemPrefix"===t[1].type){const o={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=o,e.splice(s,0,["enter",o,t[2]]),s++,n++,i=void 0,l=!0}}}return e[t][1]._spread=u,n}function i(e,t){return n;function n(n){s.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function s(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:Cr(t.start),end:void 0}}function a(e){return t;function t(t){e&&e.call(this,t),u.call(this,t)}}function u(e,t){const n=this.stack.pop(),r=this.tokenStack.pop();if(!r)throw new Error("Cannot close `"+e.type+"` ("+ft({start:e.start,end:e.end})+"): it’s not open");if(r[0].type!==e.type)if(t)t.call(this,e,r[0]);else{(r[1]||Or).call(this,e,r[0])}n.position.end=Cr(e.end)}function c(){return Dt(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function d(e){if(this.data.expectingFirstListItemValue){this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}}function p(){const e=this.resume();this.stack[this.stack.length-1].lang=e}function h(){const e=this.resume();this.stack[this.stack.length-1].meta=e}function m(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function g(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function y(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}function v(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=$t(this.sliceSerialize(e)).toLowerCase()}function x(){const e=this.resume();this.stack[this.stack.length-1].title=e}function k(){const e=this.resume();this.stack[this.stack.length-1].url=e}function b(e){const t=this.stack[this.stack.length-1];if(!t.depth){const n=this.sliceSerialize(e).length;t.depth=n}}function w(){this.data.setextHeadingSlurpLineEnding=!0}function E(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2}function S(){this.data.setextHeadingSlurpLineEnding=void 0}function C(e){const t=this.stack[this.stack.length-1].children;let n=t[t.length-1];n&&"text"===n.type||(n=le(),n.position={start:Cr(e.start),end:void 0},t.push(n)),this.stack.push(n)}function I(e){const t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=Cr(e.end)}function T(e){const n=this.stack[this.stack.length-1];if(this.data.atHardBreak){return n.children[n.children.length-1].position.end=Cr(e.end),void(this.data.atHardBreak=void 0)}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(C.call(this,e),I.call(this,e))}function O(){this.data.atHardBreak=!0}function A(){const e=this.resume();this.stack[this.stack.length-1].value=e}function _(){const e=this.resume();this.stack[this.stack.length-1].value=e}function N(){const e=this.resume();this.stack[this.stack.length-1].value=e}function P(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function R(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function D(e){const t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=function(e){return e.replace(br,wr)}(t),n.identifier=$t(t).toLowerCase()}function L(){const e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){const t=e.children;n.children=t}else n.alt=t}function M(){const e=this.resume();this.stack[this.stack.length-1].url=e}function F(){const e=this.resume();this.stack[this.stack.length-1].title=e}function z(){this.data.inReference=void 0}function j(){this.data.referenceType="collapsed"}function B(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=$t(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"}function H(e){this.data.characterReferenceType=e.type}function U(e){const t=this.sliceSerialize(e),n=this.data.characterReferenceType;let r;if(n)r=Yt(t,"characterReferenceMarkerNumeric"===n?10:16),this.data.characterReferenceType=void 0;else{r=zt(t)}this.stack[this.stack.length-1].value+=r}function V(e){this.stack.pop().position.end=Cr(e.end)}function q(e){I.call(this,e);this.stack[this.stack.length-1].url=this.sliceSerialize(e)}function Y(e){I.call(this,e);this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)}function $(){return{type:"blockquote",children:[]}}function K(){return{type:"code",lang:null,meta:null,value:""}}function G(){return{type:"inlineCode",value:""}}function W(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function J(){return{type:"emphasis",children:[]}}function X(){return{type:"heading",depth:0,children:[]}}function Q(){return{type:"break"}}function Z(){return{type:"html",value:""}}function ee(){return{type:"image",title:null,url:"",alt:null}}function te(){return{type:"link",title:null,url:"",children:[]}}function ne(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}function re(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}function oe(){return{type:"paragraph",children:[]}}function ie(){return{type:"strong",children:[]}}function le(){return{type:"text",value:""}}function se(){return{type:"thematicBreak"}}}(n)(function(e){for(;!On(e););return e}(function(e){const t={constructs:Ut([vr,...(e||{}).extensions||[]]),content:n(un),defined:[],document:n(cn),flow:n(or),lazy:{},string:n(lr),text:n(sr)};return t;function n(e){return function(n){return xr(t,e,n)}}}(n).document().write(function(){let e,t=1,n="",r=!0;return function(o,i,l){const s=[];let a,u,c,f,d;for(o=n+("string"==typeof o?o.toString():new TextDecoder(i||void 0).decode(o)),c=0,n="",r&&(65279===o.charCodeAt(0)&&c++,r=void 0);c<o.length;){if(kr.lastIndex=c,a=kr.exec(o),f=a&&void 0!==a.index?a.index:o.length,d=o.charCodeAt(f),!a){n=o.slice(c);break}if(10===d&&c===f&&e)s.push(-3),e=void 0;else switch(e&&(s.push(-5),e=void 0),c<f&&(s.push(o.slice(c,f)),t+=f-c),d){case 0:s.push(65533),t++;break;case 9:for(u=4*Math.ceil(t/4),s.push(-2);t++<u;)s.push(-1);break;case 10:s.push(-4),t=1;break;default:e=!0,t=1}c=f+1}return l&&(e&&s.push(-5),n&&s.push(n),s.push(null)),s}}()(e,t,!0))))}function Cr(e){return{line:e.line,column:e.column,offset:e.offset}}function Ir(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?Ir(e,r):Tr(e,r)}}function Tr(e,t){let n;for(n in t)if(Er.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function Or(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+ft({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+ft({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+ft({start:t.start,end:t.end})+") is still open")}function Ar(e){const t=this;t.parser=function(n){return Sr(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function _r(e,t){const n=t.referenceType;let r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];const o=e.all(t),i=o[0];i&&"text"===i.type?i.value="["+i.value:o.unshift({type:"text",value:"["});const l=o[o.length-1];return l&&"text"===l.type?l.value+=r:o.push({type:"text",value:r}),o}function Nr(e){const t=e.spread;return null==t?e.children.length>1:t}function Pr(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),o=0;const i=[];for(;r;)i.push(Rr(t.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=n.exec(t);return i.push(Rr(t.slice(o),o>0,!1)),i.join("")}function Rr(e,t,n){let r=0,o=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(o-1);for(;9===t||32===t;)o--,t=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}const Dr={blockquote:function(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){const n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(o.data={meta:t.meta}),e.patch(t,o),o=e.applyData(t,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(t,o),o},delete:function(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){const n="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),o=sn(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let l,s=e.footnoteCounts.get(r);void 0===s?(s=0,e.footnoteOrder.push(r),l=e.footnoteOrder.length):l=i+1,s+=1,e.footnoteCounts.set(r,s);const a={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+o,id:n+"fnref-"+o+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(t,a);const u={type:"element",tagName:"sup",properties:{},children:[a]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return _r(e,t);const o={src:sn(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){const n={src:sn(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return _r(e,t);const o={href:sn(r.url||"")};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){const n={href:sn(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){const r=e.all(t),o=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Nr(n[r])}return t}(n):Nr(t),i={},l=[];if("boolean"==typeof t.checked){const e=r[0];let n;e&&"element"===e.type&&"p"===e.tagName?n=e:(n={type:"element",tagName:"p",properties:{},children:[]},r.unshift(n)),n.children.length>0&&n.children.unshift({type:"text",value:" "}),n.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let s=-1;for(;++s<r.length;){const e=r[s];(o||0!==s||"element"!==e.type||"p"!==e.tagName)&&l.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||o?l.push(e):l.push(...e.children)}const a=r[r.length-1];a&&(o||"element"!==a.type||"p"!==a.tagName)&&l.push({type:"text",value:"\n"});const u={type:"element",tagName:"li",properties:i,children:l};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){const n={},r=e.all(t);let o=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++o<r.length;){const e=r[o];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){const n=e.all(t),r=n.shift(),o=[];if(r){const n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),o.push(n)}if(n.length>0){const r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=ut(t.children[1]),l=at(t.children[t.children.length-1]);i&&l&&(r.position={start:i,end:l}),o.push(r)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){const r=n?n.children:void 0,o=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,l=i?i.length:t.children.length;let s=-1;const a=[];for(;++s<l;){const n=t.children[s],r={},l=i?i[s]:void 0;l&&(r.align=l);let u={type:"element",tagName:o,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),a.push(u)}const u={type:"element",tagName:"tr",properties:{},children:e.wrap(a,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){const n={type:"text",value:Pr(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:Lr,yaml:Lr,definition:Lr,footnoteDefinition:Lr};function Lr(){}const Mr="object"==typeof self?self:globalThis,Fr=e=>((e,t)=>{const n=(t,n)=>(e.set(n,t),t),r=o=>{if(e.has(o))return e.get(o);const[i,l]=t[o];switch(i){case 0:case-1:return n(l,o);case 1:{const e=n([],o);for(const t of l)e.push(r(t));return e}case 2:{const e=n({},o);for(const[t,n]of l)e[r(t)]=r(n);return e}case 3:return n(new Date(l),o);case 4:{const{source:e,flags:t}=l;return n(new RegExp(e,t),o)}case 5:{const e=n(new Map,o);for(const[t,n]of l)e.set(r(t),r(n));return e}case 6:{const e=n(new Set,o);for(const t of l)e.add(r(t));return e}case 7:{const{name:e,message:t}=l;return n(new Mr[e](t),o)}case 8:return n(BigInt(l),o);case"BigInt":return n(Object(BigInt(l)),o);case"ArrayBuffer":return n(new Uint8Array(l).buffer,l);case"DataView":{const{buffer:e}=new Uint8Array(l);return n(new DataView(e),l)}}return n(new Mr[i](l),o)};return r})(new Map,e)(0),zr="",{toString:jr}={},{keys:Br}=Object,Hr=e=>{const t=typeof e;if("object"!==t||!e)return[0,t];const n=jr.call(e).slice(8,-1);switch(n){case"Array":return[1,zr];case"Object":return[2,zr];case"Date":return[3,zr];case"RegExp":return[4,zr];case"Map":return[5,zr];case"Set":return[6,zr];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},Ur=([e,t])=>0===e&&("function"===t||"symbol"===t),Vr=(e,{json:t,lossy:n}={})=>{const r=[];return((e,t,n,r)=>{const o=(e,t)=>{const o=r.push(e)-1;return n.set(t,o),o},i=r=>{if(n.has(r))return n.get(r);let[l,s]=Hr(r);switch(l){case 0:{let t=r;switch(s){case"bigint":l=8,t=r.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+s);t=null;break;case"undefined":return o([-1],r)}return o([l,t],r)}case 1:{if(s){let e=r;return"DataView"===s?e=new Uint8Array(r.buffer):"ArrayBuffer"===s&&(e=new Uint8Array(r)),o([s,[...e]],r)}const e=[],t=o([l,e],r);for(const n of r)e.push(i(n));return t}case 2:{if(s)switch(s){case"BigInt":return o([s,r.toString()],r);case"Boolean":case"Number":case"String":return o([s,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());const n=[],a=o([l,n],r);for(const t of Br(r))!e&&Ur(Hr(r[t]))||n.push([i(t),i(r[t])]);return a}case 3:return o([l,r.toISOString()],r);case 4:{const{source:e,flags:t}=r;return o([l,{source:e,flags:t}],r)}case 5:{const t=[],n=o([l,t],r);for(const[o,l]of r)(e||!Ur(Hr(o))&&!Ur(Hr(l)))&&t.push([i(o),i(l)]);return n}case 6:{const t=[],n=o([l,t],r);for(const o of r)!e&&Ur(Hr(o))||t.push(i(o));return n}}const{message:a}=r;return o([l,{name:s,message:a}],r)};return i})(!(t||n),!!t,new Map,r)(e),r},qr="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?Fr(Vr(e,t)):structuredClone(e):(e,t)=>Fr(Vr(e,t));function Yr(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function $r(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}const Kr=function(e){if(null==e)return Wr;if("function"==typeof e)return Gr(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Kr(e[n]);return Gr(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return Gr(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return Gr(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function Gr(e){return function(t,n,r){return Boolean(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function Wr(){return!0}const Jr=[],Xr=!0,Qr=!1;function Zr(e,t,n,r){let o;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):o=t;const i=Kr(o),l=r?-1:1;!function e(o,s,a){const u=o&&"object"==typeof o?o:{};if("string"==typeof u.type){const e="string"==typeof u.tagName?u.tagName:"string"==typeof u.name?u.name:void 0;Object.defineProperty(c,"name",{value:"node ("+o.type+(e?"<"+e+">":"")+")"})}return c;function c(){let u,c,f,d=Jr;if((!t||i(o,s,a[a.length-1]||void 0))&&(d=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[Xr,e];return null==e?Jr:[e]}(n(o,a)),d[0]===Qr))return d;if("children"in o&&o.children){const t=o;if(t.children&&"skip"!==d[0])for(c=(r?t.children.length:-1)+l,f=a.concat(t);c>-1&&c<t.children.length;){const n=t.children[c];if(u=e(n,c,f)(),u[0]===Qr)return u;c="number"==typeof u[1]?u[1]:c+l}}return d}}(e,void 0,[])()}function eo(e,t,n,r){let o,i,l;"function"==typeof t?(i=void 0,l=t,o=n):(i=t,l=n,o=r),Zr(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)}),o)}const to={}.hasOwnProperty,no={};function ro(e,t){e.position&&(t.position=function(e){const t=ut(e),n=at(e);if(t&&n)return{start:t,end:n}}(e))}function oo(e,t){let n=t;if(e&&e.data){const t=e.data.hName,r=e.data.hChildren,o=e.data.hProperties;if("string"==typeof t)if("element"===n.type)n.tagName=t;else{n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}}"element"===n.type&&o&&Object.assign(n.properties,qr(o)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function io(e,t){const n=t.data||{},r=!("value"in t)||to.call(n,"hProperties")||to.call(n,"hChildren")?{type:"element",tagName:"div",properties:{},children:e.all(t)}:{type:"text",value:t.value};return e.patch(t,r),e.applyData(t,r)}function lo(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function so(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function ao(e,t){const n=function(e,t){const n=t||no,r=new Map,o=new Map,i=new Map,l={...Dr,...n.handlers},s={all:function(e){const t=[];if("children"in e){const n=e.children;let r=-1;for(;++r<n.length;){const o=s.one(n[r],e);if(o){if(r&&"break"===n[r-1].type&&(Array.isArray(o)||"text"!==o.type||(o.value=so(o.value)),!Array.isArray(o)&&"element"===o.type)){const e=o.children[0];e&&"text"===e.type&&(e.value=so(e.value))}Array.isArray(o)?t.push(...o):t.push(o)}}}return t},applyData:oo,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:l,one:function(e,t){const n=e.type,r=s.handlers[n];if(to.call(s.handlers,n)&&r)return r(s,e,t);if(s.options.passThrough&&s.options.passThrough.includes(n)){if("children"in e){const{children:t,...n}=e,r=qr(n);return r.children=s.all(e),r}return qr(e)}return(s.options.unknownHandler||io)(s,e,t)},options:n,patch:ro,wrap:lo};return eo(e,(function(e){if("definition"===e.type||"footnoteDefinition"===e.type){const t="definition"===e.type?r:o,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}})),s}(e,t),r=n.one(e,void 0),o=function(e){const t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||Yr,r=e.options.footnoteBackLabel||$r,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",l=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[];let a=-1;for(;++a<e.footnoteOrder.length;){const o=e.footnoteById.get(e.footnoteOrder[a]);if(!o)continue;const i=e.all(o),l=String(o.identifier).toUpperCase(),u=sn(l.toLowerCase());let c=0;const f=[],d=e.footnoteCounts.get(l);for(;void 0!==d&&++c<=d;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(a,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(a,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}const p=i[i.length-1];if(p&&"element"===p.type&&"p"===p.tagName){const e=p.children[p.children.length-1];e&&"text"===e.type?e.value+=" ":p.children.push({type:"text",value:" "}),p.children.push(...f)}else i.push(...f);const h={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(i,!0)};e.patch(o,h),s.push(h)}if(0!==s.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...qr(l),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&i.children.push({type:"text",value:"\n"},o),i}function uo(e,t){return e&&"run"in e?async function(n,r){const o=ao(n,{file:r,...t});await e.run(o,r)}:function(n,r){return ao(n,{file:r,...e||t})}}function co(e){if(e)throw e}var fo,po;const ho=e(function(){if(po)return fo;po=1;var e=Object.prototype.hasOwnProperty,t=Object.prototype.toString,n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===t.call(e)},i=function(n){if(!n||"[object Object]"!==t.call(n))return!1;var r,o=e.call(n,"constructor"),i=n.constructor&&n.constructor.prototype&&e.call(n.constructor.prototype,"isPrototypeOf");if(n.constructor&&!o&&!i)return!1;for(r in n);return void 0===r||e.call(n,r)},l=function(e,t){n&&"__proto__"===t.name?n(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(t,n){if("__proto__"===n){if(!e.call(t,n))return;if(r)return r(t,n).value}return t[n]};return fo=function e(){var t,n,r,a,u,c,f=arguments[0],d=1,p=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},d=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});d<p;++d)if(null!=(t=arguments[d]))for(n in t)r=s(f,n),f!==(a=s(t,n))&&(h&&a&&(i(a)||(u=o(a)))?(u?(u=!1,c=r&&o(r)?r:[]):c=r&&i(r)?r:{},l(f,{name:n,newValue:e(h,c,a)})):void 0!==a&&l(f,{name:n,newValue:a}));return f}}());function mo(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)}function go(){const e=[],t={run:function(...t){let n=-1;const r=t.pop();if("function"!=typeof r)throw new TypeError("Expected function as last argument, not "+r);!function o(i,...l){const s=e[++n];let a=-1;if(i)r(i);else{for(;++a<t.length;)null!==l[a]&&void 0!==l[a]||(l[a]=t[a]);t=l,s?function(e,t){let n;return r;function r(...t){const r=e.length>t.length;let s;r&&t.push(o);try{s=e.apply(this,t)}catch(i){if(r&&n)throw i;return o(i)}r||(s&&s.then&&"function"==typeof s.then?s.then(l,o):s instanceof Error?o(s):l(s))}function o(e,...r){n||(n=!0,t(e,...r))}function l(e){o(null,e)}}(s,o)(...l):r(null,...l)}}(null,...t)},use:function(n){if("function"!=typeof n)throw new TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}const yo={basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');vo(e);let n,r=0,o=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else o<0&&(n=!0,o=i+1);return o<0?"":e.slice(r,o)}if(t===e)return"";let l=-1,s=t.length-1;for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else l<0&&(n=!0,l=i+1),s>-1&&(e.codePointAt(i)===t.codePointAt(s--)?s<0&&(o=i):(s=-1,o=l));r===o?o=l:o<0&&(o=e.length);return e.slice(r,o)},dirname:function(e){if(vo(e),0===e.length)return".";let t,n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){vo(e);let t,n=e.length,r=-1,o=0,i=-1,l=0;for(;n--;){const s=e.codePointAt(n);if(47!==s)r<0&&(t=!0,r=n+1),46===s?i<0?i=n:1!==l&&(l=1):i>-1&&(l=-1);else if(t){o=n+1;break}}if(i<0||r<0||0===l||1===l&&i===r-1&&i===o+1)return"";return e.slice(i,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)vo(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){vo(e);const t=47===e.codePointAt(0);let n=function(e,t){let n,r,o="",i=0,l=-1,s=0,a=-1;for(;++a<=e.length;){if(a<e.length)n=e.codePointAt(a);else{if(47===n)break;n=47}if(47===n){if(l===a-1||1===s);else if(l!==a-1&&2===s){if(o.length<2||2!==i||46!==o.codePointAt(o.length-1)||46!==o.codePointAt(o.length-2))if(o.length>2){if(r=o.lastIndexOf("/"),r!==o.length-1){r<0?(o="",i=0):(o=o.slice(0,r),i=o.length-1-o.lastIndexOf("/")),l=a,s=0;continue}}else if(o.length>0){o="",i=0,l=a,s=0;continue}t&&(o=o.length>0?o+"/..":"..",i=2)}else o.length>0?o+="/"+e.slice(l+1,a):o=e.slice(l+1,a),i=a-l-1;l=a,s=0}else 46===n&&s>-1?s++:s=-1}return o}(e,!t);0!==n.length||t||(n=".");n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/");return t?"/"+n:n}(t)},sep:"/"};function vo(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const xo={cwd:function(){return"/"}};function ko(e){return Boolean(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}function bo(e){if("string"==typeof e)e=new URL(e);else if(!ko(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){const e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){const e=new TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}const t=e.pathname;let n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){const e=t.codePointAt(n+2);if(70===e||102===e){const e=new TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}const wo=["history","path","basename","stem","extname","dirname"];class Eo{constructor(e){let t;t=e?ko(e)?{path:e}:"string"==typeof e||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":xo.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n,r=-1;for(;++r<wo.length;){const e=wo[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)wo.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?yo.basename(this.path):void 0}set basename(e){Co(e,"basename"),So(e,"basename"),this.path=yo.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?yo.dirname(this.path):void 0}set dirname(e){Io(this.basename,"dirname"),this.path=yo.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?yo.extname(this.path):void 0}set extname(e){if(So(e,"extname"),Io(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=yo.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){ko(e)&&(e=bo(e)),Co(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?yo.basename(this.path,this.extname):void 0}set stem(e){Co(e,"stem"),So(e,"stem"),this.path=yo.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){const r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){const r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){const r=new mt(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;return new TextDecoder(e||void 0).decode(this.value)}}function So(e,t){if(e&&e.includes(yo.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+yo.sep+"`")}function Co(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Io(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}const To=function(e){const t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},Oo={}.hasOwnProperty;class Ao extends To{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=go()}copy(){const e=new Ao;let t=-1;for(;++t<this.attachers.length;){const n=this.attachers[t];e.use(...n)}return e.data(ho(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2===arguments.length?(Ro("data",this.frozen),this.namespace[e]=t,this):Oo.call(this.namespace,e)&&this.namespace[e]||void 0:e?(Ro("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;const e=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...n]=this.attachers[this.freezeIndex];if(!1===n[0])continue;!0===n[0]&&(n[0]=void 0);const r=t.call(e,...n);"function"==typeof r&&this.transformers.use(r)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();const t=Mo(e),n=this.parser||this.Parser;return No("parse",n),n(String(t),t)}process(e,t){const n=this;return this.freeze(),No("process",this.parser||this.Parser),Po("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,o){const i=Mo(e),l=n.parse(i);function s(e,n){e||!n?o(e):r?r(n):t(void 0,n)}n.run(l,i,(function(e,t,r){if(e||!t||!r)return s(e);const o=t,i=n.stringify(o,r);var l;"string"==typeof(l=i)||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(l)?r.value=i:r.result=i,s(e,r)}))}}processSync(e){let t,n=!1;return this.freeze(),No("processSync",this.parser||this.Parser),Po("processSync",this.compiler||this.Compiler),this.process(e,(function(e,r){n=!0,co(e),t=r})),Lo("processSync","process",n),t}run(e,t,n){Do(e),this.freeze();const r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?o(void 0,n):new Promise(o);function o(o,i){const l=Mo(t);r.run(e,l,(function(t,r,l){const s=r||e;t?i(t):o?o(s):n(void 0,s,l)}))}}runSync(e,t){let n,r=!1;return this.run(e,t,(function(e,t){co(e),n=t,r=!0})),Lo("runSync","run",r),n}stringify(e,t){this.freeze();const n=Mo(t),r=this.compiler||this.Compiler;return Po("stringify",r),Do(e),r(e,n)}use(e,...t){const n=this.attachers,r=this.namespace;if(Ro("use",this.frozen),null==e);else if("function"==typeof e)s(e,t);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");Array.isArray(e)?l(e):i(e)}return this;function o(e){if("function"==typeof e)s(e,[]);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");if(Array.isArray(e)){const[t,...n]=e;s(t,n)}else i(e)}}function i(e){if(!("plugins"in e)&&!("settings"in e))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=ho(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else{if(!Array.isArray(e))throw new TypeError("Expected a list of plugins, not `"+e+"`");for(;++t<e.length;){o(e[t])}}}function s(e,t){let r=-1,o=-1;for(;++r<n.length;)if(n[r][0]===e){o=r;break}if(-1===o)n.push([e,...t]);else if(t.length>0){let[r,...i]=t;const l=n[o][1];mo(l)&&mo(r)&&(r=ho(!0,l,r)),n[o]=[e,r,...i]}}}}const _o=(new Ao).freeze();function No(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `parser`")}function Po(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `compiler`")}function Ro(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Do(e){if(!mo(e)||"string"!=typeof e.type)throw new TypeError("Expected node, got `"+e+"`")}function Lo(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Mo(e){return function(e){return Boolean(e&&"object"==typeof e&&"message"in e&&"messages"in e)}(e)?e:new Eo(e)}const Fo=[],zo={allowDangerousHtml:!0},jo=/^(https?|ircs?|mailto|xmpp)$/i,Bo=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Ho(e){const t=function(e){const t=e.rehypePlugins||Fo,n=e.remarkPlugins||Fo,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...zo}:zo,o=_o().use(Ar).use(n).use(uo,r).use(t);return o}(e),n=function(e){const t=e.children||"",n=new Eo;"string"==typeof t&&(n.value=t);return n}(e);return function(e,t){const n=t.allowedElements,r=t.allowElement,o=t.components,i=t.disallowedElements,l=t.skipHtml,s=t.unwrapDisallowed,a=t.urlTransform||Uo;for(const c of Bo)Object.hasOwn(t,c.from)&&(c.from,c.to&&c.to,c.id);return eo(e,u),wt(e,{Fragment:f.Fragment,components:o,ignoreInvalidStyle:!0,jsx:f.jsx,jsxs:f.jsxs,passKeys:!0,passNode:!0});function u(e,t,o){if("raw"===e.type&&o&&"number"==typeof t)return l?o.children.splice(t,1):o.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in Pt)if(Object.hasOwn(Pt,t)&&Object.hasOwn(e.properties,t)){const n=e.properties[t],r=Pt[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=a(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!i&&i.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,o)),l&&o&&"number"==typeof t)return s&&e.children?o.children.splice(t,1,...e.children):o.children.splice(t,1),t}}}(t.runSync(t.parse(n),n),e)}function Uo(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return-1===t||-1!==o&&t>o||-1!==n&&t>n||-1!==r&&t>r||jo.test(e.slice(0,t))?e:""}export{Qr as E,ue as G,Ho as M,rn as a,dn as b,Kr as c,eo as d,Gt as e,Kt as f,Jt as g,yn as h,an as i,f as j,en as k,nn as l,tn as m,$t as n,ce as o,Ut as p,pn as r,jt as s,Dt as t,on as u,Zr as v};
