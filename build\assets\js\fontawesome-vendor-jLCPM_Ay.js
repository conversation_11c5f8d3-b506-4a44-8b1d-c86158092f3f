import{g as t}from"./react-vendor-CFHJfABC.js";import{R as n}from"./router-vendor-BEZ3q80F.js";
/*!
 * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2024 Fonticons, Inc.
 */const e={prefix:"fas",iconName:"user",icon:[448,512,[128100,62144],"f007","M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512l388.6 0c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304l-91.4 0z"]},a={prefix:"fas",iconName:"key",icon:[512,512,[128273],"f084","M336 352c97.2 0 176-78.8 176-176S433.2 0 336 0S160 78.8 160 176c0 18.7 2.9 36.8 8.3 53.7L7 391c-4.5 4.5-7 10.6-7 17l0 80c0 13.3 10.7 24 24 24l80 0c13.3 0 24-10.7 24-24l0-40 40 0c13.3 0 24-10.7 24-24l0-40 40 0c6.4 0 12.5-2.5 17-7l33.3-33.3c16.9 5.4 35 8.3 53.7 8.3zM376 96a40 40 0 1 1 0 80 40 40 0 1 1 0-80z"]},r={prefix:"fas",iconName:"crown",icon:[576,512,[128081],"f521","M309 106c11.4-7 19-19.7 19-34c0-22.1-17.9-40-40-40s-40 17.9-40 40c0 14.4 7.6 27 19 34L209.7 220.6c-9.1 18.2-32.7 23.4-48.6 10.7L72 160c5-6.7 8-15 8-24c0-22.1-17.9-40-40-40S0 113.9 0 136s17.9 40 40 40c.2 0 .5 0 .7 0L86.4 427.4c5.5 30.4 32 52.6 63 52.6l277.2 0c30.9 0 57.4-22.1 63-52.6L535.3 176c.2 0 .5 0 .7 0c22.1 0 40-17.9 40-40s-17.9-40-40-40s-40 17.9-40 40c0 9 3 17.3 8 24l-89.1 71.3c-15.9 12.7-39.5 7.5-48.6-10.7L309 106z"]},o={prefix:"fas",iconName:"signature",icon:[640,512,[],"f5b7","M192 128c0-17.7 14.3-32 32-32s32 14.3 32 32l0 7.8c0 27.7-2.4 55.3-7.1 82.5l-84.4 25.3c-40.6 12.2-68.4 49.6-68.4 92l0 71.9c0 40 32.5 72.5 72.5 72.5c26 0 50-13.9 62.9-36.5l13.9-24.3c26.8-47 46.5-97.7 58.4-150.5l94.4-28.3-12.5 37.5c-3.3 9.8-1.6 20.5 4.4 28.8s15.7 13.3 26 13.3l128 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-83.6 0 18-53.9c3.8-11.3 .9-23.8-7.4-32.4s-20.7-11.8-32.2-8.4L316.4 198.1c2.4-20.7 3.6-41.4 3.6-62.3l0-7.8c0-53-43-96-96-96s-96 43-96 96l0 32c0 17.7 14.3 32 32 32s32-14.3 32-32l0-32zm-9.2 177l49-14.7c-10.4 33.8-24.5 66.4-42.1 97.2l-13.9 24.3c-1.5 2.6-4.3 4.3-7.4 4.3c-4.7 0-8.5-3.8-8.5-8.5l0-71.9c0-14.1 9.3-26.6 22.8-30.7zM24 368c-13.3 0-24 10.7-24 24s10.7 24 24 24l40.3 0c-.2-2.8-.3-5.6-.3-8.5L64 368l-40 0zm592 48c13.3 0 24-10.7 24-24s-10.7-24-24-24l-310.1 0c-6.7 16.3-14.2 32.3-22.3 48L616 416z"]},i={prefix:"fas",iconName:"shield-halved",icon:[512,512,["shield-alt"],"f3ed","M256 0c4.6 0 9.2 1 13.4 2.9L457.7 82.8c22 9.3 38.4 31 38.3 57.2c-.5 99.2-41.3 280.7-213.6 363.2c-16.7 8-36.1 8-52.8 0C57.3 420.7 16.5 239.2 16 140c-.1-26.2 16.3-47.9 38.3-57.2L242.7 2.9C246.8 1 251.4 0 256 0zm0 66.8l0 378.1C394 378 431.1 230.1 432 141.4L256 66.8s0 0 0 0z"]},s={prefix:"fas",iconName:"file-contract",icon:[384,512,[],"f56c","M64 0C28.7 0 0 28.7 0 64L0 448c0 35.3 28.7 64 64 64l256 0c35.3 0 64-28.7 64-64l0-288-128 0c-17.7 0-32-14.3-32-32L224 0 64 0zM256 0l0 128 128 0L256 0zM80 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16L80 96c-8.8 0-16-7.2-16-16s7.2-16 16-16zm0 64l64 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-8.8 0-16-7.2-16-16s7.2-16 16-16zm54.2 253.8c-6.1 20.3-24.8 34.2-46 34.2L80 416c-8.8 0-16-7.2-16-16s7.2-16 16-16l8.2 0c7.1 0 13.3-4.6 15.3-11.4l14.9-49.5c3.4-11.3 13.8-19.1 25.6-19.1s22.2 7.7 25.6 19.1l11.6 38.6c7.4-6.2 16.8-9.7 26.8-9.7c15.9 0 30.4 9 37.5 23.2l4.4 8.8 54.1 0c8.8 0 16 7.2 16 16s-7.2 16-16 16l-64 0c-6.1 0-11.6-3.4-14.3-8.8l-8.8-17.7c-1.7-3.4-5.1-5.5-8.8-5.5s-7.2 2.1-8.8 5.5l-8.8 17.7c-2.9 5.9-9.2 9.4-15.7 8.8s-12.1-5.1-13.9-11.3L144 349l-9.8 32.8z"]},c={prefix:"fas",iconName:"xmark",icon:[384,512,[128473,10005,10006,10060,215,"close","multiply","remove","times"],"f00d","M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"]},l={prefix:"fas",iconName:"check",icon:[448,512,[10003,10004],"f00c","M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z"]};
/*!
 * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2024 Fonticons, Inc.
 */
function f(t,n,e){return(n=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var a=e.call(t,n);if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function u(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);n&&(a=a.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,a)}return e}function d(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?u(Object(e),!0).forEach((function(n){f(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):u(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}const m=()=>{};let p={},h={},g=null,b={mark:m,measure:m};try{"undefined"!=typeof window&&(p=window),"undefined"!=typeof document&&(h=document),"undefined"!=typeof MutationObserver&&(g=MutationObserver),"undefined"!=typeof performance&&(b=performance)}catch(qe){}const{userAgent:y=""}=p.navigator||{},v=p,x=h,k=g,w=b;v.document;const O=!!x.documentElement&&!!x.head&&"function"==typeof x.addEventListener&&"function"==typeof x.createElement,A=~y.indexOf("MSIE")||~y.indexOf("Trident/");var P={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"}},N=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone"],S="classic",C="duotone",j=[S,C,"sharp","sharp-duotone"],z=new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}]]),E=["fak","fa-kit","fakd","fa-kit-duotone"],I={fak:"kit","fa-kit":"kit"},M={fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"},L=["fak","fakd"],F={kit:"fak"},R={"kit-duotone":"fakd"},D={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},T=["fak","fa-kit","fakd","fa-kit-duotone"],Y={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"}},W=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt","fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands"],_=[1,2,3,4,5,6,7,8,9,10],U=_.concat([11,12,13,14,15,16,17,18,19,20]),H=[...Object.keys({classic:["fas","far","fal","fat","fad"],duotone:["fadr","fadl","fadt"],sharp:["fass","fasr","fasl","fast"],"sharp-duotone":["fasds","fasdr","fasdl","fasdt"]}),"solid","regular","light","thin","duotone","brands","2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","fw","inverse","layers-counter","layers-text","layers","li","pull-left","pull-right","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul",D.GROUP,D.SWAP_OPACITY,D.PRIMARY,D.SECONDARY].concat(_.map((t=>"".concat(t,"x")))).concat(U.map((t=>"w-".concat(t))));const B="___FONT_AWESOME___",X=16,q="svg-inline--fa",V="data-fa-i2svg",G="data-fa-pseudo-element",K="data-prefix",$="data-icon",J="fontawesome-i2svg",Q=["HTML","HEAD","STYLE","SCRIPT"],Z=(()=>{try{return!0}catch(t){return!1}})();function tt(t){return new Proxy(t,{get:(t,n)=>n in t?t[n]:t[S]})}const nt=d({},P);nt[S]=d(d(d(d({},{"fa-duotone":"duotone"}),P[S]),I),M);const et=tt(nt),at=d({},{classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",brands:"fab"},duotone:{solid:"fad",regular:"fadr",light:"fadl",thin:"fadt"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds",regular:"fasdr",light:"fasdl",thin:"fasdt"}});at[S]=d(d(d(d({},{duotone:"fad"}),at[S]),F),R);const rt=tt(at),ot=d({},Y);ot[S]=d(d({},ot[S]),{fak:"fa-kit"});const it=tt(ot),st=d({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"}});st[S]=d(d({},st[S]),{"fa-kit":"fak"}),tt(st);const ct=/fa(s|r|l|t|d|dr|dl|dt|b|k|kd|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/,lt="fa-layers-text",ft=/Font ?Awesome ?([56 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit)?.*/i;tt(d({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"}}));const ut=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],dt={GROUP:"duotone-group",PRIMARY:"primary",SECONDARY:"secondary"},mt=["kit",...H],pt=v.FontAwesomeConfig||{};if(x&&"function"==typeof x.querySelector){[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach((t=>{let[n,e]=t;const a=function(t){return""===t||"false"!==t&&("true"===t||t)}(function(t){var n=x.querySelector("script["+t+"]");if(n)return n.getAttribute(t)}(n));null!=a&&(pt[e]=a)}))}const ht={styleDefault:"solid",familyDefault:S,cssPrefix:"fa",replacementClass:q,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0};pt.familyPrefix&&(pt.cssPrefix=pt.familyPrefix);const gt=d(d({},ht),pt);gt.autoReplaceSvg||(gt.observeMutations=!1);const bt={};Object.keys(ht).forEach((t=>{Object.defineProperty(bt,t,{enumerable:!0,set:function(n){gt[t]=n,yt.forEach((t=>t(bt)))},get:function(){return gt[t]}})})),Object.defineProperty(bt,"familyPrefix",{enumerable:!0,set:function(t){gt.cssPrefix=t,yt.forEach((t=>t(bt)))},get:function(){return gt.cssPrefix}}),v.FontAwesomeConfig=bt;const yt=[];const vt=X,xt={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function kt(){let t=12,n="";for(;t-- >0;)n+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return n}function wt(t){const n=[];for(let e=(t||[]).length>>>0;e--;)n[e]=t[e];return n}function Ot(t){return t.classList?wt(t.classList):(t.getAttribute("class")||"").split(" ").filter((t=>t))}function At(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Pt(t){return Object.keys(t||{}).reduce(((n,e)=>n+"".concat(e,": ").concat(t[e].trim(),";")),"")}function Nt(t){return t.size!==xt.size||t.x!==xt.x||t.y!==xt.y||t.rotate!==xt.rotate||t.flipX||t.flipY}function St(){const t="fa",n=q,e=bt.cssPrefix,a=bt.replacementClass;let r=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 6 Free";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 6 Free";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 6 Pro";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 6 Pro";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 6 Duotone";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 6 Brands";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 6 Sharp";\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 6 Sharp Duotone";\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 6 Sharp Duotone";\n}\n\nsvg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa {\n  overflow: visible;\n  box-sizing: content-box;\n}\n\n.svg-inline--fa {\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285705em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: var(--fa-pull-margin, 0.3em);\n  width: auto;\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  top: 0.25em;\n}\n.svg-inline--fa.fa-fw {\n  width: var(--fa-fw-width, 1.25em);\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-counter-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: 0.625em;\n  line-height: 0.1em;\n  vertical-align: 0.225em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n  line-height: 0.0833333337em;\n  vertical-align: 0.125em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n  line-height: 0.0714285718em;\n  vertical-align: 0.0535714295em;\n}\n\n.fa-lg {\n  font-size: 1.25em;\n  line-height: 0.05em;\n  vertical-align: -0.075em;\n}\n\n.fa-xl {\n  font-size: 1.5em;\n  line-height: 0.0416666682em;\n  vertical-align: -0.125em;\n}\n\n.fa-2xl {\n  font-size: 2em;\n  line-height: 0.03125em;\n  vertical-align: -0.1875em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: var(--fa-li-margin, 2.5em);\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: calc(-1 * var(--fa-li-width, 2em));\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.08em);\n  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em);\n}\n\n.fa-pull-left {\n  float: left;\n  margin-right: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right {\n  float: right;\n  margin-left: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  animation-name: fa-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  animation-name: fa-shake;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  animation-name: fa-spin;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 2s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n.fa-bounce,\n.fa-fade,\n.fa-beat-fade,\n.fa-flip,\n.fa-pulse,\n.fa-shake,\n.fa-spin,\n.fa-spin-pulse {\n    animation-delay: -1ms;\n    animation-duration: 1ms;\n    animation-iteration-count: 1;\n    transition-delay: 0s;\n    transition-duration: 0s;\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    transform: scale(1);\n  }\n  45% {\n    transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg);\n  }\n  4% {\n    transform: rotate(15deg);\n  }\n  8%, 24% {\n    transform: rotate(-18deg);\n  }\n  12%, 28% {\n    transform: rotate(18deg);\n  }\n  16% {\n    transform: rotate(-22deg);\n  }\n  20% {\n    transform: rotate(22deg);\n  }\n  32% {\n    transform: rotate(-12deg);\n  }\n  36% {\n    transform: rotate(12deg);\n  }\n  40%, 100% {\n    transform: rotate(0deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.fa-stack {\n  display: inline-block;\n  vertical-align: middle;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: var(--fa-stack-z-index, auto);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.sr-only,\n.fa-sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.sr-only-focusable:not(:focus),\n.fa-sr-only-focusable:not(:focus) {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}';if(e!==t||a!==n){const o=new RegExp("\\.".concat(t,"\\-"),"g"),i=new RegExp("\\--".concat(t,"\\-"),"g"),s=new RegExp("\\.".concat(n),"g");r=r.replace(o,".".concat(e,"-")).replace(i,"--".concat(e,"-")).replace(s,".".concat(a))}return r}let Ct=!1;function jt(){bt.autoAddCss&&!Ct&&(!function(t){if(!t||!O)return;const n=x.createElement("style");n.setAttribute("type","text/css"),n.innerHTML=t;const e=x.head.childNodes;let a=null;for(let r=e.length-1;r>-1;r--){const t=e[r],n=(t.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(n)>-1&&(a=t)}x.head.insertBefore(n,a)}(St()),Ct=!0)}var zt={mixout:()=>({dom:{css:St,insertCss:jt}}),hooks:()=>({beforeDOMElementCreation(){jt()},beforeI2svg(){jt()}})};const Et=v||{};Et[B]||(Et[B]={}),Et[B].styles||(Et[B].styles={}),Et[B].hooks||(Et[B].hooks={}),Et[B].shims||(Et[B].shims=[]);var It=Et[B];const Mt=[],Lt=function(){x.removeEventListener("DOMContentLoaded",Lt),Ft=1,Mt.map((t=>t()))};let Ft=!1;function Rt(t){const{tag:n,attributes:e={},children:a=[]}=t;return"string"==typeof t?At(t):"<".concat(n," ").concat(function(t){return Object.keys(t||{}).reduce(((n,e)=>n+"".concat(e,'="').concat(At(t[e]),'" ')),"").trim()}(e),">").concat(a.map(Rt).join(""),"</").concat(n,">")}function Dt(t,n,e){if(t&&t[n]&&t[n][e])return{prefix:n,iconName:e,icon:t[n][e]}}O&&(Ft=(x.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(x.readyState),Ft||x.addEventListener("DOMContentLoaded",Lt));var Tt=function(t,n,e,a){var r,o,i,s=Object.keys(t),c=s.length,l=n;for(void 0===e?(r=1,i=t[s[0]]):(r=0,i=e);r<c;r++)i=l(i,t[o=s[r]],o,t);return i};function Yt(t){const n=function(t){const n=[];let e=0;const a=t.length;for(;e<a;){const r=t.charCodeAt(e++);if(r>=55296&&r<=56319&&e<a){const a=t.charCodeAt(e++);56320==(64512&a)?n.push(((1023&r)<<10)+(1023&a)+65536):(n.push(r),e--)}else n.push(r)}return n}(t);return 1===n.length?n[0].toString(16):null}function Wt(t){return Object.keys(t).reduce(((n,e)=>{const a=t[e];return!!a.icon?n[a.iconName]=a.icon:n[e]=a,n}),{})}function _t(t,n){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{skipHooks:a=!1}=e,r=Wt(n);"function"!=typeof It.hooks.addPack||a?It.styles[t]=d(d({},It.styles[t]||{}),r):It.hooks.addPack(t,Wt(n)),"fas"===t&&_t("fa",n)}const{styles:Ut,shims:Ht}=It,Bt=Object.keys(it),Xt=Bt.reduce(((t,n)=>(t[n]=Object.keys(it[n]),t)),{});let qt=null,Vt={},Gt={},Kt={},$t={},Jt={};function Qt(t,n){const e=n.split("-"),a=e[0],r=e.slice(1).join("-");return a!==t||""===r||(o=r,~mt.indexOf(o))?null:r;var o}const Zt=()=>{const t=t=>Tt(Ut,((n,e,a)=>(n[a]=Tt(e,t,{}),n)),{});Vt=t(((t,n,e)=>{if(n[3]&&(t[n[3]]=e),n[2]){n[2].filter((t=>"number"==typeof t)).forEach((n=>{t[n.toString(16)]=e}))}return t})),Gt=t(((t,n,e)=>{if(t[e]=e,n[2]){n[2].filter((t=>"string"==typeof t)).forEach((n=>{t[n]=e}))}return t})),Jt=t(((t,n,e)=>{const a=n[2];return t[e]=e,a.forEach((n=>{t[n]=e})),t}));const n="far"in Ut||bt.autoFetchSvg,e=Tt(Ht,((t,e)=>{const a=e[0];let r=e[1];const o=e[2];return"far"!==r||n||(r="fas"),"string"==typeof a&&(t.names[a]={prefix:r,iconName:o}),"number"==typeof a&&(t.unicodes[a.toString(16)]={prefix:r,iconName:o}),t}),{names:{},unicodes:{}});Kt=e.names,$t=e.unicodes,qt=on(bt.styleDefault,{family:bt.familyDefault})};var tn;function nn(t,n){return(Vt[t]||{})[n]}function en(t,n){return(Jt[t]||{})[n]}function an(t){return Kt[t]||{prefix:null,iconName:null}}function rn(){return qt}tn=t=>{qt=on(t.styleDefault,{family:bt.familyDefault})},yt.push(tn),Zt();function on(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{family:e=S}=n,a=et[e][t];if(e===C&&!t)return"fad";const r=rt[e][t]||rt[e][a],o=t in It.styles?t:null;return r||o||null}function sn(t){return t.sort().filter(((t,n,e)=>e.indexOf(t)===n))}function cn(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{skipLookups:e=!1}=n;let a=null;const r=W.concat(T),o=sn(t.filter((t=>r.includes(t)))),i=sn(t.filter((t=>!W.includes(t)))),s=o.filter((t=>(a=t,!N.includes(t)))),[c=null]=s,l=function(t){let n=S;const e=Bt.reduce(((t,n)=>(t[n]="".concat(bt.cssPrefix,"-").concat(n),t)),{});return j.forEach((a=>{(t.includes(e[a])||t.some((t=>Xt[a].includes(t))))&&(n=a)})),n}(o),f=d(d({},function(t){let n=[],e=null;return t.forEach((t=>{const a=Qt(bt.cssPrefix,t);a?e=a:t&&n.push(t)})),{iconName:e,rest:n}}(i)),{},{prefix:on(c,{family:l})});return d(d(d({},f),function(t){const{values:n,family:e,canonical:a,givenPrefix:r="",styles:o={},config:i={}}=t,s=e===C,c=n.includes("fa-duotone")||n.includes("fad"),l="duotone"===i.familyDefault,f="fad"===a.prefix||"fa-duotone"===a.prefix;!s&&(c||l||f)&&(a.prefix="fad");(n.includes("fa-brands")||n.includes("fab"))&&(a.prefix="fab");if(!a.prefix&&ln.includes(e)){if(Object.keys(o).find((t=>fn.includes(t)))||i.autoFetchSvg){const t=z.get(e).defaultShortPrefixId;a.prefix=t,a.iconName=en(a.prefix,a.iconName)||a.iconName}}"fa"!==a.prefix&&"fa"!==r||(a.prefix=rn()||"fas");return a}({values:t,family:l,styles:Ut,config:bt,canonical:f,givenPrefix:a})),function(t,n,e){let{prefix:a,iconName:r}=e;if(t||!a||!r)return{prefix:a,iconName:r};const o="fa"===n?an(r):{},i=en(a,r);r=o.iconName||i||r,a=o.prefix||a,"far"!==a||Ut.far||!Ut.fas||bt.autoFetchSvg||(a="fas");return{prefix:a,iconName:r}}(e,a,f))}const ln=j.filter((t=>t!==S||t!==C)),fn=Object.keys(Y).filter((t=>t!==S)).map((t=>Object.keys(Y[t]))).flat();let un=[],dn={};const mn={},pn=Object.keys(mn);function hn(t,n){for(var e=arguments.length,a=new Array(e>2?e-2:0),r=2;r<e;r++)a[r-2]=arguments[r];return(dn[t]||[]).forEach((t=>{n=t.apply(null,[n,...a])})),n}function gn(t){for(var n=arguments.length,e=new Array(n>1?n-1:0),a=1;a<n;a++)e[a-1]=arguments[a];(dn[t]||[]).forEach((t=>{t.apply(null,e)}))}function bn(){const t=arguments[0],n=Array.prototype.slice.call(arguments,1);return mn[t]?mn[t].apply(null,n):void 0}function yn(t){"fa"===t.prefix&&(t.prefix="fas");let{iconName:n}=t;const e=t.prefix||rn();if(n)return n=en(e,n)||n,Dt(vn.definitions,e,n)||Dt(It.styles,e,n)}const vn=new class{constructor(){this.definitions={}}add(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];const a=n.reduce(this._pullDefinitions,{});Object.keys(a).forEach((t=>{this.definitions[t]=d(d({},this.definitions[t]||{}),a[t]),_t(t,a[t]);const n=it[S][t];n&&_t(n,a[t]),Zt()}))}reset(){this.definitions={}}_pullDefinitions(t,n){const e=n.prefix&&n.iconName&&n.icon?{0:n}:n;return Object.keys(e).map((n=>{const{prefix:a,iconName:r,icon:o}=e[n],i=o[2];t[a]||(t[a]={}),i.length>0&&i.forEach((n=>{"string"==typeof n&&(t[a][n]=o)})),t[a][r]=o})),t}},xn={i2svg:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return O?(gn("beforeI2svg",t),bn("pseudoElements2svg",t),bn("i2svg",t)):Promise.reject(new Error("Operation requires a DOM of some kind."))},watch:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{autoReplaceSvgRoot:n}=t;var e;!1===bt.autoReplaceSvg&&(bt.autoReplaceSvg=!0),bt.observeMutations=!0,e=()=>{wn({autoReplaceSvgRoot:n}),gn("watch",t)},O&&(Ft?setTimeout(e,0):Mt.push(e))}},kn={noAuto:()=>{bt.autoReplaceSvg=!1,bt.observeMutations=!1,gn("noAuto")},config:bt,dom:xn,parse:{icon:t=>{if(null===t)return null;if("object"==typeof t&&t.prefix&&t.iconName)return{prefix:t.prefix,iconName:en(t.prefix,t.iconName)||t.iconName};if(Array.isArray(t)&&2===t.length){const n=0===t[1].indexOf("fa-")?t[1].slice(3):t[1],e=on(t[0]);return{prefix:e,iconName:en(e,n)||n}}if("string"==typeof t&&(t.indexOf("".concat(bt.cssPrefix,"-"))>-1||t.match(ct))){const n=cn(t.split(" "),{skipLookups:!0});return{prefix:n.prefix||rn(),iconName:en(n.prefix,n.iconName)||n.iconName}}if("string"==typeof t){const n=rn();return{prefix:n,iconName:en(n,t)||t}}}},library:vn,findIconDefinition:yn,toHtml:Rt},wn=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{autoReplaceSvgRoot:n=x}=t;(Object.keys(It.styles).length>0||bt.autoFetchSvg)&&O&&bt.autoReplaceSvg&&kn.dom.i2svg({node:n})};function On(t,n){return Object.defineProperty(t,"abstract",{get:n}),Object.defineProperty(t,"html",{get:function(){return t.abstract.map((t=>Rt(t)))}}),Object.defineProperty(t,"node",{get:function(){if(!O)return;const n=x.createElement("div");return n.innerHTML=t.html,n.children}}),t}function An(t){const{icons:{main:n,mask:e},prefix:a,iconName:r,transform:o,symbol:i,title:s,maskId:c,titleId:l,extra:f,watchable:u=!1}=t,{width:m,height:p}=e.found?e:n,h=L.includes(a),g=[bt.replacementClass,r?"".concat(bt.cssPrefix,"-").concat(r):""].filter((t=>-1===f.classes.indexOf(t))).filter((t=>""!==t||!!t)).concat(f.classes).join(" ");let b={children:[],attributes:d(d({},f.attributes),{},{"data-prefix":a,"data-icon":r,class:g,role:f.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(m," ").concat(p)})};const y=h&&!~f.classes.indexOf("fa-fw")?{width:"".concat(m/p*16*.0625,"em")}:{};u&&(b.attributes[V]=""),s&&(b.children.push({tag:"title",attributes:{id:b.attributes["aria-labelledby"]||"title-".concat(l||kt())},children:[s]}),delete b.attributes.title);const v=d(d({},b),{},{prefix:a,iconName:r,main:n,mask:e,maskId:c,transform:o,symbol:i,styles:d(d({},y),f.styles)}),{children:x,attributes:k}=e.found&&n.found?bn("generateAbstractMask",v)||{children:[],attributes:{}}:bn("generateAbstractIcon",v)||{children:[],attributes:{}};return v.children=x,v.attributes=k,i?function(t){let{prefix:n,iconName:e,children:a,attributes:r,symbol:o}=t;const i=!0===o?"".concat(n,"-").concat(bt.cssPrefix,"-").concat(e):o;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:d(d({},r),{},{id:i}),children:a}]}]}(v):function(t){let{children:n,main:e,mask:a,attributes:r,styles:o,transform:i}=t;if(Nt(i)&&e.found&&!a.found){const{width:t,height:n}=e,a={x:t/n/2,y:.5};r.style=Pt(d(d({},o),{},{"transform-origin":"".concat(a.x+i.x/16,"em ").concat(a.y+i.y/16,"em")}))}return[{tag:"svg",attributes:r,children:n}]}(v)}function Pn(t){const{content:n,width:e,height:a,transform:r,title:o,extra:i,watchable:s=!1}=t,c=d(d(d({},i.attributes),o?{title:o}:{}),{},{class:i.classes.join(" ")});s&&(c[V]="");const l=d({},i.styles);Nt(r)&&(l.transform=function(t){let{transform:n,width:e=X,height:a=X,startCentered:r=!1}=t,o="";return o+=r&&A?"translate(".concat(n.x/vt-e/2,"em, ").concat(n.y/vt-a/2,"em) "):r?"translate(calc(-50% + ".concat(n.x/vt,"em), calc(-50% + ").concat(n.y/vt,"em)) "):"translate(".concat(n.x/vt,"em, ").concat(n.y/vt,"em) "),o+="scale(".concat(n.size/vt*(n.flipX?-1:1),", ").concat(n.size/vt*(n.flipY?-1:1),") "),o+="rotate(".concat(n.rotate,"deg) "),o}({transform:r,startCentered:!0,width:e,height:a}),l["-webkit-transform"]=l.transform);const f=Pt(l);f.length>0&&(c.style=f);const u=[];return u.push({tag:"span",attributes:c,children:[n]}),o&&u.push({tag:"span",attributes:{class:"sr-only"},children:[o]}),u}const{styles:Nn}=It;function Sn(t){const n=t[0],e=t[1],[a]=t.slice(4);let r=null;return r=Array.isArray(a)?{tag:"g",attributes:{class:"".concat(bt.cssPrefix,"-").concat(dt.GROUP)},children:[{tag:"path",attributes:{class:"".concat(bt.cssPrefix,"-").concat(dt.SECONDARY),fill:"currentColor",d:a[0]}},{tag:"path",attributes:{class:"".concat(bt.cssPrefix,"-").concat(dt.PRIMARY),fill:"currentColor",d:a[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:a}},{found:!0,width:n,height:e,icon:r}}const Cn={found:!1,width:512,height:512};function jn(t,n){let e=n;return"fa"===n&&null!==bt.styleDefault&&(n=rn()),new Promise(((a,r)=>{if("fa"===e){const e=an(t)||{};t=e.iconName||t,n=e.prefix||n}if(t&&n&&Nn[n]&&Nn[n][t]){return a(Sn(Nn[n][t]))}!Z&&bt.showMissingIcons,a(d(d({},Cn),{},{icon:bt.showMissingIcons&&t&&bn("missingIconAbstract")||{}}))}))}const zn=()=>{},En=bt.measurePerformance&&w&&w.mark&&w.measure?w:{mark:zn,measure:zn},In='FA "6.7.2"',Mn=t=>{En.mark("".concat(In," ").concat(t," ends")),En.measure("".concat(In," ").concat(t),"".concat(In," ").concat(t," begins"),"".concat(In," ").concat(t," ends"))};var Ln=t=>(En.mark("".concat(In," ").concat(t," begins")),()=>Mn(t));const Fn=()=>{};function Rn(t){return"string"==typeof(t.getAttribute?t.getAttribute(V):null)}function Dn(t){return x.createElementNS("http://www.w3.org/2000/svg",t)}function Tn(t){return x.createElement(t)}function Yn(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{ceFn:e=("svg"===t.tag?Dn:Tn)}=n;if("string"==typeof t)return x.createTextNode(t);const a=e(t.tag);Object.keys(t.attributes||[]).forEach((function(n){a.setAttribute(n,t.attributes[n])}));return(t.children||[]).forEach((function(t){a.appendChild(Yn(t,{ceFn:e}))})),a}const Wn={replace:function(t){const n=t[0];if(n.parentNode)if(t[1].forEach((t=>{n.parentNode.insertBefore(Yn(t),n)})),null===n.getAttribute(V)&&bt.keepOriginalSource){let t=x.createComment(function(t){let n=" ".concat(t.outerHTML," ");return n="".concat(n,"Font Awesome fontawesome.com "),n}(n));n.parentNode.replaceChild(t,n)}else n.remove()},nest:function(t){const n=t[0],e=t[1];if(~Ot(n).indexOf(bt.replacementClass))return Wn.replace(t);const a=new RegExp("".concat(bt.cssPrefix,"-.*"));if(delete e[0].attributes.id,e[0].attributes.class){const t=e[0].attributes.class.split(" ").reduce(((t,n)=>(n===bt.replacementClass||n.match(a)?t.toSvg.push(n):t.toNode.push(n),t)),{toNode:[],toSvg:[]});e[0].attributes.class=t.toSvg.join(" "),0===t.toNode.length?n.removeAttribute("class"):n.setAttribute("class",t.toNode.join(" "))}const r=e.map((t=>Rt(t))).join("\n");n.setAttribute(V,""),n.innerHTML=r}};function _n(t){t()}function Un(t,n){const e="function"==typeof n?n:Fn;if(0===t.length)e();else{let n=_n;"async"===bt.mutateApproach&&(n=v.requestAnimationFrame||_n),n((()=>{const n=!0===bt.autoReplaceSvg?Wn.replace:Wn[bt.autoReplaceSvg]||Wn.replace,a=Ln("mutate");t.map(n),a(),e()}))}}let Hn=!1;function Bn(){Hn=!0}function Xn(){Hn=!1}let qn=null;function Vn(t){if(!k)return;if(!bt.observeMutations)return;const{treeCallback:n=Fn,nodeCallback:e=Fn,pseudoElementsCallback:a=Fn,observeMutationsRoot:r=x}=t;qn=new k((t=>{if(Hn)return;const r=rn();wt(t).forEach((t=>{if("childList"===t.type&&t.addedNodes.length>0&&!Rn(t.addedNodes[0])&&(bt.searchPseudoElements&&a(t.target),n(t.target)),"attributes"===t.type&&t.target.parentNode&&bt.searchPseudoElements&&a(t.target.parentNode),"attributes"===t.type&&Rn(t.target)&&~ut.indexOf(t.attributeName))if("class"===t.attributeName&&function(t){const n=t.getAttribute?t.getAttribute(K):null,e=t.getAttribute?t.getAttribute($):null;return n&&e}(t.target)){const{prefix:n,iconName:e}=cn(Ot(t.target));t.target.setAttribute(K,n||r),e&&t.target.setAttribute($,e)}else(o=t.target)&&o.classList&&o.classList.contains&&o.classList.contains(bt.replacementClass)&&e(t.target);var o}))})),O&&qn.observe(r,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}function Gn(t){const n=t.getAttribute("data-prefix"),e=t.getAttribute("data-icon"),a=void 0!==t.innerText?t.innerText.trim():"";let r=cn(Ot(t));return r.prefix||(r.prefix=rn()),n&&e&&(r.prefix=n,r.iconName=e),r.iconName&&r.prefix||(r.prefix&&a.length>0&&(r.iconName=(o=r.prefix,i=t.innerText,(Gt[o]||{})[i]||nn(r.prefix,Yt(t.innerText)))),!r.iconName&&bt.autoFetchSvg&&t.firstChild&&t.firstChild.nodeType===Node.TEXT_NODE&&(r.iconName=t.firstChild.data)),r;var o,i}function Kn(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{styleParser:!0};const{iconName:e,prefix:a,rest:r}=Gn(t),o=function(t){const n=wt(t.attributes).reduce(((t,n)=>("class"!==t.name&&"style"!==t.name&&(t[n.name]=n.value),t)),{}),e=t.getAttribute("title"),a=t.getAttribute("data-fa-title-id");return bt.autoA11y&&(e?n["aria-labelledby"]="".concat(bt.replacementClass,"-title-").concat(a||kt()):(n["aria-hidden"]="true",n.focusable="false")),n}(t),i=hn("parseNodeAttributes",{},t);let s=n.styleParser?function(t){const n=t.getAttribute("style");let e=[];return n&&(e=n.split(";").reduce(((t,n)=>{const e=n.split(":"),a=e[0],r=e.slice(1);return a&&r.length>0&&(t[a]=r.join(":").trim()),t}),{})),e}(t):[];return d({iconName:e,title:t.getAttribute("title"),titleId:t.getAttribute("data-fa-title-id"),prefix:a,transform:xt,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:r,styles:s,attributes:o}},i)}const{styles:$n}=It;function Jn(t){const n="nest"===bt.autoReplaceSvg?Kn(t,{styleParser:!1}):Kn(t);return~n.extra.classes.indexOf(lt)?bn("generateLayersText",t,n):bn("generateSvgReplacementMutation",t,n)}function Qn(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!O)return Promise.resolve();const e=x.documentElement.classList,a=t=>e.add("".concat(J,"-").concat(t)),r=t=>e.remove("".concat(J,"-").concat(t)),o=bt.autoFetchSvg?[...E,...W]:N.concat(Object.keys($n));o.includes("fa")||o.push("fa");const i=[".".concat(lt,":not([").concat(V,"])")].concat(o.map((t=>".".concat(t,":not([").concat(V,"])")))).join(", ");if(0===i.length)return Promise.resolve();let s=[];try{s=wt(t.querySelectorAll(i))}catch(f){}if(!(s.length>0))return Promise.resolve();a("pending"),r("complete");const c=Ln("onTree"),l=s.reduce(((t,n)=>{try{const e=Jn(n);e&&t.push(e)}catch(f){Z||f.name}return t}),[]);return new Promise(((t,e)=>{Promise.all(l).then((e=>{Un(e,(()=>{a("active"),a("complete"),r("pending"),"function"==typeof n&&n(),c(),t()}))})).catch((t=>{c(),e(t)}))}))}function Zn(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;Jn(t).then((t=>{t&&Un([t],n)}))}const te=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{transform:e=xt,symbol:a=!1,mask:r=null,maskId:o=null,title:i=null,titleId:s=null,classes:c=[],attributes:l={},styles:f={}}=n;if(!t)return;const{prefix:u,iconName:m,icon:p}=t;return On(d({type:"icon"},t),(()=>(gn("beforeDOMElementCreation",{iconDefinition:t,params:n}),bt.autoA11y&&(i?l["aria-labelledby"]="".concat(bt.replacementClass,"-title-").concat(s||kt()):(l["aria-hidden"]="true",l.focusable="false")),An({icons:{main:Sn(p),mask:r?Sn(r.icon):{found:!1,width:null,height:null,icon:{}}},prefix:u,iconName:m,transform:d(d({},xt),e),symbol:a,title:i,maskId:o,titleId:s,extra:{attributes:l,styles:f,classes:c}}))))};var ne={mixout(){return{icon:(t=te,function(n){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a=(n||{}).icon?n:yn(n||{});let{mask:r}=e;return r&&(r=(r||{}).icon?r:yn(r||{})),t(a,d(d({},e),{},{mask:r}))})};var t},hooks:()=>({mutationObserverCallbacks:t=>(t.treeCallback=Qn,t.nodeCallback=Zn,t)}),provides(t){t.i2svg=function(t){const{node:n=x,callback:e=()=>{}}=t;return Qn(n,e)},t.generateSvgReplacementMutation=function(t,n){const{iconName:e,title:a,titleId:r,prefix:o,transform:i,symbol:s,mask:c,maskId:l,extra:f}=n;return new Promise(((n,u)=>{Promise.all([jn(e,o),c.iconName?jn(c.iconName,c.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then((c=>{let[u,d]=c;n([t,An({icons:{main:u,mask:d},prefix:o,iconName:e,transform:i,symbol:s,maskId:l,title:a,titleId:r,extra:f,watchable:!0})])})).catch(u)}))},t.generateAbstractIcon=function(t){let{children:n,attributes:e,main:a,transform:r,styles:o}=t;const i=Pt(o);let s;return i.length>0&&(e.style=i),Nt(r)&&(s=bn("generateAbstractTransformGrouping",{main:a,transform:r,containerWidth:a.width,iconWidth:a.width})),n.push(s||a.icon),{children:n,attributes:e}}}},ee={mixout:()=>({layer(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{classes:e=[]}=n;return On({type:"layer"},(()=>{gn("beforeDOMElementCreation",{assembler:t,params:n});let a=[];return t((t=>{Array.isArray(t)?t.map((t=>{a=a.concat(t.abstract)})):a=a.concat(t.abstract)})),[{tag:"span",attributes:{class:["".concat(bt.cssPrefix,"-layers"),...e].join(" ")},children:a}]}))}})},ae={mixout:()=>({counter(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{title:e=null,classes:a=[],attributes:r={},styles:o={}}=n;return On({type:"counter",content:t},(()=>(gn("beforeDOMElementCreation",{content:t,params:n}),function(t){const{content:n,title:e,extra:a}=t,r=d(d(d({},a.attributes),e?{title:e}:{}),{},{class:a.classes.join(" ")}),o=Pt(a.styles);o.length>0&&(r.style=o);const i=[];return i.push({tag:"span",attributes:r,children:[n]}),e&&i.push({tag:"span",attributes:{class:"sr-only"},children:[e]}),i}({content:t.toString(),title:e,extra:{attributes:r,styles:o,classes:["".concat(bt.cssPrefix,"-layers-counter"),...a]}}))))}})},re={mixout:()=>({text(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{transform:e=xt,title:a=null,classes:r=[],attributes:o={},styles:i={}}=n;return On({type:"text",content:t},(()=>(gn("beforeDOMElementCreation",{content:t,params:n}),Pn({content:t,transform:d(d({},xt),e),title:a,extra:{attributes:o,styles:i,classes:["".concat(bt.cssPrefix,"-layers-text"),...r]}}))))}}),provides(t){t.generateLayersText=function(t,n){const{title:e,transform:a,extra:r}=n;let o=null,i=null;if(A){const n=parseInt(getComputedStyle(t).fontSize,10),e=t.getBoundingClientRect();o=e.width/n,i=e.height/n}return bt.autoA11y&&!e&&(r.attributes["aria-hidden"]="true"),Promise.resolve([t,Pn({content:t.innerHTML,width:o,height:i,transform:a,title:e,extra:r,watchable:!0})])}}};const oe=new RegExp('"',"ug"),ie=[1105920,1112319],se=d(d(d(d({},{FontAwesome:{normal:"fas",400:"fas"}}),{"Font Awesome 6 Free":{900:"fas",400:"far"},"Font Awesome 6 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 6 Brands":{400:"fab",normal:"fab"},"Font Awesome 6 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 6 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 6 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"}}),{"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}}),{"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}}),ce=Object.keys(se).reduce(((t,n)=>(t[n.toLowerCase()]=se[n],t)),{}),le=Object.keys(ce).reduce(((t,n)=>{const e=ce[n];return t[n]=e[900]||[...Object.entries(e)][0][1],t}),{});function fe(t,n){const e="".concat("data-fa-pseudo-element-pending").concat(n.replace(":","-"));return new Promise(((a,r)=>{if(null!==t.getAttribute(e))return a();const o=wt(t.children).filter((t=>t.getAttribute(G)===n))[0],i=v.getComputedStyle(t,n),s=i.getPropertyValue("font-family"),c=s.match(ft),l=i.getPropertyValue("font-weight"),f=i.getPropertyValue("content");if(o&&!c)return t.removeChild(o),a();if(c&&"none"!==f&&""!==f){const f=i.getPropertyValue("content");let u=function(t,n){const e=t.replace(/^['"]|['"]$/g,"").toLowerCase(),a=parseInt(n),r=isNaN(a)?"normal":a;return(ce[e]||{})[r]||le[e]}(s,l);const{value:m,isSecondary:p}=function(t){const n=t.replace(oe,""),e=function(t,n){const e=t.length;let a,r=t.charCodeAt(n);return r>=55296&&r<=56319&&e>n+1&&(a=t.charCodeAt(n+1),a>=56320&&a<=57343)?1024*(r-55296)+a-56320+65536:r}(n,0),a=e>=ie[0]&&e<=ie[1],r=2===n.length&&n[0]===n[1];return{value:Yt(r?n[0]:n),isSecondary:a||r}}(f),h=c[0].startsWith("FontAwesome");let g=nn(u,m),b=g;if(h){const t=function(t){const n=$t[t],e=nn("fas",t);return n||(e?{prefix:"fas",iconName:e}:null)||{prefix:null,iconName:null}}(m);t.iconName&&t.prefix&&(g=t.iconName,u=t.prefix)}if(!g||p||o&&o.getAttribute(K)===u&&o.getAttribute($)===b)a();else{t.setAttribute(e,b),o&&t.removeChild(o);const i={iconName:null,title:null,titleId:null,prefix:null,transform:xt,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}},{extra:s}=i;s.attributes[G]=n,jn(g,u).then((r=>{const o=An(d(d({},i),{},{icons:{main:r,mask:{prefix:null,iconName:null,rest:[]}},prefix:u,iconName:b,extra:s,watchable:!0})),c=x.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===n?t.insertBefore(c,t.firstChild):t.appendChild(c),c.outerHTML=o.map((t=>Rt(t))).join("\n"),t.removeAttribute(e),a()})).catch(r)}}else a()}))}function ue(t){return Promise.all([fe(t,"::before"),fe(t,"::after")])}function de(t){return!(t.parentNode===document.head||~Q.indexOf(t.tagName.toUpperCase())||t.getAttribute(G)||t.parentNode&&"svg"===t.parentNode.tagName)}function me(t){if(O)return new Promise(((n,e)=>{const a=wt(t.querySelectorAll("*")).filter(de).map(ue),r=Ln("searchPseudoElements");Bn(),Promise.all(a).then((()=>{r(),Xn(),n()})).catch((()=>{r(),Xn(),e()}))}))}let pe=!1;const he=t=>t.toLowerCase().split(" ").reduce(((t,n)=>{const e=n.toLowerCase().split("-"),a=e[0];let r=e.slice(1).join("-");if(a&&"h"===r)return t.flipX=!0,t;if(a&&"v"===r)return t.flipY=!0,t;if(r=parseFloat(r),isNaN(r))return t;switch(a){case"grow":t.size=t.size+r;break;case"shrink":t.size=t.size-r;break;case"left":t.x=t.x-r;break;case"right":t.x=t.x+r;break;case"up":t.y=t.y-r;break;case"down":t.y=t.y+r;break;case"rotate":t.rotate=t.rotate+r}return t}),{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0});const ge={x:0,y:0,width:"100%",height:"100%"};function be(t){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return t.attributes&&(t.attributes.fill||n)&&(t.attributes.fill="black"),t}!function(t,n){let{mixoutsTo:e}=n;un=t,dn={},Object.keys(mn).forEach((t=>{-1===pn.indexOf(t)&&delete mn[t]})),un.forEach((t=>{const n=t.mixout?t.mixout():{};if(Object.keys(n).forEach((t=>{"function"==typeof n[t]&&(e[t]=n[t]),"object"==typeof n[t]&&Object.keys(n[t]).forEach((a=>{e[t]||(e[t]={}),e[t][a]=n[t][a]}))})),t.hooks){const n=t.hooks();Object.keys(n).forEach((t=>{dn[t]||(dn[t]=[]),dn[t].push(n[t])}))}t.provides&&t.provides(mn)}))}([zt,ne,ee,ae,re,{hooks:()=>({mutationObserverCallbacks:t=>(t.pseudoElementsCallback=me,t)}),provides(t){t.pseudoElements2svg=function(t){const{node:n=x}=t;bt.searchPseudoElements&&me(n)}}},{mixout:()=>({dom:{unwatch(){Bn(),pe=!0}}}),hooks:()=>({bootstrap(){Vn(hn("mutationObserverCallbacks",{}))},noAuto(){qn&&qn.disconnect()},watch(t){const{observeMutationsRoot:n}=t;pe?Xn():Vn(hn("mutationObserverCallbacks",{observeMutationsRoot:n}))}})},{mixout:()=>({parse:{transform:t=>he(t)}}),hooks:()=>({parseNodeAttributes(t,n){const e=n.getAttribute("data-fa-transform");return e&&(t.transform=he(e)),t}}),provides(t){t.generateAbstractTransformGrouping=function(t){let{main:n,transform:e,containerWidth:a,iconWidth:r}=t;const o={transform:"translate(".concat(a/2," 256)")},i="translate(".concat(32*e.x,", ").concat(32*e.y,") "),s="scale(".concat(e.size/16*(e.flipX?-1:1),", ").concat(e.size/16*(e.flipY?-1:1),") "),c="rotate(".concat(e.rotate," 0 0)"),l={outer:o,inner:{transform:"".concat(i," ").concat(s," ").concat(c)},path:{transform:"translate(".concat(r/2*-1," -256)")}};return{tag:"g",attributes:d({},l.outer),children:[{tag:"g",attributes:d({},l.inner),children:[{tag:n.icon.tag,children:n.icon.children,attributes:d(d({},n.icon.attributes),l.path)}]}]}}}},{hooks:()=>({parseNodeAttributes(t,n){const e=n.getAttribute("data-fa-mask"),a=e?cn(e.split(" ").map((t=>t.trim()))):{prefix:null,iconName:null,rest:[]};return a.prefix||(a.prefix=rn()),t.mask=a,t.maskId=n.getAttribute("data-fa-mask-id"),t}}),provides(t){t.generateAbstractMask=function(t){let{children:n,attributes:e,main:a,mask:r,maskId:o,transform:i}=t;const{width:s,icon:c}=a,{width:l,icon:f}=r,u=function(t){let{transform:n,containerWidth:e,iconWidth:a}=t;const r={transform:"translate(".concat(e/2," 256)")},o="translate(".concat(32*n.x,", ").concat(32*n.y,") "),i="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),s="rotate(".concat(n.rotate," 0 0)");return{outer:r,inner:{transform:"".concat(o," ").concat(i," ").concat(s)},path:{transform:"translate(".concat(a/2*-1," -256)")}}}({transform:i,containerWidth:l,iconWidth:s}),m={tag:"rect",attributes:d(d({},ge),{},{fill:"white"})},p=c.children?{children:c.children.map(be)}:{},h={tag:"g",attributes:d({},u.inner),children:[be(d({tag:c.tag,attributes:d(d({},c.attributes),u.path)},p))]},g={tag:"g",attributes:d({},u.outer),children:[h]},b="mask-".concat(o||kt()),y="clip-".concat(o||kt()),v={tag:"mask",attributes:d(d({},ge),{},{id:b,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[m,g]},x={tag:"defs",children:[{tag:"clipPath",attributes:{id:y},children:(k=f,"g"===k.tag?k.children:[k])},v]};var k;return n.push(x,{tag:"rect",attributes:d({fill:"currentColor","clip-path":"url(#".concat(y,")"),mask:"url(#".concat(b,")")},ge)}),{children:n,attributes:e}}}},{provides(t){let n=!1;v.matchMedia&&(n=v.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){const t=[],e={fill:"currentColor"},a={attributeType:"XML",repeatCount:"indefinite",dur:"2s"};t.push({tag:"path",attributes:d(d({},e),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})});const r=d(d({},a),{},{attributeName:"opacity"}),o={tag:"circle",attributes:d(d({},e),{},{cx:"256",cy:"364",r:"28"}),children:[]};return n||o.children.push({tag:"animate",attributes:d(d({},a),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:d(d({},r),{},{values:"1;0;1;1;0;1;"})}),t.push(o),t.push({tag:"path",attributes:d(d({},e),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:n?[]:[{tag:"animate",attributes:d(d({},r),{},{values:"1;0;0;0;0;1;"})}]}),n||t.push({tag:"path",attributes:d(d({},e),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:d(d({},r),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:t}}}},{hooks:()=>({parseNodeAttributes(t,n){const e=n.getAttribute("data-fa-symbol"),a=null!==e&&(""===e||e);return t.symbol=a,t}})}],{mixoutsTo:kn});const ye=kn.library,ve=kn.parse,xe=kn.icon;var ke,we,Oe,Ae,Pe,Ne={exports:{}};function Se(){if(we)return ke;we=1;return ke="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}function Ce(){if(Ae)return Oe;Ae=1;var t=Se();function n(){}function e(){}return e.resetWarningCache=n,Oe=function(){function a(n,e,a,r,o,i){if(i!==t){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function r(){return a}a.isRequired=a;var o={array:a,bigint:a,bool:a,func:a,number:a,object:a,string:a,symbol:a,any:a,arrayOf:r,element:a,elementType:a,instanceOf:r,node:a,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:e,resetWarningCache:n};return o.PropTypes=o,o}}function je(){return Pe||(Pe=1,Ne.exports=Ce()()),Ne.exports}const ze=t(je());function Ee(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);n&&(a=a.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,a)}return e}function Ie(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?Ee(Object(e),!0).forEach((function(n){Le(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Ee(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function Me(t){return(Me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Le(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function Fe(t,n){if(null==t)return{};var e,a,r=function(t,n){if(null==t)return{};var e,a,r={},o=Object.keys(t);for(a=0;a<o.length;a++)e=o[a],n.indexOf(e)>=0||(r[e]=t[e]);return r}(t,n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(a=0;a<o.length;a++)e=o[a],n.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(t,e)&&(r[e]=t[e])}return r}function Re(t){return function(t){if(Array.isArray(t))return De(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"==typeof t)return De(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return De(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function De(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,a=new Array(n);e<n;e++)a[e]=t[e];return a}function Te(t){return n=t,(n-=0)==n?t:(t=t.replace(/[\-_\s]+(.)?/g,(function(t,n){return n?n.toUpperCase():""}))).substr(0,1).toLowerCase()+t.substr(1);var n}var Ye=["style"];var We=!1;try{We=!0}catch(qe){}function _e(t){return t&&"object"===Me(t)&&t.prefix&&t.iconName&&t.icon?t:ve.icon?ve.icon(t):null===t?null:t&&"object"===Me(t)&&t.prefix&&t.iconName?t:Array.isArray(t)&&2===t.length?{prefix:t[0],iconName:t[1]}:"string"==typeof t?{prefix:"fas",iconName:t}:void 0}function Ue(t,n){return Array.isArray(n)&&n.length>0||!Array.isArray(n)&&n?Le({},t,n):{}}var He={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1},Be=n.forwardRef((function(t,n){var e=Ie(Ie({},He),t),a=e.icon,r=e.mask,o=e.symbol,i=e.className,s=e.title,c=e.titleId,l=e.maskId,f=_e(a),u=Ue("classes",[].concat(Re(function(t){var n,e=t.beat,a=t.fade,r=t.beatFade,o=t.bounce,i=t.shake,s=t.flash,c=t.spin,l=t.spinPulse,f=t.spinReverse,u=t.pulse,d=t.fixedWidth,m=t.inverse,p=t.border,h=t.listItem,g=t.flip,b=t.size,y=t.rotation,v=t.pull,x=(Le(n={"fa-beat":e,"fa-fade":a,"fa-beat-fade":r,"fa-bounce":o,"fa-shake":i,"fa-flash":s,"fa-spin":c,"fa-spin-reverse":f,"fa-spin-pulse":l,"fa-pulse":u,"fa-fw":d,"fa-inverse":m,"fa-border":p,"fa-li":h,"fa-flip":!0===g,"fa-flip-horizontal":"horizontal"===g||"both"===g,"fa-flip-vertical":"vertical"===g||"both"===g},"fa-".concat(b),null!=b),Le(n,"fa-rotate-".concat(y),null!=y&&0!==y),Le(n,"fa-pull-".concat(v),null!=v),Le(n,"fa-swap-opacity",t.swapOpacity),n);return Object.keys(x).map((function(t){return x[t]?t:null})).filter((function(t){return t}))}(e)),Re((i||"").split(" ")))),d=Ue("transform","string"==typeof e.transform?ve.transform(e.transform):e.transform),m=Ue("mask",_e(r)),p=xe(f,Ie(Ie(Ie(Ie({},u),d),m),{},{symbol:o,title:s,titleId:c,maskId:l}));if(!p)return function(){var t;!We&&console&&"function"==typeof console.error&&(t=console).error.apply(t,arguments)}("Could not find icon",f),null;var h=p.abstract,g={ref:n};return Object.keys(e).forEach((function(t){He.hasOwnProperty(t)||(g[t]=e[t])})),Xe(h[0],g)}));Be.displayName="FontAwesomeIcon",Be.propTypes={beat:ze.bool,border:ze.bool,beatFade:ze.bool,bounce:ze.bool,className:ze.string,fade:ze.bool,flash:ze.bool,mask:ze.oneOfType([ze.object,ze.array,ze.string]),maskId:ze.string,fixedWidth:ze.bool,inverse:ze.bool,flip:ze.oneOf([!0,!1,"horizontal","vertical","both"]),icon:ze.oneOfType([ze.object,ze.array,ze.string]),listItem:ze.bool,pull:ze.oneOf(["right","left"]),pulse:ze.bool,rotation:ze.oneOf([0,90,180,270]),shake:ze.bool,size:ze.oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:ze.bool,spinPulse:ze.bool,spinReverse:ze.bool,symbol:ze.oneOfType([ze.bool,ze.string]),title:ze.string,titleId:ze.string,transform:ze.oneOfType([ze.string,ze.object]),swapOpacity:ze.bool};var Xe=function t(n,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof e)return e;var r=(e.children||[]).map((function(e){return t(n,e)})),o=Object.keys(e.attributes||{}).reduce((function(t,n){var a=e.attributes[n];switch(n){case"class":t.attrs.className=a,delete e.attributes.class;break;case"style":t.attrs.style=a.split(";").map((function(t){return t.trim()})).filter((function(t){return t})).reduce((function(t,n){var e,a=n.indexOf(":"),r=Te(n.slice(0,a)),o=n.slice(a+1).trim();return r.startsWith("webkit")?t[(e=r,e.charAt(0).toUpperCase()+e.slice(1))]=o:t[r]=o,t}),{});break;default:0===n.indexOf("aria-")||0===n.indexOf("data-")?t.attrs[n.toLowerCase()]=a:t.attrs[Te(n)]=a}return t}),{attrs:{}}),i=a.style,s=void 0===i?{}:i,c=Fe(a,Ye);return o.attrs.style=Ie(Ie({},o.attrs.style),s),n.apply(void 0,[e.tag,Ie(Ie({},o.attrs),c)].concat(Re(r)))}.bind(null,n.createElement);export{Be as F,ze as P,c as a,s as b,l as c,e as d,a as e,i as f,o as g,r as h,ye as l};
