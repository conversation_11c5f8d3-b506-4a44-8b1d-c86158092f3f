import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '../common';
import { Store, ShoppingBag, Menu, X, User, ChevronDown, Settings, MapPin, Package, LogOut, Heart } from 'lucide-react';
import { useCart } from '../../context/CartContext';
import { useAuth } from '../../context/AuthContext';
import { motion, AnimatePresence } from 'motion/react';

// Component UserDropdown
const UserDropdown = ({ user, onLogout, getDisplayName, getAvatarUrl }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = () => {
    onLogout();
    setIsOpen(false);
    navigate('/');
  };

  const menuItems = [
    { icon: Settings, label: 'Tài Khoản', path: '/account' },
    { icon: MapPin, label: 'Địa Chỉ', path: '/addresses' },
    { icon: Package, label: 'Đơn Hàng', path: '/orders' },
    { icon: Heart, label: 'Yêu Thích', path: '/wishlist' },
  ];

  return (
    <div className="relative" ref={dropdownRef}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-50 transition-colors"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-dexin-light">
          <img
            src={getAvatarUrl()}
            alt={getDisplayName()}
            className="w-full h-full object-cover"
            onError={(e) => {
              e.target.src = user?.gender === 'female'
                ? 'https://via.placeholder.com/32/FE7CAB/FFFFFF?text=F'
                : 'https://via.placeholder.com/32/B90E56/FFFFFF?text=M';
            }}
          />
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-100 py-2 z-50"
          >
            {/* User Info */}
            <div className="px-4 py-3 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-dexin-light">
                  <img
                    src={getAvatarUrl()}
                    alt={getDisplayName()}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = user?.gender === 'female'
                        ? 'https://via.placeholder.com/40/FE7CAB/FFFFFF?text=F'
                        : 'https://via.placeholder.com/40/B90E56/FFFFFF?text=M';
                    }}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {getDisplayName()}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    @{user.userName}
                  </p>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-1">
              {menuItems.map((item, index) => (
                <Link
                  key={index}
                  to={item.path}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-dexin-primary transition-colors"
                >
                  <item.icon className="w-4 h-4" />
                  <span className="text-sm">{item.label}</span>
                </Link>
              ))}
            </div>

            {/* Logout */}
            <div className="border-t border-gray-100 py-1">
              <button
                onClick={handleLogout}
                className="flex items-center space-x-3 w-full px-4 py-2 text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span className="text-sm">Đăng Xuất</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [shouldFixNav, setShouldFixNav] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const { cartCount } = useCart();
  const { user, logout, isAuthenticated, getDisplayName, getAvatarUrl } = useAuth();

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Chỉ cố định navbar nếu scroll xuống quá 100px
      setShouldFixNav(currentScrollY > 100);

      // Hiện thanh màu hồng scroll xuống quá 50px
      setIsScrolled(currentScrollY > 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleMobileLogout = () => {
    logout();
    setMobileMenuOpen(false);
  };

  return (
    <nav className={`w-full z-50 transition-all duration-300 bg-white ${
      shouldFixNav
        ? 'fixed top-0'
        : 'relative'
    }`}>
      {/* Banner giảm giá */}
      <div className={`bg-pink-100 text-center transition-all duration-300 ${
        isScrolled && shouldFixNav ? 'h-0 overflow-hidden' : 'h-8 py-1.5'
      }`}>
        <div className="text-sm font-dexin">DEXIN giảm giá 10% gói tân trang nhà trong dịp ra mắt</div>
      </div>

      <div className={`border-b border-gray-100 transition-all duration-300 ${
        isScrolled && shouldFixNav ? 'shadow-md shadow-dexin-light-20' : ''
      }`}>
        <div className="container mx-auto py-3 px-4 sm:px-6 flex justify-between items-center h-auto sm:h-16 md:h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/">
              <img src="/images/logo-dexin-final.png" alt="DEXIN" className={`transition-all duration-300 ${
                isScrolled && shouldFixNav ? 'h-7 sm:h-8' : 'h-8 sm:h-9'
              }`} />
            </Link>
          </div>

          {/* Menu chính - Desktop */}
          <div className="hidden lg:flex items-center space-x-6 xl:space-x-12">
            <Link to="/phac-y" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Phác ý</Link>
            <Link to="/mo-loi" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Mở lối</Link>
            <Link to="/ngo-loi" className="px-3 py-6 pb-4 text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all font-bold relative">Ngõ lời</Link>
            <Link to="/chuyen-nha" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Chuyện nhà</Link>
            <Link to="/chung-nhip" className="px-3 py-6 pb-4 text-gray-800 hover:text-dexin-light hover:border-b-2 hover:border-dexin-primary font-medium text-base xl:text-xl transition-all relative">Chung nhịp</Link>
          </div>

          {/* Buttons - Desktop */}
          <div className="hidden md:flex items-center space-x-3 lg:space-x-6">
            {isAuthenticated ? (
              // Hiển thị khi đã đăng nhập
              <>
                <Link to="/store" className="ml-2 hover:text-dexin-light transition-colors">
                  <Store className="h-5 w-5 lg:h-6 lg:w-6" />
                </Link>
                <Link to="/cart" className="ml-2 hover:text-dexin-light transition-colors relative">
                  <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                      {cartCount > 99 ? '99+' : cartCount}
                    </span>
                  )}
                </Link>
                <UserDropdown
                  user={user}
                  onLogout={logout}
                  getDisplayName={getDisplayName}
                  getAvatarUrl={getAvatarUrl}
                />
              </>
            ) : (
              // Hiển thị khi chưa đăng nhập
              <>
                <Link to="/login" className="ml-2">
                  <Button variant="outline" size="sm" className="lg:text-base">
                    Về nhà
                  </Button>
                </Link>
                <Link to="/signup" className="ml-2">
                  <Button variant="primary" size="sm" className="lg:text-base">
                    Gõ cửa
                  </Button>
                </Link>
                <Link to="/store" className="ml-2 hover:text-dexin-light transition-colors">
                  <Store className="h-5 w-5 lg:h-6 lg:w-6" />
                </Link>
                <Link to="/cart" className="ml-2 hover:text-dexin-light transition-colors relative">
                  <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
                  {cartCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                      {cartCount > 99 ? '99+' : cartCount}
                    </span>
                  )}
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden items-center">
            <button
              onClick={toggleMobileMenu}
              className="p-2 rounded-md text-gray-600 hover:text-dexin-primary focus:outline-none"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white py-4 px-6 shadow-lg border-t border-gray-100 animate-fadeIn">
            <div className="flex flex-col space-y-4">
              <Link to="/phac-y" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Phác ý</Link>
              <Link to="/mo-loi" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Mở lối</Link>
              <Link to="/ngo-loi" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Ngõ lời</Link>
              <Link to="/chuyen-nha" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Chuyện nhà</Link>
              <Link to="/chung-nhip" className="text-gray-800 hover:text-dexin-primary font-medium py-2" onClick={() => setMobileMenuOpen(false)}>Chung nhịp</Link>

              {isAuthenticated ? (
                // Mobile menu khi đã đăng nhập
                <div className="pt-4 border-t border-gray-100">
                  {/* User info */}
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-dexin-light">
                      <img
                        src={getAvatarUrl()}
                        alt={getDisplayName()}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.src = user?.gender === 'female'
                            ? 'https://via.placeholder.com/40/FE7CAB/FFFFFF?text=F'
                            : 'https://via.placeholder.com/40/B90E56/FFFFFF?text=M';
                        }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {getDisplayName()}
                      </p>
                      <p className="text-xs text-gray-500 truncate">
                        @{user.userName}
                      </p>
                    </div>
                  </div>

                  {/* Mobile user menu items */}
                  <div className="space-y-2 mb-4">
                    <Link to="/account" className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary" onClick={() => setMobileMenuOpen(false)}>
                      <Settings className="w-4 h-4" />
                      <span>Tài Khoản</span>
                    </Link>
                    <Link to="/addresses" className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary" onClick={() => setMobileMenuOpen(false)}>
                      <MapPin className="w-4 h-4" />
                      <span>Địa Chỉ</span>
                    </Link>
                    <Link to="/orders" className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary" onClick={() => setMobileMenuOpen(false)}>
                      <Package className="w-4 h-4" />
                      <span>Đơn Hàng</span>
                    </Link>
                    <Link to="/wishlist" className="flex items-center space-x-3 py-2 text-gray-700 hover:text-dexin-primary" onClick={() => setMobileMenuOpen(false)}>
                      <Heart className="w-4 h-4" />
                      <span>Yêu Thích</span>
                    </Link>
                  </div>

                  <div className="flex items-center space-x-4 pt-2 border-t border-gray-100">
                    <Link to="/store" className="hover:text-dexin-light transition-colors" onClick={() => setMobileMenuOpen(false)}>
                      <Store className="h-5 w-5" />
                    </Link>
                    <Link to="/cart" className="hover:text-dexin-light transition-colors relative" onClick={() => setMobileMenuOpen(false)}>
                      <ShoppingBag className="h-5 w-5" />
                      {cartCount > 0 && (
                        <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                          {cartCount > 9 ? '9+' : cartCount}
                        </span>
                      )}
                    </Link>
                    <button
                      onClick={handleMobileLogout}
                      className="flex items-center space-x-2 text-red-600 hover:text-red-700 ml-auto"
                    >
                      <LogOut className="w-4 h-4" />
                      <span className="text-sm">Đăng xuất</span>
                    </button>
                  </div>
                </div>
              ) : (
                // Mobile menu khi chưa đăng nhập
                <div className="flex items-center space-x-4 pt-2 border-t border-gray-100">
                  <Link to="/login" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="outline" size="sm" className="border-dexin-light text-dexin-light">
                      Về nhà
                    </Button>
                  </Link>
                  <Link to="/signup" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="primary" size="sm">
                      Gõ cửa
                    </Button>
                  </Link>
                  <Link to="/store" className="ml-auto hover:text-dexin-light transition-colors" onClick={() => setMobileMenuOpen(false)}>
                    <Store className="h-5 w-5" />
                  </Link>
                  <Link to="/cart" className="hover:text-dexin-light transition-colors relative" onClick={() => setMobileMenuOpen(false)}>
                    <ShoppingBag className="h-5 w-5" />
                    {cartCount > 0 && (
                      <span className="absolute -top-1 -right-1 bg-dexin-primary text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                        {cartCount > 9 ? '9+' : cartCount}
                      </span>
                    )}
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;