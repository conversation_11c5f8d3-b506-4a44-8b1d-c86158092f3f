# Khắc phục lỗi Production - DEXIN Website

## Tổng quan các lỗi đã khắc phục

### 1. Lỗi MIME Type JavaScript Module
**Lỗi:** `Failed to load module script: Expected a JavaScript module script but the server responded with a MIME type of "text/html"`

**Nguyên nhân:** Server không cấu hình đúng MIME type cho JavaScript modules

**Giải pháp:**
- Tạo file `.htaccess` với cấu hình MIME type đúng
- Tạo file `web.config` cho IIS server
- Tạo file `_redirects` cho Netlify

### 2. Lỗi Manifest Icon
**Lỗi:** `Error while trying to use the following icon from the Manifest: https://dexin.io.vn/images/Logo%20Mini.png`

**Nguyên nhân:** URL encoding trong manifest.json không khớp với tên file thực tế

**Giải pháp:**
- Sửa đường dẫn trong `manifest.json` từ `Logo%20Mini.png` thành `Logo Mini.png`
- Sửa đường dẫn screenshot từ `hometile%20hero.jpg` thành `hometile hero.jpg`

## Files đã được tạo/sửa

### 1. Server Configuration Files
- `build/.htaccess` - Cấu hình Apache server
- `build/web.config` - Cấu hình IIS server  
- `build/_redirects` - Cấu hình Netlify

### 2. Manifest Files
- `build/manifest.json` - Đã sửa đường dẫn icon
- `public/manifest.json` - Đã sửa đường dẫn icon (source)

### 3. Build Configuration
- `vite.config.js` - Thêm base path configuration
- `rebuild-production.js` - Script tự động rebuild

## Hướng dẫn Deploy

### Bước 1: Rebuild Project
```bash
# Sử dụng script tự động
node rebuild-production.js

# Hoặc build thủ công
npm run build
```

### Bước 2: Upload lên Server
Upload toàn bộ thư mục `build/` lên production server

### Bước 3: Cấu hình Server

#### Apache Server
- File `.htaccess` đã được tạo tự động trong thư mục build
- Đảm bảo Apache có module `mod_rewrite` và `mod_headers` enabled

#### IIS Server
- Sử dụng file `web.config` đã được tạo
- Đảm bảo IIS có URL Rewrite module installed

#### Netlify
- File `_redirects` đã được tạo tự động
- Netlify sẽ tự động nhận diện và áp dụng

### Bước 4: Kiểm tra
1. Truy cập https://dexin.io.vn
2. Mở Developer Tools > Console
3. Kiểm tra không còn lỗi MIME type và manifest icon

## Các tính năng đã thêm

### Security Headers
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

### Performance Optimization
- Gzip compression cho static assets
- Cache headers cho long-term caching
- Proper MIME types cho tất cả file types

### SPA Routing
- Redirect tất cả routes về index.html
- Preserve API routes (không redirect /api/*)

## Troubleshooting

### Nếu vẫn gặp lỗi MIME type:
1. Kiểm tra server có hỗ trợ .htaccess không
2. Kiểm tra module mod_rewrite và mod_headers đã enable
3. Thử sử dụng web.config cho IIS server

### Nếu vẫn gặp lỗi manifest icon:
1. Kiểm tra file icon có tồn tại trong thư mục build/images/
2. Kiểm tra đường dẫn trong manifest.json không có URL encoding
3. Clear browser cache và thử lại

### Nếu SPA routing không hoạt động:
1. Kiểm tra server configuration file đã được upload
2. Kiểm tra server có hỗ trợ URL rewrite
3. Thử truy cập trực tiếp một route để test

## Liên hệ
Nếu gặp vấn đề, vui lòng liên hệ team development với thông tin:
- URL gặp lỗi
- Browser và version
- Screenshot console errors
