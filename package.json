{"name": "dexin", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@google/generative-ai": "^0.24.0", "@headlessui/react": "^2.2.4", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.5.0", "axios": "^1.9.0", "compressorjs": "^1.2.1", "emoji-picker-react": "^4.12.2", "jspdf": "^3.0.1", "konva": "^9.3.20", "lucide-react": "^0.484.0", "motion": "^12.6.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-konva": "^19.0.3", "react-markdown": "^10.1.0", "react-range": "^1.10.0", "react-router-dom": "^7.4.0", "react-toastify": "^11.0.5", "remark-gfm": "^4.0.1", "styled-components": "^6.1.17", "use-image": "^1.1.1", "web-vitals": "^5.0.1"}, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "test": "vitest", "dev:prod": "cross-env NODE_ENV=production vite", "dev:staging": "cross-env NODE_ENV=staging vite", "build:prod": "cross-env NODE_ENV=production vite build", "build:staging": "cross-env NODE_ENV=staging vite build", "build:analyze": "vite build --mode analyze", "preview": "vite preview"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "postcss": "^8.5.3", "postcss-import": "^16.0.0", "postcss-nesting": "^12.0.1", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.4.1", "terser": "^5.39.2", "vite": "^6.3.5", "vitest": "^3.1.4"}}