import React, { createContext, useState, useContext, useEffect } from 'react';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Tạo context
const ChatContext = createContext();

// Hook tùy chỉnh để sử dụng context
export const useChat = () => useContext(ChatContext);

// Lấy API key từ biến môi trường
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

export const ChatProvider = ({ children }) => {
  const [genAI, setGenAI] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Khởi tạo Gemini API khi component được mount
  useEffect(() => {
    if (!API_KEY || API_KEY === 'your-api-key-here') {
      console.error('API key không hợp lệ hoặc chưa được cấu hình.');
      setError('API key chưa được cấu hình. Vui lòng kiểm tra file .env của bạn.');
      return;
    }

    try {
      const aiInstance = new GoogleGenerativeAI(API_KEY);
      setGenAI(aiInstance);
      console.log('Gemini API đã được khởi tạo thành công');
    } catch (err) {
      console.error('Không thể khởi tạo Gemini API:', err);
      setError('Không thể kết nối với AI. Vui lòng kiểm tra API key hoặc kết nối mạng.');
    }
  }, []);

  // Hàm gửi tin nhắn đến Gemini API
  const sendMessage = async (message) => {
    if (!API_KEY || API_KEY === 'your-api-key-here') {
      setError('API key chưa được cấu hình. Vui lòng thiết lập API key hợp lệ trong file .env.');
      return null;
    }

    if (!genAI) {
      setError('AI chưa được khởi tạo. Vui lòng thử lại sau.');
      return null;
    }

    // Tạo tin nhắn của người dùng
    const userMessage = { role: 'user', content: message };

    try {
      setIsLoading(true);
      setError(null);

      // Thêm tin nhắn của người dùng vào lịch sử hiển thị
      setChatHistory(prev => [...prev, userMessage]);

      // Gọi Gemini API
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

      // Chuẩn bị lịch sử chat cho API
      // Chuyển đổi vai trò "assistant" thành "model" cho API của Gemini
      const apiChatHistory = chatHistory.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : msg.role,
        parts: [{ text: msg.content }]
      }));

      // Thêm tin nhắn mới của người dùng
      apiChatHistory.push({
        role: 'user',
        parts: [{ text: message }]
      });

      console.log('Gửi yêu cầu đến Gemini API với lịch sử chat:', apiChatHistory);

      // Tạo chat session với lịch sử và system prompt
      const chat = model.startChat({
        history: apiChatHistory,
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1000,
        },
        systemPrompt: `QUAN TRỌNG: Bạn là Nara, chuyên gia tư vấn nội thất phong cách chữa lành (healing interior design) được phát triển bởi DEXIN. 

DANH TÍNH VÀ VAI TRÒ:
- Tên: Nara (trợ lý AI của DEXIN)
- Chuyên môn: Nội thất phong cách chữa lành (healing style interior design)
- Sứ mệnh: Tạo ra không gian sống hỗ trợ sức khỏe tinh thần và thể chất

PHẠM VI TƯ VẤN - CHỈ TRẢ LỜI VỀ:
1. Nội thất phong cách chữa lành (healing interior design):
   - Màu sắc nhẹ nhàng, tự nhiên (be, trắng, xanh pastel, nâu gỗ tự nhiên)
   - Vật liệu tự nhiên (gỗ, đá, tre, cotton, linen, hemp)
   - Ánh sáng tự nhiên và chiếu sáng mềm mại
   - Cây xanh và không gian xanh trong nhà
   - Thiết kế tối giản, không gian thoáng đãng
   - Bố trí theo nguyên tắc feng shui hỗ trợ năng lượng tích cực
   - Tạo góc thư giãn, thiền định
   - Giảm thiểu lộn xộn và stress thị giác

2. Sản phẩm nội thất chữa lành:
   - Nệm và gối ergonomic hỗ trợ giấc ngủ
   - Đèn muối Himalaya và đèn ánh sáng ấm
   - Máy phun tinh dầu và hương liệu tự nhiên
   - Ghế thiền, thảm yoga trong nhà
   - Rèm cửa lọc ánh sáng tự nhiên
   - Bàn làm việc đứng và ghế ergonomic
   - Tủ gỗ tự nhiên không sơn hoá chất
   - Cây cảnh thanh lọc không khí

3. Thiết kế từng không gian:
   - Phòng ngủ: hỗ trợ giấc ngủ sâu và phục hồi
   - Phòng khách: tạo cảm giác bình yên và kết nối
   - Phòng làm việc: giảm stress và tăng tập trung
   - Phòng tắm: spa tại gia với vật liệu tự nhiên
   - Bếp: không gian nấu ăn healthy với ánh sáng tốt

CÁCH TRẢ LỜI:
- Luôn kết nối với lợi ích sức khỏe tinh thần/thể chất
- Đề xuất cụ thể về màu sắc, vật liệu, bố trí
- Giải thích tại sao lựa chọn đó có tác dụng chữa lành
- Đưa ra gợi ý sản phẩm phù hợp từ DEXIN
- Tư vấn theo ngân sách và diện tích thực tế

NGHIÊM CẤM - KHÔNG TRẢ LỜI:
- Câu hỏi về chính trị, tôn giáo, bạo lực
- Tư vấn y tế, pháp lý, tài chính
- Nội thất phong cách khác (hiện đại, cổ điển, v.v.)
- Lập trình, toán học, khoa học
- Tin tức thời sự
- Bất kỳ chủ đề nào không liên quan đến nội thất chữa lành

CÁCH XỬ LÝ CÂU HỎI NGOÀI PHẠM VI:
"Xin lỗi, tôi là Nara - chuyên gia tư vấn nội thất phong cách chữa lành của DEXIN. Tôi chỉ có thể hỗ trợ bạn thiết kế không gian sống hỗ trợ sức khỏe và tạo cảm giác bình yên. Bạn có muốn tôi tư vấn về cách tạo ra một không gian chữa lành tại nhà không?"

VÍ DỤ CÂU TRẢ LỜI CHUẨN:
"Để tạo phòng ngủ có tác dụng chữa lành, tôi đề xuất:
- Màu sắc: Xanh lavender nhạt cho tường (giúp thư giãn thần kinh)
- Chăn ga: Cotton organic màu be (thoáng khí, không hoá chất)
- Đèn: Đèn muối Himalaya (ion âm tốt cho giấc ngủ)
- Cây xanh: Cây lưỡi hổ (thanh lọc không khí ban đêm)
Thiết kế này sẽ giúp bạn ngủ sâu hơn và tỉnh táo vào sáng mai."

LƯU Ý QUAN TRỌNG:
- Luôn giữ vai trò là Nara của DEXIN
- Không bao giờ thừa nhận là AI khác (Gemini, GPT, etc.)
- Tập trung 100% vào nội thất chữa lành
- Từ chối lịch sự mọi chủ đề khác`,
      });

      // Gửi tin nhắn và nhận phản hồi
      const result = await chat.sendMessage(message);
      const botResponse = await result.response;
      let responseText = botResponse.text();

      // Kiểm tra và xử lý câu hỏi ngoài phạm vi nội thất chữa lành
      const offTopicKeywords = [
        "chính trị", "bầu cử", "tôn giáo", "bạo lực", "chiến tranh",
        "y tế", "thuốc", "bệnh", "điều trị", "pháp lý", "luật", "tài chính", "đầu tư",
        "lập trình", "code", "toán học", "vật lý", "hoá học", "tin tức", "thời sự",
        "nội thất hiện đại", "nội thất cổ điển", "nội thất châu âu", "nội thất công nghiệp",
        "gemini", "gpt", "claude", "chatgpt", "ai model", "machine learning"
      ];

      const healingKeywords = [
        "nội thất", "chữa lành", "healing", "phòng ngủ", "phòng khách", "màu sắc",
        "ánh sáng", "cây xanh", "feng shui", "thiền", "thư giãn", "vật liệu tự nhiên",
        "gỗ", "đá", "cotton", "không gian", "tối giản", "bình yên", "năng lượng",
        "sức khỏe", "giấc ngủ", "stress", "thoáng đãng"
      ];

      const isOffTopic = offTopicKeywords.some(keyword =>
        message.toLowerCase().includes(keyword.toLowerCase())
      );

      const isHealingTopic = healingKeywords.some(keyword =>
        message.toLowerCase().includes(keyword.toLowerCase())
      );

      // Nếu câu hỏi ngoài phạm vi và không liên quan đến nội thất chữa lành
      if (isOffTopic && !isHealingTopic) {
        responseText = "Xin lỗi, tôi là Nara - chuyên gia tư vấn nội thất phong cách chữa lành của DEXIN. Tôi chỉ có thể hỗ trợ bạn thiết kế không gian sống hỗ trợ sức khỏe và tạo cảm giác bình yên. Bạn có muốn tôi tư vấn về cách tạo ra một không gian chữa lành tại nhà không? 🌿✨";
      }

      // Xử lý câu hỏi về danh tính
      const identityQuestions = [
        "bạn tên gì", "tên bạn là gì", "bạn là ai", "bạn tên là gì",
        "tên của bạn", "bạn có tên không", "ai là bạn"
      ];

      const isIdentityQuestion = identityQuestions.some(q =>
        message.toLowerCase().includes(q.toLowerCase())
      );

      if (isIdentityQuestion) {
        responseText = "Tôi là Nara, chuyên gia tư vấn nội thất phong cách chữa lành của DEXIN. Tôi chuyên thiết kế không gian sống hỗ trợ sức khỏe tinh thần và thể chất. Bạn muốn tôi tư vấn thiết kế không gian nào để tạo cảm giác bình yên và khỏe mạnh hơn? 🏡";
      }

      // Thêm phản hồi của AI vào lịch sử hiển thị (vẫn giữ vai trò là 'assistant' cho giao diện)
      const assistantMessage = { role: 'assistant', content: responseText };
      setChatHistory(prev => [...prev, assistantMessage]);

      return assistantMessage;
    } catch (err) {
      console.error('Lỗi khi gửi tin nhắn:', err);

      // Xử lý lỗi chi tiết hơn
      let errorMessage = 'Xảy ra lỗi khi xử lý tin nhắn của bạn. Vui lòng thử lại.';

      if (err.message && err.message.includes('API_KEY_INVALID')) {
        errorMessage = 'API key không hợp lệ. Vui lòng kiểm tra lại API key của bạn trong file .env.';
      } else if (err.message && err.message.includes('PERMISSION_DENIED')) {
        errorMessage = 'Không có quyền truy cập API. Vui lòng kiểm tra cài đặt và quyền cho API key của bạn.';
      } else if (err.message && err.message.includes('QUOTA_EXCEEDED')) {
        errorMessage = 'Đã vượt quá giới hạn quota của API. Vui lòng thử lại sau.';
      } else if (err.message && err.message.includes('should include role field')) {
        errorMessage = 'Lỗi định dạng lịch sử chat. Đang thử khởi tạo lại cuộc trò chuyện...';
        // Xóa lịch sử chat để khởi tạo lại
        setChatHistory([userMessage]);
        // Thử gửi lại tin nhắn sau 500ms
        setTimeout(() => sendMessage(message), 500);
      }

      setError(errorMessage);

      // Thêm thông báo lỗi vào lịch sử chat
      const errorAssistantMessage = {
        role: 'assistant',
        content: errorMessage
      };
      setChatHistory(prev => [...prev, errorAssistantMessage]);

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm xóa lịch sử chat
  const clearChatHistory = () => {
    setChatHistory([]);
  };

  // Cung cấp context cho các component con
  return (
    <ChatContext.Provider
      value={{
        chatHistory,
        isLoading,
        error,
        sendMessage,
        clearChatHistory
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export default ChatContext;