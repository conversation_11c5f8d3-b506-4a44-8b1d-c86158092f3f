import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { authService } from '../services/authService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Constants
  const STORAGE_KEY = 'dexin_user';

  // Helper function để lưu user vào localStorage
  const saveUserToStorage = useCallback((userData) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(userData));
    } catch (error) {
      console.error('Error saving user to localStorage:', error);
    }
  }, []);

  // Helper function để xóa user khỏi localStorage  
  const removeUserFromStorage = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error removing user from localStorage:', error);
    }
  }, []);

  // Khởi tạo - kiểm tra localStorage
  useEffect(() => {
    const initAuth = async () => {
      try {
        setError(null);
        const savedUser = localStorage.getItem(STORAGE_KEY);
        
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          
          // Validate user data structure
          if (userData && userData.id && userData.email) {
            setUser(userData);
          } else {
            console.warn('Invalid user data in localStorage, removing...');
            removeUserFromStorage();
          }
        }
      } catch (error) {
        console.error('Error loading user from localStorage:', error);
        setError('Có lỗi khi tải thông tin người dùng');
        removeUserFromStorage();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, [removeUserFromStorage]);

  // Đăng nhập
  const login = useCallback(async (loginData) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input
      if (!loginData?.username?.trim() || !loginData?.password) {
        return { 
          success: false, 
          message: 'Vui lòng nhập đầy đủ thông tin đăng nhập' 
        };
      }

      const result = await authService.login({
        identifier: loginData.username.trim(),
        password: loginData.password
      });

      if (result.success && result.user) {
        setUser(result.user);
        saveUserToStorage(result.user);
        setError(null);
        return { success: true, message: result.message };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = 'Có lỗi xảy ra khi đăng nhập';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [saveUserToStorage]);

  // Đăng ký
  const register = useCallback(async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      // Validate input
      if (!userData?.firstName?.trim() || !userData?.lastName?.trim()) {
        return { success: false, message: 'Vui lòng nhập đầy đủ họ tên' };
      }
      if (!userData?.email?.trim() || !userData?.username?.trim()) {
        return { success: false, message: 'Vui lòng nhập email và tên người dùng' };
      }
      if (!userData?.password || userData.password.length < 6) {
        return { success: false, message: 'Mật khẩu phải có ít nhất 6 ký tự' };
      }

      // Tạm thời disable check exists để tránh multiple API calls
      // Để server handle duplicate validation
      const result = await authService.register(userData);

      if (result.success && result.user) {
        setUser(result.user);
        saveUserToStorage(result.user);
        setError(null);
        return { success: true, message: result.message };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Register error:', error);
      const errorMessage = 'Có lỗi xảy ra khi đăng ký';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [saveUserToStorage]);

  // Đăng xuất
  const logout = useCallback(() => {
    setUser(null);
    setError(null);
    removeUserFromStorage();
    
    // Clear auth service cache để đảm bảo bảo mật
    try {
      if (authService.clearCache) {
        authService.clearCache();
      }
    } catch (error) {
      console.warn('Error clearing auth cache:', error);
    }
  }, [removeUserFromStorage]);

  // Cập nhật thông tin user với API
  const updateUser = useCallback(async (updatedUserData) => {
    try {
      if (!user?.id) {
        const errorMessage = 'Không tìm thấy thông tin người dùng';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }

      setLoading(true);
      setError(null);

      // Gọi API để cập nhật trên server
      const result = await authService.updateUser(user.id, {
        ...user,
        ...updatedUserData
      });

      if (result.success && result.user) {
        // Cập nhật state và localStorage với dữ liệu mới từ server
        setUser(result.user);
        saveUserToStorage(result.user);
        setError(null);
        return { success: true, message: result.message };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Update user error:', error);
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật thông tin';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user, saveUserToStorage]);

  // Cập nhật thông tin user chỉ local (không gọi API)
  const updateUserLocal = useCallback((updatedUserData) => {
    if (!user) return;
    
    const updatedUser = { ...user, ...updatedUserData };
    setUser(updatedUser);
    saveUserToStorage(updatedUser);
    setError(null);
  }, [user, saveUserToStorage]);

  // Làm mới thông tin user từ server
  const refreshUser = useCallback(async () => {
    try {
      if (!user?.id) {
        return { success: false, message: 'Không tìm thấy thông tin người dùng' };
      }

      setLoading(true);
      setError(null);
      
      const result = await authService.getUserById(user.id);

      if (result.success && result.user) {
        setUser(result.user);
        saveUserToStorage(result.user);
        setError(null);
        return { success: true, user: result.user };
      } else {
        setError(result.message);
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      const errorMessage = 'Có lỗi xảy ra khi làm mới thông tin người dùng';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user?.id, saveUserToStorage]);

  // Thay đổi mật khẩu
  const changePassword = useCallback(async (currentPassword, newPassword) => {
    try {
      if (!user?.id) {
        const errorMessage = 'Không tìm thấy thông tin người dùng';
        setError(errorMessage);
        return { success: false, message: errorMessage };
      }

      if (!currentPassword?.trim() || !newPassword?.trim()) {
        return { success: false, message: 'Vui lòng nhập đầy đủ mật khẩu' };
      }

      if (newPassword.length < 6) {
        return { success: false, message: 'Mật khẩu mới phải có ít nhất 6 ký tự' };
      }

      setLoading(true);
      setError(null);
      
      const result = await authService.changePassword(user.id, currentPassword, newPassword);
      
      if (!result.success) {
        setError(result.message);
      }
      
      return result;
    } catch (error) {
      console.error('Change password error:', error);
      const errorMessage = 'Có lỗi xảy ra khi đổi mật khẩu';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Computed values với useMemo để tối ưu performance
  const authValues = useMemo(() => ({
    // State
    user,
    loading,
    error,
    
    // Auth methods
    login,
    register,
    logout,
    updateUser,
    updateUserLocal,
    refreshUser,
    changePassword,
    clearError,
    
    // Computed helpers
    isAuthenticated: Boolean(user),
    getDisplayName: () => {
      if (!user) return '';
      return `${user.firstName || ''} ${user.lastName || ''}`.trim() || 
             user.userName || 
             user.email || 
             'Người dùng';
    },
    getAvatarUrl: () => {
      if (!user || !user.avatar || user.avatar === 'default_avatar.png') {
        // Return default avatar based on gender
        return user?.gender === 'female' 
          ? '/images/default-avatar-female.png' 
          : '/images/default-avatar-male.png';
      }
      return user.avatar;
    },
    
    // Role helpers
    hasRole: (role) => user?.role === role,
    hasAnyRole: (roles) => Array.isArray(roles) && roles.includes(user?.role),
    isAdmin: () => user?.role === 'admin',
    isUser: () => user?.role === 'user'
  }), [
    user, 
    loading, 
    error, 
    login, 
    register, 
    logout, 
    updateUser, 
    updateUserLocal, 
    refreshUser, 
    changePassword, 
    clearError
  ]);

  return (
    <AuthContext.Provider value={authValues}>
      {children}
    </AuthContext.Provider>
  );
}; 